# StudyCalendar 学习日历组件

一个美观的学习日历组件，用于展示学习统计数据和打卡功能。

## 功能特性

- 📊 展示学习统计数据（坚持天数、做题总数、正确率）
- ✅ 打卡功能，支持已打卡状态显示
- 🎨 渐变背景设计，美观的UI界面
- 📱 响应式设计，适配不同屏幕尺寸
- 🔧 高度可定制，支持自定义数据

## 使用方法

### 1. 导入组件

```vue
<script>
import StudyCalendar from '@/components/study-calendar.vue'

export default {
  components: {
    StudyCalendar
  }
}
</script>
```

### 2. 在模板中使用

```vue
<template>
  <view>
    <!-- 基础用法 -->
    <study-calendar @check-in="handleCheckIn" />
    
    <!-- 自定义数据 -->
    <study-calendar 
      :persist-days="45"
      :total-questions="1200"
      :accuracy-rate="85"
      :is-checked-in="false"
      @check-in="handleCheckIn"
    />
  </view>
</template>
```

### 3. 处理事件

```vue
<script>
export default {
  methods: {
    handleCheckIn() {
      console.log('用户点击了打卡按钮')
      // 调用打卡API
      this.checkInAPI()
    },
    
    async checkInAPI() {
      try {
        // 发送打卡请求
        const result = await this.$api.checkIn()
        if (result.success) {
          uni.showToast({
            title: '打卡成功！',
            icon: 'success'
          })
        }
      } catch (error) {
        uni.showToast({
          title: '打卡失败，请重试',
          icon: 'error'
        })
      }
    }
  }
}
</script>
```

## Props 属性

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| persistDays | Number/String | 31 | 坚持天数 |
| totalQuestions | Number/String | 700 | 做题总数 |
| accuracyRate | Number/String | 12 | 正确率（百分比，不需要%符号） |
| isCheckedIn | Boolean | true | 是否已打卡 |

## Events 事件

| 事件名 | 说明 | 回调参数 |
|--------|------|----------|
| check-in | 用户点击打卡按钮时触发 | - |

## 样式定制

组件使用了SCSS，你可以通过以下方式自定义样式：

```scss
// 自定义主色调
.study-calendar {
  --primary-color: #2E68FF;
  --success-color: #40B983;
  --warning-color: #FF8A00;
  --error-color: #FF6B6B;
}
```

## 设计说明

- 背景使用渐变色，营造清新的视觉效果
- 数字使用大字体突出显示，便于快速阅读
- 打卡按钮采用圆角设计，提供良好的交互体验
- 已打卡状态使用绿色主题，未打卡使用橙色主题
- 添加了装饰性背景元素，增强视觉层次

## 注意事项

1. 确保项目中已安装并配置了SCSS支持
2. 组件依赖uni-app框架，请在uni-app项目中使用
3. 建议在使用前测试不同屏幕尺寸下的显示效果
4. 打卡功能需要配合后端API实现数据持久化

## 兼容性

- ✅ 微信小程序
- ✅ H5
- ✅ App
- ✅ 支付宝小程序
- ✅ 百度小程序
- ✅ 字节跳动小程序
