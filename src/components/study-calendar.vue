<template>
  <view class="study-calendar">
    <!-- 右上角装饰图片 -->
    <image
      class="decoration-image"
      src="https://yakaixin.oss-cn-beijing.aliyuncs.com/study-card-color.png"
      mode="aspectFit"
    />

    <view class="calendar-header">
      <view class="title-section">
        <text class="calendar-title">学习日历</text>
        <view class="check-in-status" v-if="isCheckedIn">
          <image
            class="check-icon"
            src="https://yakaixin.oss-cn-beijing.aliyuncs.com/study-card-zan.png"
            mode="aspectFit"
          />
          <text class="check-text">今日已打卡</text>
        </view>
      </view>
    </view>

    <view class="calendar-content">
      <view class="stats-row">
        <view class="stat-item">
          <text class="stat-number blue-number">{{ persistDays }}</text>
          <text class="stat-label">坚持天数</text>
        </view>

        <view class="stat-item">
          <text class="stat-number blue-number">{{ totalQuestions }}</text>
          <text class="stat-label">做题总数</text>
        </view>

        <view class="stat-item">
          <text class="stat-number red-number">{{ accuracyRate }}</text>
          <text class="stat-label">正确率%</text>
        </view>

        <view class="action-section">
          <view
            class="check-in-btn"
            :class="{ 'checked': isCheckedIn }"
            @click="handleCheckIn"
          >
            <text class="btn-text">打卡</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'StudyCalendar',
  props: {
    // 坚持天数
    persistDays: {
      type: [Number, String],
      default: 31
    },
    // 做题总数
    totalQuestions: {
      type: [Number, String],
      default: 700
    },
    // 正确率
    accuracyRate: {
      type: [Number, String],
      default: 12
    },
    // 是否已打卡
    isCheckedIn: {
      type: Boolean,
      default: true
    }
  },
  methods: {
    handleCheckIn() {
      if (!this.isCheckedIn) {
        this.$emit('check-in')
        uni.showToast({
          title: '打卡成功！',
          icon: 'success',
          duration: 2000
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.study-calendar {
  background: white;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 20rpx rgba(46, 104, 255, 0.1);
  position: relative;
  overflow: hidden;

  // 右上角装饰图片
  .decoration-image {
    position: absolute;
    top: 0;
    right: 0;
    z-index: 1;
    opacity: 0.8;
  }

  .calendar-header {
    margin-bottom: 32rpx;
    position: relative;
    z-index: 2;

    .title-section {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .calendar-title {
        font-size: 32rpx;
        font-weight: 600;
        color: #333;
        line-height: 1.2;
      }

      .check-in-status {
        display: flex;
        align-items: center;
        background: rgba(64, 185, 131, 0.15);
        padding: 8rpx 16rpx;
        border-radius: 20rpx;

        .check-icon {
          width: 32rpx;
          height: 32rpx;
          margin-right: 8rpx;
        }

        .check-text {
          font-size: 24rpx;
          color: #40B983;
          font-weight: 500;
        }
      }
    }
  }

  .calendar-content {
    position: relative;
    z-index: 2;

    .stats-row {
      display: flex;
      align-items: flex-end;
      justify-content: space-between;

      .stat-item {
        text-align: center;

        .stat-number {
          display: block;
          font-size: 48rpx;
          font-weight: 700;
          line-height: 1.2;
          margin-bottom: 8rpx;

          &.blue-number {
            color: #2E68FF;
          }

          &.red-number {
            color: #FF6B6B;
          }
        }

        .stat-label {
          display: block;
          font-size: 24rpx;
          color: #666;
          font-weight: 400;
          line-height: 1.2;
        }
      }

      .action-section {
        .check-in-btn {
          background: linear-gradient(135deg, #FF8A00 0%, #FFB366 100%);
          border-radius: 32rpx;
          padding: 16rpx 32rpx;
          box-shadow: 0 4rpx 16rpx rgba(255, 138, 0, 0.3);
          transition: all 0.3s ease;

          &:active {
            transform: scale(0.95);
          }

          &.checked {
            background: linear-gradient(135deg, #40B983 0%, #6BCF95 100%);
            box-shadow: 0 4rpx 16rpx rgba(64, 185, 131, 0.3);
          }

          .btn-text {
            font-size: 28rpx;
            color: #fff;
            font-weight: 600;
            line-height: 1.2;
          }
        }
      }
    }
  }
}

// 响应式适配
@media screen and (max-width: 750rpx) {
  .study-calendar {
    margin: 16rpx;
    padding: 24rpx;
    
    .calendar-header {
      margin-bottom: 24rpx;
      
      .title-section {
        .calendar-title {
          font-size: 32rpx;
        }
        
        .check-in-status {
          .check-icon {
            width: 28rpx;
            height: 28rpx;
          }
          
          .check-text {
            font-size: 22rpx;
          }
        }
      }
    }
    
    .calendar-content {
      .stats-row {
        margin-bottom: 32rpx;
        
        .stat-item {
          .stat-number {
            font-size: 40rpx;
          }
          
          .stat-label {
            font-size: 22rpx;
          }
        }
      }
      
      .action-section {
        .check-in-btn {
          padding: 14rpx 32rpx;
          
          .btn-text {
            font-size: 26rpx;
          }
        }
      }
    }
  }
}
</style>
