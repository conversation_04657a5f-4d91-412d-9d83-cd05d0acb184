<template>
  <view class="demo-page">
    <view class="demo-header">
      <text class="demo-title">学习日历组件演示</text>
    </view>
    
    <!-- 已打卡状态 - 带装饰图片和赞图标 -->
    <view class="demo-section">
      <view class="section-title">已打卡状态（带装饰图片）</view>
      <study-calendar
        :persist-days="31"
        :total-questions="700"
        :accuracy-rate="12"
        :is-checked-in="true"
        @check-in="handleCheckIn"
      />
    </view>

    <!-- 未打卡状态 -->
    <view class="demo-section">
      <view class="section-title">未打卡状态</view>
      <study-calendar
        :persist-days="30"
        :total-questions="680"
        :accuracy-rate="15"
        :is-checked-in="false"
        @check-in="handleCheckIn"
      />
    </view>

    <!-- 高正确率示例 -->
    <view class="demo-section">
      <view class="section-title">高正确率示例（已打卡）</view>
      <study-calendar
        :persist-days="45"
        :total-questions="1200"
        :accuracy-rate="85"
        :is-checked-in="true"
        @check-in="handleCheckIn"
      />
    </view>
  </view>
</template>

<script>
import StudyCalendar from './study-calendar.vue'

export default {
  name: 'StudyCalendarDemo',
  components: {
    StudyCalendar
  },
  methods: {
    handleCheckIn() {
      console.log('用户点击了打卡按钮')
      uni.showToast({
        title: '打卡成功！',
        icon: 'success',
        duration: 2000
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.demo-page {
  min-height: 100vh;
  background: #f5f6f7;
  padding: 40rpx 0;
  
  .demo-header {
    text-align: center;
    margin-bottom: 60rpx;
    
    .demo-title {
      font-size: 40rpx;
      font-weight: 600;
      color: #333;
    }
  }
  
  .demo-section {
    margin-bottom: 60rpx;
    
    .section-title {
      font-size: 28rpx;
      font-weight: 500;
      color: #666;
      margin: 0 24rpx 20rpx 24rpx;
      padding-left: 16rpx;
      border-left: 4rpx solid #2E68FF;
    }
  }
}
</style>
