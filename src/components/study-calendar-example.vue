<template>
  <view class="example-page">
    <view class="page-title">学习日历组件示例</view>
    
    <!-- 基础用法 -->
    <view class="section">
      <view class="section-title">基础用法</view>
      <study-calendar 
        @check-in="handleCheckIn"
      />
    </view>
    
    <!-- 自定义数据 -->
    <view class="section">
      <view class="section-title">自定义数据</view>
      <study-calendar 
        :persist-days="45"
        :total-questions="1200"
        :accuracy-rate="85"
        :is-checked-in="false"
        @check-in="handleCheckIn"
      />
    </view>
    
    <!-- 已打卡状态 -->
    <view class="section">
      <view class="section-title">已打卡状态</view>
      <study-calendar 
        :persist-days="7"
        :total-questions="350"
        :accuracy-rate="68"
        :is-checked-in="true"
        @check-in="handleCheckIn"
      />
    </view>
  </view>
</template>

<script>
import StudyCalendar from './study-calendar.vue'

export default {
  name: 'StudyCalendarExample',
  components: {
    StudyCalendar
  },
  methods: {
    handleCheckIn() {
      console.log('用户点击了打卡按钮')
      // 这里可以调用API进行打卡操作
      // 例如：this.checkInAPI()
    }
  }
}
</script>

<style lang="scss" scoped>
.example-page {
  min-height: 100vh;
  background: #f5f6f7;
  padding: 40rpx 0;
  
  .page-title {
    text-align: center;
    font-size: 40rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 40rpx;
  }
  
  .section {
    margin-bottom: 60rpx;
    
    .section-title {
      font-size: 32rpx;
      font-weight: 500;
      color: #666;
      margin: 0 24rpx 20rpx 24rpx;
    }
  }
}
</style>
