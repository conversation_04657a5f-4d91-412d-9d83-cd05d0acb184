<template>
  <view class="huihui_parent" :style="parentCss">
    <view class="huihui_process" :style="processCss"></view>
    <view class="huihui_left_part" :style="leftCss"></view>
    <view class="huihui_right_part" :style="rightCss"></view>
    <view class="huihui_inner_circle" :style="innerCircleCss">
      <slot></slot>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {}
  },
  props: {
    processColor: {
      type: String,
      default: '#42b983'
    },
    originalColor: {
      type: String,
      default: '#EEEEEE'
    },
    innerbgColor: {
      type: String,
      default: '#FFFFFF'
    },
    process: {
      type: Number,
      default: 0
    },
    radius: {
      type: Number,
      default: 100
    },
    barWidth: {
      type: Number,
      default: 3
    },
    startPosDegree: {
      type: Number,
      default: 0
    }
  },
  computed: {
    processCss() {
      const { radius, processColor } = this
      return {
        borderRadius: `${radius}upx`,
        height: `${radius * 2}upx`,
        width: `${radius * 2}upx`,
        backgroundColor: processColor
      }
    },
    parentCss() {
      const { radius, startPosDegree } = this
      return {
        borderRadius: `${radius}upx`,
        height: `${radius * 2}upx`,
        width: `${radius * 2}upx`,
        transform: 'rotate(' + startPosDegree + 'deg )'
      }
    },
    leftCss() {
      let { radius, originalColor, process } = this
      let leftTransformerDegree =
        (process > 50 ? ((process > 100 ? 100 : process) - 50) * 3.6 : 0) +
        'deg'
      return {
        height: `${radius * 2}upx`,
        width: `${radius}upx`,
        backgroundColor: originalColor,
        transform: 'rotate(' + leftTransformerDegree + ')',
        transformOrigin: 'right center',
        borderTopLeftRadius: `${radius}upx`,
        borderBottomLeftRadius: `${radius}upx`,
        left: 0,
        top: 0
      }
    },
    rightCss() {
      let { radius, processColor, originalColor, process } = this
      let rightTransformerDegree = (process > 50 ? 0 : process) * 3.6 + 'deg'

      return {
        height: `${radius * 2}upx`,
        width: `${radius}upx`,
        backgroundColor: process > 50 ? processColor : originalColor,
        transform: 'rotate(' + rightTransformerDegree + ')',
        transformOrigin: 'left center',
        borderTopRightRadius: `${radius}upx`,
        borderBottomRightRadius: `${radius}upx`,
        left: `${radius}upx`,
        top: 0
      }
    },
    innerCircleCss() {
      const { radius, innerbgColor, barWidth, startPosDegree } = this
      return {
        borderRadius: `${radius - barWidth}upx`,
        height: `${(radius - barWidth) * 2}upx`,
        width: `${(radius - barWidth) * 2}upx`,
        transform: 'rotate(' + (0 - startPosDegree) + 'deg )  scale(1.35)',

        left: `${barWidth}upx`,
        top: `${barWidth}upx`
      }
      // backgroundColor: innerbgColor,
    }
  },
  mounted() {},
  methods: {}
}
</script>

<style scoped>
.huihui_component {
  justify-content: center;
  align-items: center;
}
.huihui_parent {
  justify-content: center;
  align-items: center;
  overflow: hidden;
}
.huihui_process {
  justify-content: center;
  align-items: center;
  overflow: hidden;
}
.huihui_inner_circle {
  justify-content: center;
  align-items: center;
  overflow: hidden;
  position: absolute;
  display: flex;
  background: url('/static/lesson/makeQuestion/speak.png') no-repeat;
  background-size: cover;
  transform: scale(2);
}
.huihui_left_part {
  overflow: hidden;
  position: absolute;
}
.huihui_right_part {
  overflow: hidden;
  position: absolute;
}
</style>
