<template>
  <view class="recorder">
    <view
      class="recorder-box"
      @longpress="onStartRecoder"
      @touchend="onEndRecoder"
    >
      <!-- <u-circle-progress
        :active-color="theme"
        :duration="0"
        :width="91"
        :percent="finish ? 0 : calcProgress"
      >
      </u-circle-progress> -->
    </view>
    <!-- <view
      class="recorder-box"
      v-else
      @longpress="onStartRecoder"
      @touchend="onEndRecoder"
    >
      <u-circle-progress
        :active-color="theme"
        :duration="0"
        :width="91"
        :percent="0"
      >
      </u-circle-progress>
    </view> -->
    <view class="mask"></view>
  </view>
</template>

<script>
// import uCircleProgress from './u-circle-progress.vue'
const recorderManager = uni.getRecorderManager()
export default {
  components: {
    // uCircleProgress
  },
  props: {
    width: {
      type: String,
      default: '91rpx'
    },
    height: {
      type: String,
      default: '91rpx'
    },
    maximum: {
      type: [Number, String],
      default: 60
    },
    duration: {
      type: Number,
      default: 20
    },
    theme: {
      type: String,
      default: '#fff'
    },
    endresult: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      reDate: '00:00',
      sec: 0,
      min: 0,
      finish: false,
      playProgress: 100,
      playTimer: null,
      timer: null,
      playStatus: false
    }
  },
  created() {
    // 监听
    this.onMonitorEvents()
  },
  computed: {
    // 录制时间计算
    calcProgress() {
      return ((this.sec + this.min * 60) / this.maximum) * 100
    }
  },
  methods: {
    // 点击事件
    // 重新录制
    reset() {
      this.min = 0
      this.sec = 0
      this.reDate = '00:00'
      this.playProgress = 100
      this.finish = false
      this.$emit('reset')
    },
    // 录制结束
    onEndRecoder() {
      uni.vibrateShort()
      recorderManager.stop()
      this.finish = true
    },
    // 开始录制
    onStartRecoder() {
      uni.vibrateShort()
      if (this.endresult) {
        this.$xh.Toast('测评已经结束了哦！')
        return
      }
      this.reset()
      setTimeout(() => {
        recorderManager.start({
          duration: this.maximum * 1000
        })
      }, 100)
    },
    // 监听
    onMonitorEvents() {
      // 录制开始
      recorderManager.onStart(() => {
        uni.showLoading({
          title: '采集中，请大声说话呦...'
        })
        this.startDate()
        this.$emit('start')
      })
      // 录制结束
      recorderManager.onStop(({ tempFilePath }) => {
        clearInterval(this.timer)
        uni.hideLoading()
        this.finish = false
        this.$emit('end', tempFilePath)
      })
    },
    // 录音计时
    startDate() {
      clearInterval(this.timer)
      this.sec = 0
      this.min = 0
      this.timer = setInterval(() => {
        this.sec += this.duration / 1000
        if (this.sec >= 60) {
          this.min++
          this.sec = 0
        }
      }, this.duration)
    }
  }
}
</script>

<style lang="scss">
.recorder {
  position: relative;
  display: flex;
  align-items: center;
  flex-direction: column;
  // background-color: red;
  font-size: 24rpx;
  // width: 100%;
  width: r(91);
  height: r(91);
  .mask {
    width: r(91);
    height: r(91);
    background: url('/static/lesson/makeQuestion/speak.png') no-repeat;
    background-size: cover;
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    margin: 0;
    pointer-events: none;
  }
  .re-top {
    display: flex;
    justify-content: space-between;
    padding: 10rpx 20rpx;
    width: 100%;
    font-size: 28rpx;
    box-sizing: border-box;
  }
  .title {
    font-size: 36rpx;
    color: #333;
    padding: 20rpx 0 30rpx;
  }
  .recorder-box {
    position: relative;
  }
  .now-date {
    font-size: 28rpx;
    color: #666;
    padding: 20rpx 0;
  }
}
</style>
