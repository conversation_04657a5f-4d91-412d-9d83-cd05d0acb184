<template>
  <view
    class="status_bar"
    :style="{
      height: iStatusBarHeight + 'px',
      backgroundColor: color,
      paddingTop: iStatusBarHeight + 'px',
      position: position
    }"
  ></view>
</template>
<script>
export default {
  props: {
    color: {
      default: '#fff'
    },
    position: {
      default: 'fixed'
    }
  },
  data() {
    return {
      iStatusBarHeight: 0
    }
  },
  mounted() {
    this.iStatusBarHeight = uni.getSystemInfoSync().statusBarHeight
    this.$emit('changetatusBarHeight', this.iStatusBarHeight)
  }
}
</script>
<style lang="scss">
.status_bar {
  height: --status-bar-height;
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  z-index: 10;
}
</style>
