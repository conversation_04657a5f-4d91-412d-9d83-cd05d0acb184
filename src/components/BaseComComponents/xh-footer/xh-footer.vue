<template>
  <view class="xh-footer">
    <view
      class="item message"
      :class="{ active: 'message' === currentRouter }"
      @click="changerouter('message')"
    >
      <image
        src="/static/tab/message.png"
        mode="widthFix"
        v-if="'message' !== currentRouter"
      />
      <image src="/static/tab/message-active.png" mode="widthFix" v-else />
      <text>消息</text>
    </view>
    <view
      class="fw item"
      :class="{ active: 'service' === currentRouter }"
      @click="changerouter('service')"
    >
      <view class="content-box">
        <!-- <image
          src="https://img2.baidu.com/it/u=2102736929,2417598652&fm=26&fmt=auto&gp=0.jpg"
           mode="widthFix"
        /> -->
        <text>服务</text>
      </view>
    </view>
    <view
      class="my item"
      @click="changerouter('my')"
      :class="{ active: 'my' === currentRouter }"
    >
      <image
        src="/static/tab/my.png"
        mode="widthFix"
        v-if="'my' !== currentRouter"
      />
      <image src="/static/tab/my-active.png" mode="widthFix" v-else />
      <text>我的</text>
    </view>
  </view>
</template>
<script>
export default {
  props: ['currentRouter'],
  methods: {
    changerouter(path) {
      this.$emit('change', path)
    }
  }
}
</script>
<style lang="scss" scoped>
.xh-footer {
  position: fixed;
  left: 0;
  bottom: 0;
  height: r(120);
  // height: r(180);
  width: 100vw;
  background-color: #fff;
  display: flex;
  align-items: center;
  justify-content: space-around;
  // overflow-y: hidden;
  // background: url('/static/nav/bg.png') no-repeat left top;
  // background-size: 100% 100%;
  // background-position: left top -26rpx; /* 0px, 15px */
  .item {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    width: 30%;
    // flex: 1;
    border-radius: 40%;
    height: 100%;
    transition: all 0.2s;
    image {
      width: r(64);
      margin-bottom: r(5);
    }
    text {
      font-size: r(24);
      color: #9aa7b8;
    }
  }
  .item:active {
    background-color: #f8f8f8;
  }
  .fw {
    width: r(140);
    height: r(140);
    background-color: #fff;
    border-radius: 50%;
    position: relative;
    top: -30rpx;
    padding: r(13);
    display: block;
    // padding-top: r(30);
    .content-box {
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      background-color: #f6c864;
      background-image: linear-gradient(to left, #feb423, #ffcf68);
      background: url('/static/tab/service.png');
      background-size: cover;
      width: 100%;
      height: 100%;
      border-radius: 50%;
      transition: all 0.2s;
      box-shadow: 0 0px 10px 1px rgba(0, 0, 0, 0.12);
      -moz-box-shadow: 0 0px 10px 1px rgba(0, 0, 0, 0.12);
      -webkit-box-shadow: 0 0px 10px 1px rgba(0, 0, 0, 0.12);
      padding-top: r(60);
    }
    text {
      color: #fff;
      transition: all 0.2s;
    }
  }
  .active {
    text {
      color: #58b1f8;
    }
  }
  .fw.active {
    .content-box {
      background-color: #5ac0f9;
      background-image: linear-gradient(to left, #03a9ff, #14c0ff);
      background: url('/static/tab/service-active2.png');
      background-size: cover;
    }
    text {
      color: #fff;
    }
  }
}
</style>
