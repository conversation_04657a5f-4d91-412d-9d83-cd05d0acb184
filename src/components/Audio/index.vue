<!-- 沟通记录 -->
<template>
    <view>
        <view>
            <view class="audio-con" :style="audioStyle">
                <view class="action-btn">
                    <img v-show="isPlayBtn" @click="playAudio"
                        src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/16867400357396248168674003573919594_play.png"
                        alt="">
                    <img v-show="!isPlayBtn" @click="puserAudio"
                        src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/16587431802302cb6165874318023045315_pause-icon.png"
                        alt="">
                </view>
                <view class="time-text start-time">
                    {{ currentTime | handleTime }}
                </view>
                <view>
                    <van-slider v-model="progressValue" @input="slide" />
                </view>
                <view class="time-text end-time">
                    {{ maxDuration | handleTime }}
                </view>
            </view>
            <br>

        </view>
    </view>
</template>
<script>

export default {
    props: {
        audioStyle: {
            type: Object,
            default: function () {
                return {
                    width: '5.26rem'
                }
            }
        },
        audioUrl: {
            type: String,
            default: ''
        }
    },

    data() {
        return {
            progressValue: 0,
            isPlayBtn: true,
            url: '',
            innerAudioContext: null,
            maxDuration: 0,
            currentTime: 0,
            progressNum: 0
        }
    },
    filters: {
        handleTime(oldTime) {
            let time = (oldTime / 100).toFixed(2).split('.')
            if (+time[0] < 10) {
                time[0] = '0' + time[0]
            }
            return time.join(':')
        },
    },
    mounted() {
        this.initAudio()

    },
    filter: {

    },
    methods: {
        // 初始化
        initAudio() {
            this.innerAudioContext = uni.createInnerAudioContext();
            // this.innerAudioContext.src = 'https://m7-record.oss-cn-hangzhou.aliyuncs.com/vPaiRecord/T00000027288/monitor/bj.tx.15.4/20230614/20230614-154914_T00000027288_1686728954000_ddbd56ad-67e8-4e5c-a528-7c4d80d84818.wav';
            this.innerAudioContext.src = this.audioUrl;
            this.currentTime = 0
            // 时间改变回调
            this.innerAudioContext.onTimeUpdate((res) => {
                this.currentTime = this.innerAudioContext.currentTime
                this.progressValue = this.currentTime / this.maxDuration * 100;
                if (this.innerAudioContext.currentTime === this.maxDuration && this.isPlayBtn === false) {
                    this.isPlayBtn = !this.isPlayBtn;
                    this.currentTime = 0
                    this.innerAudioContext.seek(this.currentTime)
                }
            })
            this.innerAudioContext.onError((res) => {
            });
            this.innerAudioContext.onCanplay((res) => {
                this.maxDuration = this.innerAudioContext.duration
            })
        },
        // 滑动滑块
        slide(val) {
            this.progressValue = val;
            this.currentTime = this.maxDuration * val / 100
            this.innerAudioContext.seek(this.currentTime)
        },
        // 点击播放按钮
        playAudio() {
            this.innerAudioContext.seek(this.currentTime)
            this.innerAudioContext.play()
            this.isPlayBtn = !this.isPlayBtn;
        },
        // 点击暂停按钮
        puserAudio() {
            this.innerAudioContext.stop()
            this.isPlayBtn = !this.isPlayBtn;
        },

    }
}
</script>
<style scoped lang="less">
.audio-con {
    display: flex;
    height: 0.8rem;
    background: rgba(3, 32, 61, 0.05);
    border-radius: 0.08rem;
    line-height: 0.8rem;
}

.van-slider {
    width: 2.5rem;
    border-radius: 0.08rem;
    background: #D8DFE6;
    display: inline-block;
    vertical-align: middle;
}


.action-btn {
    width: 0.36rem;
    height: 0.36rem;
    margin: 0 0.26rem;
}

.van-slider__button {
    width: 0.24rem !important;
    height: 0.24rem !important;
}

.action-btn img {
    vertical-align: middle;
    width: 0.36rem;
}

.time-text {
    font-size: 0.24rem;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: rgba(3, 32, 61, 0.85);
}

.start-time {
    margin-right: 0.16rem;
}

.end-time {
    margin-left: 0.16rem;
}
</style>

  