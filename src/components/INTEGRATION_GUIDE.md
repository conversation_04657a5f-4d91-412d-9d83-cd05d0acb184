# 学习日历组件集成指南

## 快速开始

### 1. 在首页中添加组件

在 `src/modules/jintiku/pages/index/index.vue` 中添加学习日历组件：

```vue
<template>
  <view class="question-index">
    <!-- 现有的头部内容 -->
    <view class="header-box">
      <!-- ... 现有代码 ... -->
    </view>
    
    <!-- 轮播图 -->
    <view class="banner-view">
      <u-swiper style="height: 200rpx;" :radius="'32rpx'" :list="bannerList" @click="bannerClick"></u-swiper>
    </view>
    
    <!-- 新增：学习日历组件 -->
    <study-calendar 
      :persist-days="studyStats.persistDays"
      :total-questions="studyStats.totalQuestions"
      :accuracy-rate="studyStats.accuracyRate"
      :is-checked-in="studyStats.isCheckedIn"
      @check-in="handleCheckIn"
    />
    
    <!-- 现有的学习卡片网格 -->
    <view>
      <study-card-grid @cardClick="handleCardClick" />
    </view>
    
    <!-- ... 其他现有内容 ... -->
  </view>
</template>
```

### 2. 导入组件

在 script 部分添加组件导入：

```vue
<script>
// 导入学习日历组件
import StudyCalendar from '@/components/study-calendar.vue'
// ... 其他导入

export default {
  components: {
    StudyCalendar,
    // ... 其他组件
  },
  data() {
    return {
      // ... 现有数据
      
      // 新增：学习统计数据
      studyStats: {
        persistDays: 31,
        totalQuestions: 700,
        accuracyRate: 12,
        isCheckedIn: true
      }
    }
  },
  // ... 其他配置
}
</script>
```

### 3. 添加事件处理方法

在 methods 中添加打卡处理方法：

```vue
<script>
export default {
  methods: {
    // ... 现有方法
    
    // 新增：处理打卡事件
    async handleCheckIn() {
      try {
        // 调用打卡API
        const result = await this.checkInAPI()
        if (result.success) {
          // 更新打卡状态
          this.studyStats.isCheckedIn = true
          this.studyStats.persistDays += 1
          
          // 保存打卡记录到本地存储
          const today = new Date().toDateString()
          uni.setStorageSync('lastCheckInDate', today)
          
          uni.showToast({
            title: '打卡成功！',
            icon: 'success',
            duration: 2000
          })
        }
      } catch (error) {
        console.error('打卡失败:', error)
        uni.showToast({
          title: '打卡失败，请重试',
          icon: 'error',
          duration: 2000
        })
      }
    },
    
    // 新增：打卡API调用
    async checkInAPI() {
      // 这里替换为实际的API调用
      return new Promise((resolve) => {
        setTimeout(() => {
          resolve({ success: true })
        }, 1000)
      })
    },
    
    // 新增：加载学习统计数据
    async loadStudyStats() {
      try {
        // 这里替换为实际的API调用
        // const stats = await this.$api.getStudyStats()
        
        // 模拟数据
        const stats = {
          persistDays: 31,
          totalQuestions: 700,
          accuracyRate: 12,
          isCheckedIn: this.checkTodayCheckIn()
        }
        
        this.studyStats = stats
      } catch (error) {
        console.error('加载学习统计失败:', error)
      }
    },
    
    // 新增：检查今日是否已打卡
    checkTodayCheckIn() {
      const today = new Date().toDateString()
      const lastCheckIn = uni.getStorageSync('lastCheckInDate')
      return lastCheckIn === today
    }
  }
}
</script>
```

### 4. 在页面生命周期中加载数据

```vue
<script>
export default {
  onLoad(e) {
    // ... 现有代码
    this.loadStudyStats()
  },
  
  onShow() {
    // ... 现有代码
    this.refreshStudyStats()
  },
  
  methods: {
    // ... 其他方法
    
    // 新增：刷新学习统计数据
    refreshStudyStats() {
      this.loadStudyStats()
    }
  }
}
</script>
```

## API 集成建议

### 1. 获取学习统计数据

建议创建一个API方法来获取用户的学习统计数据：

```javascript
// 在 api/index.js 中添加
export function getStudyStats() {
  return request({
    url: '/api/study/stats',
    method: 'GET'
  })
}
```

### 2. 打卡API

建议创建一个打卡API：

```javascript
// 在 api/index.js 中添加
export function checkIn() {
  return request({
    url: '/api/study/checkin',
    method: 'POST'
  })
}
```

### 3. 完整的API集成示例

```vue
<script>
import { getStudyStats, checkIn } from '@/api/index.js'

export default {
  methods: {
    async loadStudyStats() {
      try {
        const response = await getStudyStats()
        this.studyStats = {
          persistDays: response.data.persist_days,
          totalQuestions: response.data.total_questions,
          accuracyRate: response.data.accuracy_rate,
          isCheckedIn: response.data.is_checked_in_today
        }
      } catch (error) {
        console.error('加载学习统计失败:', error)
      }
    },
    
    async handleCheckIn() {
      try {
        const response = await checkIn()
        if (response.success) {
          // 重新加载统计数据
          await this.loadStudyStats()
          
          uni.showToast({
            title: '打卡成功！',
            icon: 'success'
          })
        }
      } catch (error) {
        uni.showToast({
          title: '打卡失败，请重试',
          icon: 'error'
        })
      }
    }
  }
}
</script>
```

## 样式调整

如果需要调整组件样式以适配现有设计，可以通过以下方式：

### 1. 修改组件边距

```vue
<style>
/* 在页面样式中覆盖组件样式 */
.study-calendar {
  margin: 16rpx 24rpx !important;
}
</style>
```

### 2. 自定义颜色主题

可以修改 `study-calendar.vue` 中的颜色变量来匹配项目主题色。

## 注意事项

1. 确保项目中已安装并配置了SCSS支持
2. 组件使用了uni-app的API，确保在uni-app环境中使用
3. 建议在真机上测试组件的显示效果
4. 打卡功能需要配合后端API实现数据持久化
5. 可以根据实际需求调整组件的props和样式
