import Vuex from 'vuex'
import Vue from 'vue'
Vue.use(Vuex)
const moduleContext = require.context('../modules', true, /store\/index\.js/)
let resultStore = {}
moduleContext.keys().forEach(name => {
	let names = name.split('/')
	if (!resultStore[names[1]]) {
		resultStore[names[1]] = {
			...moduleContext(name).default,
			namespaced: true
		}
	}
})
const store = new Vuex.Store({
	state: {
		token: ''
	},
	mutations: {},
	actions: {},
	getters: {},
	modules: resultStore
})
export default store
