import Vue from 'vue'
import App from './App'
import store from './store'
import xhutlis from './utlis'

import uView from 'uview-ui'
Vue.use(uView)

Vue.config.productionTip = false
App.mpType = 'app'
// import lodash from 'lodash'
Vue.prototype.$store = store
// Vue.prototype._ = lodash
// 全局公共方法
Vue.prototype.$xh = {}
for (let fn in xhutlis) {
  Vue.prototype.$xh[fn] = xhutlis[fn]
}
const app = new Vue({
  render: h => h(App)
})
app.$mount()
