// App 推送逻辑
export default {
  // onLaunch: function() {
  //   // #ifdef APP-PLUS
  //   plus.push.addEventListener('click', this._handlePush)
  //   plus.push.addEventListener('receive', this._handlePush)
  //   // #endif
  // },
  data() {
    return {
      jyJPush: []
    }
  },
  methods: {
    _handlePush() {
      //   this.jyJPush = uni.requireNativePlugin('JY-JPush')
      //   this.jyJPush.setJYJPushAlias(
      //     {
      //       userAlias: 'testAlias'
      //     },
      //     result => {
      //       // //  设置成功或者失败，都会通过这个result回调返回数据；数据格式保持极光返回的安卓/iOS数据一致
      //       // //  注：若没有返回任何数据，考虑是否初始化完成
      //       // uni.showToast({
      //       //   icon: 'none',
      //       //   title: JSON.stringify(result)
      //       // })
      //       uni.redirectTo({
      //         url: '/pages/index/index?name=message'
      //       })
      //     }
      //   )
      //   this.jyJPush.addJYJPushReceiveNotificationListener(result => {
      //     //  监听成功后，若收到推送，会在result返回对应的数据；数据格式保持极光返回的安卓/iOS数据一致
      //     // uni.showToast({
      //     //   icon: 'none',
      //     //   title: JSON.stringify(result.notificationContent)
      //     // })
      //     uni.redirectTo({
      //       url: '/pages/index/index?name=message'
      //     })
      //   })
      //   this.jyJPush.addJYJPushReceiveOpenNotificationListener(result => {
      //     //  监听成功后，若点击推送消息，会触发result；数据格式保持极光返回的安卓/iOS数据一致
      //     // uni.showToast({
      //     //   icon: 'none',
      //     //   title: JSON.stringify(result.notificationContent)
      //     // })
      //     uni.redirectTo({
      //       url: '/pages/index/index?name=message'
      //     })
      //   })
    }
  }
}
