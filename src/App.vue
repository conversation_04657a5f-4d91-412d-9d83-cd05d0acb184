<script>
// import onLaunch from './utlis/onLaunch.js'
import store from './store'
// import updateApp from './mixin/updateApp.js'
import uniPush from './mixin/uniPush.js'

export default {
  mixins: [uniPush],
  onLaunch: function (options) {
    //#ifdef MP-WEIXIN
    console.log('执行？？')
    // this.autoUpdate()
    //#endif
  },
  onShow: function () {
    this._handlePush()
    // 计算屏幕长宽比 更新到全局
    try {
      const res = uni.getSystemInfoSync()
      let proportion =
        res.screenHeight > res.screenWidth
          ? res.screenHeight / res.screenWidth
          : res.screenWidth / res.screenHeight
      store.state.proportion = proportion
    } catch (e) {
      // error
    }
  },
  onLoad() {
    console.log('没执行？？')
  }
}
</script>
<style lang="scss">
@import 'uview-ui/index.scss';
@import url('./commen.scss');
@import url('./static/css/animate.css');
</style>
