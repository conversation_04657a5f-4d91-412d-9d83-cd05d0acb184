view,
div,
cover-view,
input {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}
.box-show {
  box-shadow: 0 0px 10px 1px rgba(0, 0, 0, 0.12);
  -moz-box-shadow: 0 0px 10px 1px rgba(0, 0, 0, 0.12);
  -webkit-box-shadow: 0 0px 10px 1px rgba(0, 0, 0, 0.12);
}
.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}
.flex {
  display: flex;
  align-items: center;
}
image {
  will-change: transform;
}
// 安全区
.safePage {
  padding-bottom: 0;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}
// .commen {
//    // padding-bottom: r(120);
//    // height: 100vh;
//    // height: 100%;
//    // height: 100px;
//    // background-color: #F6C864;
//    background-color: #f8f8f8;
//  }
//  .height-footer{
//     height: r(120);
//  }
page {
  background-color: #f5f6f7;
}
.page {
  min-height: 100vh;
  background-color: #f7f7f7;
}
.btn:active {
  transform: scale(0.9);
}

// 百度小程序常用全局css---
.page-baidu {
  height: 100vh;
  background-color: #fff;
  overflow-y: auto;
  position: relative;
}
.white {
  color: #fff;
}
.safe-btm {
  padding-bottom: 0;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}
.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}
.button {
  transition: all 0.25s;
}
.button:active {
  transform: scale(0.9);
}
.hide-text {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.no-data {
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 140rpx;
  font-size: 28rpx;
  color: #ccc;
}
.red {
  color: #f44141;
}
.main-color {
  color: #387dfc;
}
.blod {
  font-weight: 800;
}

.pay-time .u-count-down__text {
  font-weight: 500;
  text-align: center;
  font-size: 24rpx !important;
  color: #2e68ff !important;
  height: 44rpx;
  line-height: 44rpx !important;
  width: 160rpx;
}
