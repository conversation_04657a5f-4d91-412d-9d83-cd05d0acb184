export default {
  // 基础设置获取秘钥
  systemSeret() {
    if (process.env.NODE_ENV === 'development') {
      return 'ECADUT9R5C8U0U5RK8II7Q27LR0YPCYW'
    } else if (process.env.NODE_ENV === 'production') {
      return 'ECADUT9R5C8U0U5RK8II7Q27LR0YPCYW'
    }
  },
  // 运营
  crmSeret() {
    if (process.env.NODE_ENV === 'development') {
      return 'JX5RTY0QYTIDGWKMSZMZ2LS3DWPVQ9C3'
    } else if (process.env.NODE_ENV === 'production') {
      return 'JX5RTY0QYTIDGWKMSZMZ2LS3DWPVQ9C3'
    }
  }
}
