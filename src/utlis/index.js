import store from '../store/index'
import storeSpaceName from './storeSpaceName'
import { BaseUrl, getBaseUrl } from '../api/httpRequestConfig'
// var md5 = require('./md5.js')
var md5 = require('md5')
var Base64 = require('./base64')
// import sha1 from 'js-sha1'
import { packey } from '@/config'
const bus = require('../../bus/index')
const { moduleName } = require('../../bus/busName')
const week = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
const innerAudioContext = uni.createInnerAudioContext()
bus.$on(moduleName, name => {
  console.log('name', name)
})
export const isPad = () => {
  return new Promise((resolve, reject) => {
    uni.getSystemInfo({
      success(info) {
        if (info.windowWidth >= 768) {
          resolve('ipad')
        } else {
          resolve('iphone')
        }
      },
      fail() {
        reject()
      }
    })
  })
}

function Toast(text, fn) {
  uni.showToast({
    icon: 'none',
    title: text,
    duration: 2000
  })
  setTimeout(() => {
    fn && fn()
  }, 200)
}
Toast.success = text => {
  uni.showToast({
    icon: 'success',
    title: text,
    duration: 2000
  })
}
Toast.loading = text => {
  uni.showToast({
    icon: 'loading',
    title: text,
    duration: 2000
  })
}

function caniUse(apistr, api) {
  function _null() {
    console.log(apistr + ' api在该平台下不可用，请检查兼容性')
    return {}
  }
  if (uni.canIUse(apistr)) {
    return api
  } else {
    return _null
  }
}
export default {
  // 带权限的跳转
  /**
   *@modelName 模块名
   *@path 路径
   *@routes store中所存储的相关所有路由string
   */
  $push(modelName, path, routes = 'routes') {
    let paths = store.state[modelName][routes]
    if (!paths) {
      return this.Toast('未知代码错误！')
    }
    if (!modelName) {
      return this.Toast('跳转失败，模块名未找到！')
    }
    if (!path) {
      return this.Toast('跳转失败，路径未找到！')
    }
    if (!paths.includes(path.split('?')[0])) {
      return this.Toast('暂无权限！')
    }
    uni.navigateTo({
      url: `/modules/${modelName}/${path}`,
      fail(err) {
        console.log(err, 'err1')
        uni.switchTab({
          url: `/modules/${modelName}/${path}`,
          fail(err) {
            console.log(err, 'err2')
          }
        })
      }
    })
  },
  $redirect(modelName, path, routes = 'routes') {
    let paths = store.state[modelName][routes]
    if (!paths) {
      return this.Toast('未知代码错误！')
    }
    if (!modelName) {
      return this.Toast('跳转失败，模块名未找到！')
    }
    if (!path) {
      return this.Toast('跳转失败，路径未找到！')
    }
    if (!paths.includes(path.split('?')[0])) {
      return this.Toast('暂无权限！')
    }
    uni.redirectTo({
      url: `/modules/${modelName}/${path}`,
      fail(err) {
        console.log(err)
      }
    })
  },
  /**
   * @不带权限的跳转路由
   * @modelName 模块名
   * @path 对应的路由路径
   * @options 参数
   */
  push(modelName, path, options = {}) {
    uni.navigateTo({
      url: `/modules/${modelName}/${path}`,
      ...options
    })
  },
  redirect(modelName, path, options = {}) {
    uni.redirectTo({
      url: `/modules/${modelName}/${path}`,
      ...options
    })
  },
  // 隐藏/显示 tab栏
  tabBar(type, obj = {}) {
    if (type === 'show') {
      uni.showTabBar(obj)
    } else {
      uni.hideTabBar(obj)
    }
  },
  getplatform() {
    const platform = uni.getSystemInfoSync().platform
    return platform
  },
  // 本地缓存
  setStorage(key, data) {
    try {
      let _data = data
      if (_data === undefined || _data === null) {
        console.log('setStorage存储错误:key和value为', key, data)
        return
      }
      if (typeof data !== 'string' && typeof _data !== 'number') {
        _data = JSON.stringify(_data)
      }
      if (typeof _data === 'number') {
        _data = String(_data)
      }
      uni.setStorageSync(key, _data)
    } catch (e) {
      // error
      console.error('同步缓存出错')
    }
  },
  setStorageSync_(key, data) {
    let _data = data
    if (_data === undefined || _data === null) {
      console.log('setStorage存储错误:key和value为', key, data)
      return
    }
    if (typeof data !== 'string' && typeof value !== 'number') {
      _data = JSON.stringify(_data)
    }
    try {
      uni.setStorageSync(key, _data)
    } catch (e) {
      console.log(e)
    }
  },
  getStorage(key) {
    let value
    //#ifdef APP-PLUS
    value = uni.getStorageSync(key)
    //#endif
    //#ifdef H5
    value = localStorage.getItem(key)
    //#endif
    try {
      return JSON.parse(value)
    } catch (e) {
      return value
    }
  },
  // 清除所有缓存
  clearStorage() {
    try {
      //#ifdef APP-PLUS
      uni.clearStorageSync()
      //#endif
      //#ifdef H5
      sessionStorage.clear()
      //#endif
    } catch (e) {
      // error
      Toast('清除失败')
    }
  },
  // 自定义loding
  showLoading() {
    store.commit('showLoading')
  },
  hideLoading() {
    store.commit('hideLoading')
  },
  Toast: Toast,

  // 监测数据对象是否有空值
  isNull(obj) {
    let flag = false
    for (let key in obj) {
      if (obj[key] === '') {
        flag = true
      }
    }
    return flag
  },

  // 序列化时间
  parseTimeDay(time) {
    if (!time) {
      return '暂无'
    }
    const d = new Date(time * 1000)
    let mounth = d.getMonth() + 1
    let year = d.getFullYear() + '年'
    mounth = mounth >= 10 ? mounth + '月' : '0' + mounth + '月'
    let day = d.getDate()
    day = day >= 10 ? day + '日' : '0' + day + '日'
    return year + mounth + day
  },
  // 序列化时间 2018.08.03
  parseTimeYear(time) {
    if (!time) {
      return '暂无'
    }
    const d = new Date(time * 1000)
    let year = d.getFullYear()
    let mounth = d.getMonth() + 1
    mounth = mounth >= 10 ? mounth : '0' + mounth
    let day = d.getDate()
    day = day >= 10 ? day : '0' + day
    return `${year}.${mounth}.${day}`
  },
  parseFullTime(time) {
    if (!time) {
      return '暂无'
    }
    const d = new Date(time)
    let year = d.getFullYear()
    let mounth = d.getMonth() + 1
    mounth = mounth >= 10 ? mounth : '0' + mounth
    let hour = d.getHours()
    hour = hour >= 10 ? hour : '0' + hour
    let min = d.getMinutes()
    min = min >= 10 ? min : '0' + min
    let second = d.getSeconds()
    second = second >= 10 ? second : '0' + second
    let day = d.getDate()
    day = day >= 10 ? day : '0' + day
    return `${year}-${mounth}-${day} ${hour}:${min}:${second}`
  },
  parseTimeMouth(time) {
    if (!time) {
      return '暂无'
    }
    const d = new Date(time * 1000)
    let year = d.getFullYear()
    let mounth = d.getMonth() + 1
    mounth = mounth >= 10 ? mounth : '0' + mounth
    let day = d.getDate()
    day = day >= 10 ? day : '0' + day
    return `${mounth}月${day}日`
  },
  // 序列化时间 获取周
  parseTimeWeek(time) {
    if (!time) {
      return '暂无'
    }
    const d = new Date(time * 1000)
    let index = d.getDay()
    return week[index - 1]
  },
  // 本地存储的命名在这里面
  getSpacename() {
    return storeSpaceName
  },

  caniUse: caniUse,
  // 字符串截取
  strSlice: (str, num) => {
    // return
    if (str.length >= num) {
      return str.slice(0, num) + '...'
    }
    return str
  },
  // 文件上传
  upLoad(tempfileurl, resolveFn, rejectFn) {
    return new Promise((resolve, reject) => {
      let formData = {}
      function success(uploadFileRes) {
        if (!uploadFileRes) {
          return reject({
            status: false,
            code_msg: '啥都没有返回！'
          })
        }
        let data = JSON.parse(uploadFileRes.data)
        let _resolve = resolveFn ? resolveFn : resolve
        let _reject = rejectFn ? rejectFn : reject
        if (data.code === 100002) {
          store.state.token = ''
          uni.removeStorage('__xingyun_userinfo__')
          uni.removeStorage('__xingyun_token__')
          // 去登录
          that.push('jintiku', 'pages/loginCenter/index')
          return _reject(data)
        } else if (data.code === 100002) {
          Toast('上传失败请重试!')
          return _reject(data)
        } else {
          return _resolve(data.data[0])
        }
      }
      function fail(err) {
        console.log(err, '上传失败')
        return reject(err)
      }
      let that = this
      if (!tempfileurl) {
        uni.chooseImage({
          success: chooseImageRes => {
            const tempFilePaths = chooseImageRes.tempFilePaths
            that.upLoad(tempFilePaths[0], resolve, reject)
          }
        })
      } else {
        let baseUrl = ''
        if (process.env.NODE_ENV == 'development') {
          baseUrl = 'https://xingyundev.jinyingjie.com/api'
        } else {
          baseUrl = process.env.VUE_APP_BASE_API
        }
        // 认证变量
        const biseKey = process.env.VUE_APP_BASICKEY
        const biseValue = process.env.VUE_APP_BASICVALUE
        let base64 = Base64.encode(`${biseKey}:${biseValue}`)
        uni.uploadFile({
          header: {
            Authorization: 'Basic ' + base64
          },
          url: baseUrl + '/c/base/uploadfiles',
          filePath: tempfileurl,
          name: 'file',
          formData,
          success: success,
          fail: fail
        })
      }
    })
  },
  // 将网络图片转base64
  urlTobase64(img) {
    return new Promise((resolve, reject) => {
      // #ifdef APP-PLUS
      uni.downloadFile({
        url: img,
        success(res) {
          uni.getFileSystemManager().readFile({
            filePath: res.tempFilePath, //选择图片返回的相对路径
            encoding: 'base64', //编码格式
            success: res => {
              resolve('data:image/png;base64,' + res.data)
            },
            fail() {
              resolve('data:')
            }
          })
        }
      })
      // #endif

      // #ifdef H5
      var xhr = new XMLHttpRequest()
      xhr.open('get', img, true)
      // 至关重要
      xhr.responseType = 'blob'
      xhr.onload = function () {
        if (this.status == 200) {
          //得到一个blob对象
          var blob = this.response
          // 至关重要
          let oFileReader = new FileReader()
          oFileReader.onloadend = function (e) {
            // 此处拿到的已经是 base64的图片了
            let base64 = e.target.result
            resolve(base64)
          }
          oFileReader.readAsDataURL(blob)
        }
      }
      xhr.send()
      // #endif
    })
  },
  // 常用正则表达式
  getRegExp() {
    return {
      // 手机号
      phone:
        /^(13[0-9]|14[01456879]|15[0-35-9]|16[2567]|17[0-8]|18[0-9]|19[0-35-9])\d{8}$/
    }
  },
  // 校验表单
  checkProp(data, rules) {
    let flag = true
    for (let key in data) {
      if (rules[key] && rules[key].reg) {
        // 说明是正则
        if (!rules[key].reg.test(data[key])) {
          rules[key] && rules[key].tip && Toast(rules[key].tip)
          flag = false
        }
      } else {
        // 说明是字符串  只校验字符串是否是空即可
        if (!data[key]) {
          rules[key] && rules[key].tip && Toast(rules[key].tip)
          flag = false
        }
      }
    }
    return flag
  },
  // 加密参数
  getSin(json, secret) {
    let _json = JSON.stringify(json)
    // return md5(sha1(md5(_json + secret)))
  },
  // 跳转
  push(modelName, path, options = {}) {
    // uni.navigateTo({
    //   url: `/modules/${modelName}/${path}`,
    //   ...options
    // })
    uni.navigateTo({
      url: `/modules/${modelName}/${path}`,
      ...options,
      fail(err) {
        console.log(err)
        uni.switchTab({
          url: `/modules/${modelName}/${path}`,
          fail(err) {
            console.log(err, 'err2')
          }
        })
      }
    })
  },
  redirect(modelName, path, options = {}) {
    uni.redirectTo({
      url: `/modules/${modelName}/${path}`,
      ...options
    })
  },
  // 获取当前路由
  getPath() {
    let currentRoutes = getCurrentPages() // 获取当前打开过的页面路由数组
    let currentRoute = currentRoutes[currentRoutes.length - 1].route //获取当前页面路由
    return currentRoute
  },
  // 对象数组扁平化
  flat(arr, children = 'subs') {
    return arr.reduce((pre, current) => {
      if (current[children] && current[children].length) {
        pre = pre.concat(this.flat(current[children], children))
      }
      pre.push(current)
      return pre
    }, [])
  },
  /*
   * @预览文件
   * @url 远程路径
   * @type 文件类型
   */
  priview(url, type = 'pdf') {
    uni.showLoading({
      title: '文件正在打开中，请稍等...'
    })
    return new Promise((resolve, reject) => {
      uni.downloadFile({
        url: url,
        timeout: 500000,
        success(e) {
          if (e.statusCode != 200) {
            uni.hideLoading()
            reject(e)
            return
          }
          uni.openDocument({
            filePath: e.tempFilePath,
            fileType: type,
            success() {
              uni.hideLoading()
              resolve()
            },
            fail(err) {
              uni.hideLoading()
              reject(err)
            }
          })
        },
        fail(err) {
          // uni.hideLoading()
          // reject(err)
          // 非远程文件 - 本地文件
          uni.openDocument({
            filePath: url,
            fileType: type,
            success() {
              uni.hideLoading()
              resolve()
            },
            fail(err) {
              uni.hideLoading()
              reject(err)
            }
          })
        }
      })
    })
  },
  /**
   * 保存文件
   * @url 远程url链接
   */
  saveFile(url) {
    let that = this
    uni.showLoading({
      title: '文件正保存，请稍等...'
    })
    function hidLoading() {
      uni.hideLoading()
      that.Toast('文件保存成功！')
    }
    return new Promise((resolve, reject) => {
      uni.downloadFile({
        url: url,
        timeout: 500000,
        success(e) {
          if (e.statusCode != 200) {
            hidLoading()
            reject(e)
            return
          }
          uni.saveFile({
            tempFilePath: e.tempFilePath,
            success(e) {
              hidLoading()
              resolve()
            },
            fail(err) {
              hidLoading()
              reject(err)
            }
          })
        },
        fail(err) {
          hidLoading()
          reject(err)
        }
      })
    })
  },
  /**
   * @获取文件列表
   */
  getSaveFileLists() {
    return new Promise((resolve, reject) => {
      uni.getSavedFileList({
        success(e) {
          resolve(e.fileList ? e.fileList : [])
        },
        fail(err) {
          reject(err)
        }
      })
    })
  },
  /**
   * @拼接完整图片路径
   */
  completepath(path) {
    return process.env.VUE_APP_BASEOSSURL + path
  },
  /**
   * @发起支付
   * @data 支付必填项
   * @success 成功回调
   * @fail 成功回调
   */
  pay(data, success, fail) {
    uni.getProvider({
      service: 'payment',
      success(e) {
        const { provider } = e
        uni.requestPayment({
          provider,
          ...data,
          success(e) {
            success && success(e)
          },
          fail(err) {
            fail && fail(err)
          }
        })
      }
    })
  },
  iosTime(time) {
    return time.replace(/\-/g, '/')
  },
  // // 全局音频播放
  playAudio(src, fn = null, err = null) {
    innerAudioContext.stop()
    innerAudioContext.autoplay = false
    if (src) {
      innerAudioContext.src = src
    } else {
      return
    }
    innerAudioContext.play()
    innerAudioContext.onPlay(() => {
      // console.log('开始播放音频', src)
    })
    innerAudioContext.onError(res => {
      console.log(res.errMsg)
      console.log(res.errCode)
      err && err()
    })
    innerAudioContext.onEnded(res => {
      fn && fn()
    })
  },
  pauseAudio() {
    innerAudioContext.pause()
  },
  shareAppMessage() {
    let { major_id = '', major_name = '' } =
      uni.getStorageSync('__xingyun_major__')
    let userinfo = uni.getStorageSync('__xingyun_userinfo__')
    let employee_id = ``
    if (userinfo?.employee_info?.employee_id) {
      employee_id = `&employee_id=${userinfo?.employee_info?.employee_id}`
    }
    console.log(
      `/modules/jintiku/pages/index/index?major_id=${major_id}&major_name=${major_name}${employee_id}`,
      '分享链接'
    )
    return {
      title: '牙开心助你医路通关',
      imageUrl:
        'https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/datatpls/jintikutocshare.jpg',
      path: `/modules/jintiku/pages/index/index?major_id=${major_id}&major_name=${major_name}${employee_id}`
    }
  },
  isInitName(url) {
    let info = uni.getStorageSync('__xingyun_userinfo__')
    let nickname = info.nickname
    if (!nickname) {
      wx.showModal({
        title: '提示',
        content: '未检测到您的用户名，请增加。',
        success(res) {
          if (res.confirm) {
            uni.navigateTo({
              url: `/modules/jintiku/pages/my/person?toUrl=${encodeURIComponent(
                url
              )}`,
              fail() {
                uni.navigateBack({
                  delta: 1
                })
              }
            })
          } else if (res.cancel) {
            console.log('用户点击取消')
          }
        }
      })
      return true
    }
    return false
  }
}
