// import {
// 	timeout,
// 	noToastUrl,
// 	// getBaseUrl,
// 	// app_id
// } from './httpRequestConfig'
import $xh from '@utlis/index'
import Base64 from '@utlis/base64'
import store from '../store/index'
// 认证变量
const biseKey = process.env.VUE_APP_BASICKEY
const biseValue = process.env.VUE_APP_BASICVALUE

let showLoading = function (loadingtext) {
  uni.showLoading({
    title: loadingtext ? loadingtext : '加载中..'
  })
}
let hideLoading = function () {
  uni.hideLoading()
}
// 设置认证
function setBasic(header = {}) {
  let base64 = Base64.encode(`${biseKey}:${biseValue}`)
  header['Authorization'] = 'Basic ' + base64
  header['x-merchant-id'] = Base64.encode(process.env.VUE_APP_MERCHANTID)
  header['x-brand-id'] = Base64.encode(process.env.VUE_APP_BRANDID)
}

// 处理装态码
function handlerCode(
  data,
  normal_code = 1000000,
  handlerStatusCode = {},
  requestData
) {
  let res_code = data.code
  if (res_code == normal_code || res_code == 0) {
    // 正常
    return true
  } else {
    // 不正常 - 提示
    if (data.msg && data.msg.length) {
      if (typeof data.msg == 'string') {
        $xh.Toast(data.msg)
      } else {
        data.msg.forEach(msg => {
          $xh.Toast(msg)
        })
      }
    } else {
      $xh.Toast('接口返回异常，请打开网络选项检查！')
    }
    if (handlerStatusCode[res_code]) {
      // 有特殊装态吗处理特殊状态码
      return handlerStatusCode[res_code](data, requestData)
    }
    return false
  }
}

function request({
  url,
  _baseUrl, // 基础路径
  data = {},
  method = 'GET',
  hideloading = false,
  header = {},
  noToastUrl,
  noCheckoutLogin,
  timeout,
  normal_code,
  handlerStatusCode,
  checkLogin // 检查登陆
}) {
  let _header = {
    ...header
  }
  if (!noToastUrl.includes(url) && !data.noloading) {
    !hideloading && showLoading(data.loadingtext)
  }
  //未登录接口也要传header导致的
  if (!checkLogin(_header, url, data) && !noCheckoutLogin.includes(url)) {
    // 默认失败
    // $xh.Toast('登录失效！')
    console.log(`登录失效！，失效地址：${url}`)
    // wx.switchTab({
    //   url: '/modules/officialWebsite/pages/my/index'
    // })
    uni.hideLoading()
    return Promise.reject('登录失效！')
  }
  return new Promise((resolve, reject) => {
    let _url
    let _data = JSON.parse(JSON.stringify(data))
    _url = _baseUrl + url
    setBasic(_header) // 设置认证
    if (!_header['Content-Type']) {
      if (method.toUpperCase() === 'POST' || method.toUpperCase() === 'PUT') {
        _header['content-type'] = 'application/x-www-form-urlencoded'
      } else {
        _header['content-type'] = 'application/text'
      }
    }
    console.log(_url, '_url')
    uni.request({
      url: _url,
      method,
      header: _header,
      data: _data,
      dataType: 'json', // 返回数据格式
      timeout,
      success(res) {
        hideLoading()
        // if (!res.data.status) {
        //   let msg =
        //     res.data && res.data.errmsg ? res.data.errmsg : '接口发生未知错误！'
        //   $xh.Toast(msg)
        //   reject(res)
        // } else {
        //   hideLoading()
        //   resolve(res.data)
        // }
        // 处理状态码 handlerStatusCode 是特殊装态码处理
        let flag = handlerCode(
          res.data,
          normal_code,
          handlerStatusCode,
          ...arguments
        )
        if (flag) {
          // 成功请求
          resolve(res.data)
        } else {
          reject(res.data)
        }
      },
      fail(err) {
        hideLoading()
        if (!noToastUrl.includes(url)) {
          $xh.Toast('哎呦，出错啦~~')
        }
        reject(err)
      }
    })
  })
}
// 函数柯里化
export default (function (_baseUrl) {
  return function ({
    noToastUrl = [],
    noCheckoutLogin = [],
    timeout = 10000,
    normal_code = 10000,
    handlerStatusCode = {},
    checkLogin = () => {
      return false
    }
  }) {
    return (params = {}) => {
      return request({
        ...params,
        timeout,
        _baseUrl: _baseUrl,
        noToastUrl,
        noCheckoutLogin,
        normal_code,
        handlerStatusCode,
        checkLogin
      })
    }
  }
})(process.env.VUE_APP_BASE_API)
// 返回请求实例
export function newRequest(_baseUrl = process.env.VUE_APP_BASE_API) {
  return function ({
    noToastUrl = [],
    noCheckoutLogin = [],
    timeout = 10000,
    normal_code = 10000,
    handlerStatusCode = {},
    checkLogin = () => {
      return false
    }
  }) {
    return (params = {}) => {
      return request({
        ...params,
        timeout,
        _baseUrl: _baseUrl,
        noToastUrl,
        noCheckoutLogin,
        normal_code,
        handlerStatusCode,
        checkLogin
      })
    }
  }
}
