export const timeout = 6000 * 5
// 基础请求路径
export const getBaseUrl = (paragraph = '') => {
  if (process.env.NODE_ENV === 'development') {
    return 'https://xingyundev.jinyingjie.com/' + paragraph
  } else if (process.env.NODE_ENV === 'production') {
    // return 'https://api.jinyingjie.com' + paragraph
    return process.env.VUE_APP_BASE_API + paragraph
  }
}
export const errMessage = [
  {
    url: '/api/auth/login',
    msg: '验证码或者手机号不匹配',
    code: 401
  }
]
// 不需要做提醒的api url列表
export const noToastUrl = ['/api/report/app']

// 不需要tokne 白名单
export const whiteList = []

export const app_id = '5226278382408306815'
