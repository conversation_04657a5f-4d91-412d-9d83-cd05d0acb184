import getrequest from './request.js'
import { getBaseUrl } from './httpRequestConfig'
import secretConfig from '@/utlis/secretConfig.js'
// 基础请求函数
// secretConfig.crmSeret()
const http = getrequest(getBaseUrl())
// 微信登陆
export const wxLogin = data => {
  return http({
    url: '/Login/getWxLogin',
    data: data,
    method: 'GET'
  })
}
// 业务员生成小程序推广码
export const getWxCode = data => {
  return http({
    url: '/WeChatApplet/appletQrCode',
    data: data,
    method: 'GET'
  })
}
// 学员端生成添加老师二维码
export const getWeChatPromoterQrcode = data => {
  return http({
    url: '/WeChatApplet/getWeChatPromoterQrcode',
    data: data,
    method: 'GET'
  })
}
// 活动信息
export const setActive = data => {
  return http({
    url: '/MiniApps/miniAppActiveInfoRecord',
    data: data,
    method: 'GET'
  })
}
// 收藏列表
export const getLearningData = function (data = {}) {
  return http({
    url: '/c/tiku/exam/learning/data',
    method: 'GET',
    data
  })
}
