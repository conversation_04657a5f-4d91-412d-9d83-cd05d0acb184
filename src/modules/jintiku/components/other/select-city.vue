<template>
  <view
    class="select-lesson mask"
    :class="{ show }"
    @click="close"
    :style="{ height: show ? '100%' : '0' }"
  >
    <!-- :style="{ height: isShow ? '100%' : '0' }" -->
    <view class="box" :class="{ show }" @click.stop="() => {}">
      <view class="title">选择对应校区下的开课城市</view>
      <view class="select-content">
        <view class="select-content-left">
          <view
            class="select-menu hide-text"
            :class="{ active: leftActiveId == item.id }"
            v-for="(item, index) in datas"
            :key="index"
            @click.stop="getRightInfo(item)"
          >
            {{ item.name }}
          </view>
        </view>
        <view class="select-content-right">
          <view class="select-menu-content">
            <view
              class="select-menu-item hide-text"
              v-for="(item, index) in rightInfos"
              :class="{ active: item.city_id == value }"
              :key="index"
              @click.stop="change(item)"
            >
              {{ item.city_id_name }}
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>
<script>
import { homeApi } from '../../api/index'
export default {
  props: {
    value: {
      type: String
    },
    show: {
      type: Boolean
    },
    cityIdName: {
      type: String
    }
  },
  data() {
    return {
      leftActiveId: '',
      datas: [],
      rightInfos: []
    }
  },
  created() {
    this.getDatas()
  },
  methods: {
    getDatas() {
      homeApi
        .getCampus({
          page: 1,
          size: 1000
        })
        .then(data => {
          this.datas = data.data.list
          if (this.datas.length) {
            this.getRightInfo(this.datas[0])
          }
        })
    },
    getRightInfo(item) {
      this.leftActiveId = item.id
      homeApi
        .getCityLists({
          page: 1,
          size: 1000,
          org_id: item.id,
          is_usable: 1
        })
        .then(data => {
          this.rightInfos = data.data.list
        })
    },
    change(item) {
      this.$emit('input', item.id)
      this.$emit('change', item)
      this.$emit('update:show', false)
      this.$emit('update:cityIdName', item.city_id_name)
    },
    close() {
      this.$emit('update:show', false)
    }
  }
}
</script>
<style scoped lang="less">
.mask {
  position: fixed;
  left: 0;
  // top: 176rpx;
  top: 160rpx;
  z-index: 10;
  width: 100%;
  height: 100%;
  transition: all 0.25s;
  background-color: rgba(0, 0, 0, 0.4);
  opacity: 0;
  // pointer-events: none;
  .box {
    background-color: #fff;
    height: 0;
    transition: all 0.25s;
    overflow: hidden;
    // height: 1032rpx;
    .title {
      height: 72rpx;
      display: flex;
      align-items: center;
      padding-left: 24rpx;
      margin-bottom: 24rpx;
      font-size: 24rpx;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: rgba(3, 32, 61, 0.65);
    }
    .select-content {
      height: 960rpx;
      display: flex;
      .select-content-left {
        width: 184rpx;
        height: 100%;
        background: #f6f7f8;
        overflow-y: auto;
        .select-menu {
          height: 96rpx;
          line-height: 96rpx;
          text-align: center;
          background: #f6f7f8;
          width: 100%;
          padding: 0 10rpx;
          // display: flex;
          // align-items: center;
          // justify-content: center;
          font-size: 26rpx;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #03203d;
        }
        .select-menu.active {
          background-color: #fff;
        }
      }
      .select-content-right {
        flex: 1;
        background-color: #fff;
        padding: 0 24rpx;
        overflow-y: auto;
        height: 100%;
        .select-menu-item {
          border: 2rpx solid transparent;
          width: 224rpx;
          height: 68rpx;
          line-height: 68rpx;
          text-align: center;
          padding: 0 10rpx;
          // display: flex;
          // align-items: center;
          // justify-content: center;
          border-radius: 34rpx;
          background: #f6f7f8;
          color: #03203d;
          font-size: 24rpx;
          transition: all 0.25s;
          margin-bottom: 32rpx;
        }
        .select-menu-item.active {
          background: #ebf1ff;
          border: 2rpx solid rgba(46, 104, 255, 0.5);
          color: #2e68ff;
        }
      }
      .select-menu-content {
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        justify-content: space-between;
      }
    }
  }
  .show {
    height: 1032rpx;
  }
}
.show {
  opacity: 1;
}
</style>
