<template>
  <view class="screen">
    <view class="top">
      <text></text>
      <view>全部筛选</view>
      <img
        @click="clickFun"
        src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/16696466273197224166964662731928647_icon_guanbi%402x.png"
      />
    </view>
    <view class="content">
      <view v-for="i in 4" :key="i">
        <view class="titleBox">授课形式</view>
        <view class="listBox">
          <view
            v-for="(item, index) in teachingFrom"
            :key="index"
            :class="{ viewOne: teaching_type == item.type }"
          >
            {{ item.name }}
          </view>
        </view>
      </view>
    </view>
    <view class="footer">
      <view class="btn">
        <view class="left">重置</view>
        <view class="right">确认</view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      teaching_type: '',
      teachingFrom: [
        {
          name: '直播',
          type: '1'
        },
        {
          name: '面授',
          type: '2'
        },
        {
          name: '录播',
          type: '3'
        }
      ]
    }
  },
  methods: {
    clickFun() {
      this.$emit('operationFun')
    }
  }
}
</script>
<style lang="less" scoped>
.screen {
  width: 100%;
  height: 70%;
  position: fixed;
  bottom: 0;
  left: 0;
  background: #ffffff;
  border-radius: 40rpx 40rpx 0 0;
  padding: 0 24rpx;
  display: flex;
  flex-direction: column;
  .top {
    padding: 32rpx 40rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 32rpx;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #03203d;
    img {
      width: 40rpx;
      height: 40rpx;
    }
  }
  .content {
    flex: 1;
    overflow-y: auto;
    padding-bottom: 200rpx;
    .titleBox {
      font-size: 24rpx;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: rgba(3, 32, 61, 0.65);
      margin-bottom: 24rpx;
    }
    .listBox {
      display: flex;
      flex-wrap: wrap;
      margin-bottom: 24rpx;
      view {
        width: 214rpx;
        height: 68rpx;
        background: #f6f7f8;
        border-radius: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24rpx;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #03203d;
        margin-right: 20rpx;
        margin-bottom: 24rpx;
      }
      .viewOne {
        background: #ebf1ff;
        border-radius: 32rpx;
        border: 2rpx solid rgba(46, 104, 255, 0.5);
        color: #2e68ff;
      }
    }
  }
  .footer {
    position: fixed;
    left: 0;
    bottom: 15rpx;
    width: 100%;
    height: 88rpx;
    padding: 0 24rpx;
    .btn {
      width: 100%;
      display: flex;
      height: 100%;
      border-radius: 44rpx;
      overflow: hidden;
      view {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 28rpx;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
      }
      .left {
        background: #eceef0;

        color: rgba(3, 32, 61, 0.85);
      }
      .right {
        background: #1469ff;
        color: #ffffff;
      }
    }
  }
}
</style>
