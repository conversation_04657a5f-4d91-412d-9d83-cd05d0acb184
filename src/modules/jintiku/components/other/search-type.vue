<template>
  <picker-shell height="1120rpx" title="全部筛选" :value="value" @input="input">
    <view class="screen">
      <view class="content">
        <view>
          <view class="titleBox">授课形式</view>
          <view class="listBox">
            <view
              v-for="(item, index) in teachingFrom"
              :key="index"
              :class="[
                {
                  viewOne: teaching_type == item.type,
                  viewBox: (index + 1) % 3 == 0
                }
              ]"
              @click="teachingFun(item.type)"
            >
              {{ item.name }}
            </view>
          </view>
        </view>
        <view>
          <view class="titleBox">服务模式</view>
          <view class="listBox">
            <view
              v-for="(item, index) in serviceFrom"
              :key="index"
              :class="[
                {
                  viewOne: service_type == item.type,
                  viewBox: (index + 1) % 3 == 0
                }
              ]"
              @click="serviceFun(item.type)"
            >
              {{ item.name }}
            </view>
          </view>
        </view>
        <view>
          <view class="titleBox">产品类型</view>
          <view class="listBox">
            <view
              v-for="(item, index) in productFrom"
              :key="index"
              :class="[
                {
                  viewOne: type.indexOf(item.type) != -1,
                  viewBox: (index + 1) % 3 == 0
                }
              ]"
              @click="productFun(item.type)"
            >
              {{ item.name }}
            </view>
          </view>
        </view>
        <view>
          <view class="titleBox">课程分类</view>
          <view class="listBox">
            <view
              v-for="(item, index) in courseFrom"
              :key="index"
              :class="[
                {
                  viewOne: goods_type_id_string.indexOf(item.id) != -1,
                  viewBox: (index + 1) % 3 == 0
                }
              ]"
              @click="courseFun(item.id)"
            >
              {{ item.name }}
            </view>
          </view>
        </view>
      </view>
      <view class="footer">
        <view class="btns">
          <view class="left" @click="resetFun">重置</view>
          <view class="right" @click="confirmFun">确认</view>
        </view>
      </view>
    </view>
  </picker-shell>
</template>
<script>
import pickerShell from '../commen/picker-shell.vue'
import { homeApi } from '../../api/index'
export default {
  // 首页筛选
  name: 'search-type',
  props: {
    value: {
      type: Boolean,
      default: false
    }
  },
  components: {
    pickerShell
  },
  data() {
    return {
      teaching_type: '',
      teachingFrom: [
        {
          name: '直播',
          type: '1'
        },
        {
          name: '面授',
          type: '2'
        },
        {
          name: '录播',
          type: '3'
        }
      ],
      service_type: '',
      serviceFrom: [
        {
          name: '一年制',
          type: '1'
        },
        {
          name: '重读',
          type: '2'
        },
        {
          name: '不过退费',
          type: '3'
        }
      ],
      type: [],
      productFrom: [
        // {
        //   name: '课程',
        //   type: '1'
        // },
        {
          name: '课程', // 原套餐
          type: '2'
        },
        {
          name: '图书',
          type: '4'
        }
      ],
      goods_type_id_string: [],
      courseFrom: []
    }
  },
  created() {
    this.startFun()
  },
  methods: {
    startFun() {
      homeApi.getCourse({ type: '1' }).then(res => {
        this.courseFrom = res.data
      })
    },
    input(val) {
      this.$emit('input', val)
    },
    teachingFun(type) {
      if (this.teaching_type == type) {
        this.teaching_type = ''
        return
      }
      this.teaching_type = type
    },
    serviceFun(type) {
      if (this.service_type == type) {
        this.service_type = ''
        return
      }
      this.service_type = type
    },
    productFun(type) {
      let index = this.type.indexOf(type)
      if (index != -1) {
        type != '2' ? this.type.splice(index, 1) : this.type.splice(index, 2)
        return
      }
      if (type == '2') {
        this.type.push(type)
        this.type.push('3')
        return
      }
      this.type.push(type)
    },
    courseFun(type) {
      let index = this.goods_type_id_string.indexOf(type)
      if (index != -1) {
        this.goods_type_id_string.splice(index, 1)
        return
      }
      this.goods_type_id_string.push(type)
    },
    // 重置
    resetFun() {
      this.teaching_type = ''
      this.service_type = ''
      this.type = []
      this.goods_type_id_string = []
    },
    // 确认
    confirmFun() {
      let obj = {
        teaching_type: this.teaching_type,
        service_type: this.service_type,
        type: this.type.join(','),
        goods_type_id_string: this.goods_type_id_string.join(',')
      }
      this.$emit('confirmFun', obj)
    }
  }
}
</script>
<style scoped lang="less">
.screen {
  width: 100%;
  padding: 0 24rpx;
  display: flex;
  flex-direction: column;
  .top {
    padding: 32rpx 40rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 32rpx;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #03203d;
    img {
      width: 40rpx;
      height: 40rpx;
    }
  }
  .content {
    flex: 1;
    overflow-y: auto;
    padding-bottom: 200rpx;
    .titleBox {
      font-size: 24rpx;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: rgba(3, 32, 61, 0.65);
      margin-bottom: 24rpx;
    }
    .listBox {
      display: flex;
      flex-wrap: wrap;
      margin-bottom: 24rpx;
      view {
        width: 214rpx;
        // min-width: 214rpx;
        // padding: 0 74rpx;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        height: 68rpx;
        line-height: 68rpx;
        background: #f6f7f8;
        border-radius: 32px;
        text-align: center;
        padding: 0 15rpx;
        // display: flex;
        // align-items: center;
        // justify-content: center;
        font-size: 24rpx;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #03203d;
        margin-right: 30rpx;
        margin-bottom: 24rpx;
      }
      .viewOne {
        background: #ebf1ff;
        border-radius: 32rpx;
        border: 2rpx solid rgba(46, 104, 255, 0.5);
        color: #2e68ff;
      }
      .viewBox {
        margin-right: 0;
      }
    }
  }
  .footer {
    position: fixed;
    left: 0;
    bottom: 15rpx;
    width: 100%;
    height: 88rpx;
    padding: 0 24rpx;
    .btns {
      width: 100%;
      display: flex;
      height: 100%;
      border-radius: 44rpx;
      overflow: hidden;
      view {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 28rpx;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
      }
      .left {
        background: #eceef0;

        color: rgba(3, 32, 61, 0.85);
      }
      .right {
        background: #1469ff;
        color: #ffffff;
      }
    }
  }
}
</style>
