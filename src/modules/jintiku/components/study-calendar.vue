<template>
  <view class="study-calendar">
    <!-- 右上角装饰图片 -->
    <image
      class="decoration-image"
      src="https://yakaixin.oss-cn-beijing.aliyuncs.com/study-card-color.png"
    />
	
	<view class="check-in-status">
	  <image
	    class="check-icon"
	    src="https://yakaixin.oss-cn-beijing.aliyuncs.com/study-card-zan.png"
	    mode="aspectFit"
	  />
	  <text class="check-text">{{ ischecked?"今日已打卡":"今日未打卡"}}</text>
	</view>

    <view class="calendar-header">
        <text class="calendar-title">学习日历</text>
    </view>

    <view class="calendar-content">
      <view class="stats-row">
        <view class="stat-item">
          <text class="stat-number blue-number">{{ persistDays }}</text>
          <text class="stat-label">坚持天数</text>
        </view>

        <view class="stat-item">
          <text class="stat-number blue-number">{{ totalQuestions }}</text>
          <text class="stat-label">做题总数</text>
        </view>

        <view class="stat-item">
          <text class="stat-number red-number">{{ accuracyRate }}</text>
          <text class="stat-label">正确率%</text>
        </view>

        <view class="action-section">
          <view
            class="check-in-btn"
            :class="{ 'checked': ischecked }"
            @click="handleCheckIn"
          >
            <text :class="ischecked?'btn-text-checked':'btn-text'">{{ischecked?'已打卡':'打卡'}}</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'StudyCalendar',
  props: {
    // 坚持天数
    persistDays: {
      type: [Number, String],
      default: 31
    },
    // 做题总数
    totalQuestions: {
      type: [Number, String],
      default: 700
    },
    // 正确率
    accuracyRate: {
      type: [Number, String],
      default: 12
    },
    // 是否已打卡
    isCheckedIn: {
      type: Boolean,
      default: true
    }
  },
  data(){
	  return {
		  ischecked:false
	  }
  },
  methods: {
    handleCheckIn() {
      if (!this.isCheckedIn) {
        this.$emit('check-in')
		this.ischecked = true;
        uni.showToast({
          title: '打卡成功！',
          icon: 'success',
          duration: 2000
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.study-calendar {
font-family: PingFangSC-Medium, PingFang SC;
  background: white;
  border-radius: 32rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 20rpx rgba(46, 104, 255, 0.1);
  position: relative;
  overflow: hidden;

  // 右上角装饰图片
  .decoration-image {
    position: absolute;
    top: 0;
    right: 0;
    width: 260rpx;
    height: 64rpx;
    opacity: 0.8;
  }
  
  .check-in-status {
    display: flex;
    align-items: center;
	justify-content: center;
	position: absolute;
	top: 0;
	right: 0;
	width: 260rpx;
	height: 64rpx;
  
    .check-icon {
      width: 32rpx;
      height: 32rpx;
      margin-right: 8rpx;
    }
  
    .check-text {
      font-size: 24rpx;
      font-weight: 500;
    }
  }

  .calendar-header {
    margin-bottom: 32rpx;
    position: relative;
    z-index: 2;
	display: flex;
	align-items: center;     

      .calendar-title {
        font-size: 32rpx;
        font-weight: 600;
        color: #333;
        line-height: 1.2;
      }
    
  }

  .calendar-content {
    position: relative;
    z-index: 2;

    .stats-row {
      display: flex;
      align-items: flex-end;
      justify-content: space-between;

      .stat-item {
        text-align: center;

        .stat-number {
          display: block;
          font-size: 48rpx;
          font-weight: 700;
          line-height: 1.2;
          margin-bottom: 8rpx;

          &.blue-number {
            color: #2E68FF;
          }

          &.red-number {
            color: #FF6B6B;
          }
        }

        .stat-label {
          display: block;
          font-size: 30rpx;
          color: #666;
          font-weight: 400;
          line-height: 1.2;
		  margin-top: 10rpx;
        }
      }

      .action-section {
        .check-in-btn {
          background: linear-gradient(135deg, #FF860E 0%, #FF6912 100%);
          border-radius: 35rpx;
		  width: 140rpx;
		  height:70rpx;
          box-shadow: 0 4rpx 16rpx rgba(255, 138, 0, 0.3);
          transition: all 0.3s ease;
		  display: flex;
		  flex-direction: row;
		  justify-content: center;
		  align-items: center;
		  
          &:active {
            transform: scale(0.95);
          }

          &.checked {
            background: #E0F0FF;
            box-shadow: 0 4rpx 16rpx rgba(64, 185, 131, 0.3);
          }

          .btn-text {
            font-size: 28rpx;
            color: #fff;
          }
		  .btn-text-checked {
		    font-size: 28rpx;
		    color: #018BFF;
		  }
        }
      }
    }
  }
}

// 响应式适配
@media screen and (max-width: 750rpx) {
	.calendar-header {
	  margin-bottom: 24rpx;
	  
	  .title-section {
	    .calendar-title {
	      font-size: 32rpx;
	    }
	    
	    .check-in-status {
	      .check-icon {
	        width: 28rpx;
	        height: 28rpx;
	      }
	      
	      .check-text {
	        font-size: 22rpx;
	      }
	    }
	  }
	}
  .study-calendar {
    margin: 16rpx;
    padding: 24rpx;
    
    .calendar-content {
      .stats-row {
        margin-bottom: 20rpx;
        
        .stat-item {
          .stat-number {
            font-size: 40rpx;
          }
          
          .stat-label {
            font-size: 22rpx;
          }
        }
      }
      
      .action-section {
        .check-in-btn {
          
          .btn-text {
            font-size: 26rpx;
          }
        }
      }
    }
  }
}
</style>
