<template>
  <view class="uview-test">
    <view class="test-section">
      <text class="section-title">uView 组件测试</text>
      
      <!-- 测试 u-tag 组件 -->
      <view class="tag-test">
        <text class="test-label">u-tag 测试：</text>
        <u-tag text="测试标签1" type="primary"></u-tag>
        <u-tag text="测试标签2" type="warning" plain></u-tag>
        <u-tag text="测试标签3" type="success" size="mini"></u-tag>
      </view>
      
      <!-- 测试 u-button 组件 -->
      <view class="button-test">
        <text class="test-label">u-button 测试：</text>
        <u-button text="测试按钮" type="primary" size="mini"></u-button>
      </view>
      
      <!-- 自定义标签作为对比 -->
      <view class="custom-test">
        <text class="test-label">自定义标签：</text>
        <view class="custom-tag">自定义1</view>
        <view class="custom-tag success">自定义2</view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'UviewTest',
  data() {
    return {}
  },
  mounted() {
    console.log('UviewTest 组件已挂载')
  }
}
</script>

<style lang="scss" scoped>
.uview-test {
  padding: 40rpx;
  background: #f5f6f7;
  min-height: 100vh;
  
  .test-section {
    background: #fff;
    border-radius: 16rpx;
    padding: 32rpx;
    
    .section-title {
      font-size: 32rpx;
      font-weight: 600;
      color: #333;
      margin-bottom: 40rpx;
      display: block;
    }
    
    .tag-test,
    .button-test,
    .custom-test {
      margin-bottom: 32rpx;
      
      .test-label {
        display: block;
        font-size: 28rpx;
        color: #666;
        margin-bottom: 16rpx;
      }
    }
    
    .custom-tag {
      display: inline-block;
      background: #2979ff;
      color: #fff;
      padding: 8rpx 16rpx;
      border-radius: 8rpx;
      font-size: 24rpx;
      margin-right: 16rpx;
      
      &.success {
        background: #19be6b;
      }
    }
  }
}
</style>
