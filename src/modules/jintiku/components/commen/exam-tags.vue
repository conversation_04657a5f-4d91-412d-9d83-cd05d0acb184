<template>
  <view class="tags">
    <!-- 调试信息 -->
    <view class="ee" style="background: #ff0000; color: #fff;">
      DEBUG: {{ item.name || 'No item' }}
    </view>

    <!-- 秒杀标签 -->
    <view :class="seckill ? 'ee ee-seckill-q' : 'ee'">
      <view class="ee-seckill-q-row" v-if="seckill">
        共 <view style="color: red;">
          {{ item.tiku_goods_details && item.tiku_goods_details.question_num }}
        </view>
        题
      </view>
      <view v-else>
        {{ info.num_text || '默认文本' }}
      </view>
    </view>

    <!-- 类型8的题目数量标签 -->
    <view v-if="item.type == 8" class="ee ee-tag-class">
      共{{ item.tiku_goods_details && item.tiku_goods_details.question_num }}题
    </view>

    <!-- 权限状态为2的月份标签 -->
    <view class="ee ee-tag-class" v-if="item.permission_status == '2'">
      {{ info.month_text }}
    </view>

    <!-- 权限状态为1的有效期标签 -->
    <view class="ee" v-if="item.permission_status == '1'">
      {{ validityText }}
    </view>
  </view>
</template>

<script>
export default {
  name: 'ExamTags',
  props: {
    // 考试项目数据
    item: {
      type: Object,
      required: true,
      default: () => ({})
    },
    // 信息数据
    info: {
      type: Object,
      required: true,
      default: () => ({})
    },
    // 是否为秒杀
    seckill: {
      type: Boolean,
      default: false
    },
    // 有效期计算函数的结果（从父组件传入）
    validityText: {
      type: String,
      default: ''
    }
  },
  mounted() {
    // 调试信息
    console.log('ExamTags mounted:', {
      item: this.item,
      info: this.info,
      seckill: this.seckill,
      validityText: this.validityText
    })
  },
  computed: {
    // 如果需要在组件内部计算有效期，可以使用这个计算属性
    computedValidityText() {
      if (this.validityText) {
        return this.validityText
      }
      // 这里可以添加默认的有效期计算逻辑
      return this.getDefaultValidity()
    }
  },
  methods: {
    // 默认的有效期计算方法
    getDefaultValidity() {
      if (!this.item) return ''
      
      // 这里添加默认的有效期计算逻辑
      // 可以根据实际业务需求来实现
      if (this.item.validity_time) {
        const now = new Date()
        const validityDate = new Date(this.item.validity_time)
        const diffTime = validityDate - now
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
        
        if (diffDays > 0) {
          return `剩余${diffDays}天`
        } else {
          return '已过期'
        }
      }
      
      return ''
    }
  }
}
</script>

<style lang="scss" scoped>
.tags {
  display: flex;
  padding-bottom: 24rpx;

  .ee {
    margin-right: 12rpx;
    height: 40rpx;
    line-height: 40rpx;
    background: #f5f6fa;
    border-radius: 8rpx;
    padding: 0 16rpx;
    font-size: 20rpx;
    color: rgba(44, 55, 61, 0.71);
    margin-top: 16rpx;
  }
  
  .ee:nth-child(1) {
    background: #ebf1ff;
    color: #2e68ff;
  }
}

.ee-seckill-q {
  background: #FFD27C !important;
}

.ee-seckill-q-row {
  display: flex;
  flex-direction: row;
  font-weight: 600;
  color: black;
}

.ee-tag-class {
  color: #4981D7 !important;
  border: 1rpx solid #4981D7 !important;
  background: transparent !important;
}
</style>
