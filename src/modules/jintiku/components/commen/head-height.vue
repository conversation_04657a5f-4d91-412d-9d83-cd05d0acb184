<template>
  <view
    class="head-hei"
    :style="{
      height: height + 'px'
    }"
  ></view>
</template>
<script>
export default {
  props: {
    statusBarHeight: {}
  },
  data() {
    return {
      height: 0
    }
  },
  created() {
    let that = this
    uni.getSystemInfo({
      success(data) {
        // let menuButtonInfo = uni.getMenuButtonBoundingClientRect()
        that.height = data.statusBarHeight
        // + menuButtonInfo.height
        that.$emit('update:statusBarHeight', that.height)
      }
    })
  }
}
</script>

<style scoped lang="less">
// .head-hei {
// height: 140rpx;
// }
</style>
