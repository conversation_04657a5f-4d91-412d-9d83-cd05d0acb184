<template>
  <view class="loginH5">
    <view class="img">
      <image
        src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/16954380355842bbc169543803558573934_%E5%AE%89%E5%8D%93%402x.png"
        alt=""
      />
    </view>
    <view class="tabs">
      <view class="tab" :class="{ active: current == 0 }" @click="current = 0">
        手机注册登录
      </view>
      <!-- <view class="tab" :class="{ active: current == 1 }" @click="current = 1">
        密码登录
      </view> -->
    </view>
    <swiper
      class="swiper"
      :indicator-dots="false"
      :autoplay="false"
      :current="current"
      :duration="300"
      :circular="false"
      @change="swiperChange"
    >
      <swiper-item :key="1">
        <view class="swiper-item">
          <view class="input">
            <image
              src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/16981168209892506169811682099042826_person.png"
              mode="widthFix"
              class="icon"
            />
            <input
              type="text"
              class="phone"
              placeholder="请输入11位手机号码"
              v-model="form.phone"
            />
          </view>
          <view class="input">
            <image
              src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/1698116851191468616981168511918397_dun.png"
              mode="widthFix"
              class="icon"
            />
            <input
              type="text"
              class="phone"
              placeholder="6位短信验证码"
              v-model="form.code"
            />
            <view class="btn" :class="{ disabled: disabled }" @click="getCode">
              {{ text }}
            </view>
          </view>
          <view class="login-btn button" @click="loginFn"> 登录 </view>
          <view class="xieyi">
            <u-radio-group v-model="agree">
              <u-radio
                shape="circle"
                label="点击登录或完成账号注册表示您已阅读并同意牙开心"
                labelColor="#C4CAD1"
              ></u-radio>
            </u-radio-group>
            <text class="desc">《牙开心用户服务协议》</text>
            <text class="desc">《隐私服务》</text>
          </view>
        </view>
      </swiper-item>
      <!-- <swiper-item :key="2">
        <view class="swiper-item">
          <view class="input">
            <image
              src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/16981168209892506169811682099042826_person.png"
              mode="widthFix"
              class="icon"
            />
            <input type="text" class="phone" placeholder="请输入11位手机号码" />
          </view>
          <view class="input">
            <image
              src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/1698116851191468616981168511918397_dun.png"
              mode="widthFix"
              class="icon"
            />
            <input type="password" class="phone" placeholder="请输入密码" />
          </view>
          <view class="login-btn button"> 登录 </view>
          <view class="xieyi">
            <u-radio-group v-model="agree">
              <u-radio
                shape="circle"
                label="点击登录或完成账号注册表示您已阅读并同意牙开心"
                labelColor="#C4CAD1"
              ></u-radio>
            </u-radio-group>
            <text class="desc">《牙开心用户服务协议》</text>
            <text class="desc">《隐私服务》</text>
          </view>
        </view>
      </swiper-item> -->
    </swiper>
  </view>
</template>
<script>
import { sendCode } from '../../api/h5Active'
export default {
  data() {
    return {
      current: 0,
      agree: false,
      text: '获取验证码',
      time: 60,
      disabled: false,
      // 验证码信息
      form: {
        code: '',
        phone: ''
      }
    }
  },
  methods: {
    swiperChange(e) {
      this.current = e.detail.current
    },
    setTime() {
      if (this.time <= 0) {
        this.time = 60
        this.text = '重新获取验证码'
        this.disabled = false
        return
      }
      this.time--
      this.text = `${this.time}s后重新发送`
      let timer = setTimeout(() => {
        clearTimeout(timer)
        this.setTime()
      }, 1000)
    },
    getCode() {
      if (!this.form.phone) {
        this.$xh.Toast('请输入手机号！')
        return
      }
      if (!this.form.phone.match(this.$xh.getRegExp().getRegExp)) {
        this.$xh.Toast('请输入正确的手机号！')
        return
      }
      if (this.disabled) {
        return
      }
      this.disabled = true
      this.setTime()
      sendCode({
        phone: this.form.phone,
        scene: 2
        // isysys: '4'
      }).then(() => {
        this.$xh.Toast('发送成功！')
      })
    },
    loginFn() {
      let brand_id = uni.getStorageSync('brand_id')
      let merchant_id = uni.getStorageSync('merchant_id')
      this.$store
        .dispatch('jintiku/CODELOGIN', {
          phone: this.form.phone,
          code: this.form.code,
          brand_id,
          merchant_id,
          channel_id: process.env.VUE_APP_CHANNELID,
          extendu_id: process.env.VUE_APP_EXTENDUID,
          need_employee_info: 1
        })
        .then(() => {
          // this.$store.dispatch('jintiku/SETMENUS').then(() => {
          //   this.$emit('success')
          // })
          this.$emit('success')
        })
        .catch(err => {
          this.$emit('fail')
          this.$xh.Toast('登录失败！')
        })
    }
  }
}
</script>
<style scoped lang="less">
.loginH5 {
  height: 100vh;
  background-color: #fff;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0 40rpx;
  padding-top: 120rpx;
  .img {
    margin-bottom: 118rpx;
    image {
      width: 120rpx;
      height: 120rpx;
    }
  }
  .tabs {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    margin-bottom: 56rpx;
    .tab {
      font-size: 36rpx;
      font-family: PingFangSC-Medium, PingFang SC;
      color: #42586e;
      line-height: 64rpx;
    }
    .active {
      font-weight: 800;
      color: #1469ff;
    }
  }
  .swiper {
    height: 1000rpx;
    width: 100%;
    .swiper-item {
      height: 1000rpx;
      .input {
        width: 100%;
        height: 92rpx;
        border-bottom: 1px solid #d9dee2;
        display: flex;
        align-items: center;
        margin-bottom: 70rpx;
        position: relative;
        .icon {
          width: 32rpx;
          height: 32rpx;
          margin-right: 24rpx;
        }
        .phone {
          font-size: 32rpx;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #42586e;
          line-height: 40rpx;
        }
        .btn {
          position: absolute;
          right: 0;
          top: 0;
          bottom: 0;
          line-height: 92rpx;
          margin: auto 0;
          font-size: 32rpx;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #1469ff;
        }
        .disabled {
          color: #ccc;
        }
      }
      .login-btn {
        width: 100%;
        height: 92rpx;
        line-height: 92rpx;
        background: #1469ff;
        border-radius: 46rpx;
        font-size: 36rpx;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 800;
        color: #ffffff;
        text-align: center;
        margin-top: 160rpx;
        margin-bottom: 40rpx;
      }
      .desc {
        font-size: 24rpx;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #1469ff;
      }
    }
  }
}
</style>
