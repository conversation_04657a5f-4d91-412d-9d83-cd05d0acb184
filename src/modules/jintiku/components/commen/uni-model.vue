<template>
  <view class="Models" v-if="value">
    <view
      class="content animated"
      :class="{ bounceIn: value, bounceOut: close }"
    >
      <view class="title"> {{ title }} </view>
      <view class="desc"> {{ desc }} </view>
      <view class="btns">
        <view class="btn cancel flex-center" @click="cancelFn">
          {{ cancel }}
        </view>
        <view class="btn sure flex-center" @click="successFn">{{ sure }}</view>
      </view>
    </view>
  </view>
</template>
<script>
export default {
  props: {
    value: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: ''
    },
    desc: {
      type: String,
      default: ''
    },
    sure: {
      type: String,
      default: '确定'
    },
    cancel: {
      type: String,
      default: '取消'
    }
  },
  components: {},
  data() {
    return {
      close: false
    }
  },
  created() {},
  methods: {
    cancelFn() {
      this.close = true
      setTimeout(() => {
        this.$emit('input', false)
        this.$emit('cancel', false)
        this.close = false
      }, 1000)
    },
    successFn() {
      this.$emit('success')
    }
  },
  onLoad() {}
}
</script>
<style scoped lang="less">
.Models {
  position: fixed;
  z-index: 5000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.4);
  display: flex;
  align-items: center;
  justify-content: center;
  .content {
    width: 538rpx;
    min-height: 348rpx;
    background-color: #fff;
    border-radius: 16rpx;
    padding: 40rpx;
    .title {
      height: 48rpx;
      font-size: 32rpx;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #03203d;
      line-height: 48rpx;
      margin-bottom: 44rpx;
      text-align: center;
      font-weight: 800;
    }
    .desc {
      font-size: 28rpx;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: rgba(3, 32, 61, 0.75);
      line-height: 48rpx;
      margin-bottom: 48rpx;
      text-align: center;
    }
    .btns {
      display: flex;
      align-items: center;
      justify-content: center;
      .btn {
        width: 204rpx;
        height: 72rpx;
        background: #2e68ff;
        border-radius: 36rpx;
        color: #fff;
        margin-right: 50rpx;
        border: 2rpx solid transparent;
        font-size: 26rpx;
      }
      .btn:last-child {
        margin-right: 0;
        background: #fff;
        border: 2rpx solid rgba(3, 32, 61, 0.25);
        color: rgba(3, 32, 61, 0.75);
      }
    }
  }
}
</style>
