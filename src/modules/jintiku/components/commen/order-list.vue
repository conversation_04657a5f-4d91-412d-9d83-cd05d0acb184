<template>
  <view>
    <scroll-view
      scroll-y
      refresher-enabled
      :refresher-triggered="triggered"
      @scrolltolower="getList()"
      @refresherrefresh="refresherrefresh()"
      class="scroll-view"
    >
      <view class="list" style="padding: 32rpx 24rpx 24rpx 24rpx">
        <view
          v-for="(item, index) in list"
          :key="index"
          class="item"
          @click="paySuccess(item)"
        >
          <view
            class="pay-time"
            v-if="item.status == '1' && item.countdown > 0"
          >
            <!-- <view>29:00:00</view> -->
            <u-count-down
              :time="item.countdown * 1000"
              format="HH:mm:ss"
              millisecond
              @finish="refresherrefresh()"
            ></u-count-down>
          </view>
          <view class="order-info">
            <view class="order_no">
              <text>{{ item.order_no }}</text>
              <view class="copy" @click.stop="copy(item.order_no)">复制</view>
            </view>
            <view class="status">{{ item.status_name }}</view>
          </view>
          <view class="header">
            <view class="name">{{ item.goods_name }}</view>
          </view>

          <view class="tags">
            <view class="ee">{{ item.num_text }}</view>

            <view class="ee"> {{ item.month_text }}</view>
          </view>
          <view class="path"> {{ item.tips }} </view>
          <view class="bottom">
            <view class="price-label"> 共1件商品，实付款： </view>
            <view class="price-num"> ¥ {{ item.payable_amount }} </view>
          </view>
          <view class="bottom-but" v-if="item.status == '1'">
            <!-- <view class="but" @click="clearOrder(item)">取消订单 </view> -->
            <view class="but" @click="getPayModeListNew(item)">去支付 </view>
          </view>
        </view>

        <view class="not_data" v-if="list.length == 0">
          <view class="img">
            <img
              src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/16954369620338446169543696203498545_%E7%BC%96%E7%BB%84%402x%20(4).png"
              alt=""
            />
          </view>
          <view class="desc">暂无订单！</view>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script>
import {
  orderList,
  wechatapplet,
  payModeListNew,
  payModeListNewDetail
} from '../../api/index'
import { app_id } from '../../config'
export default {
  components: {},
  props: {
    status: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      list: [],
      page: 1,
      size: 20,
      triggered: false,
      end: false
    }
  },
  created() {
    this.getList()
    // this.getList2()
  },
  methods: {
    paySuccess(item) {
      if (item.status == '2') {
        this.$xh.push(
          'jintiku',
          `pages/order/paySuccess` +
            `?goods_id=${item.goods_id}&professional_id_name=${item.professional_id_name}`
        )
      }
    },
    refresherrefresh() {
      this.list = []
      this.page = 1
      this.end = false
      this.triggered = true
      setTimeout(() => {
        this.triggered = false
      }, 200)
      this.getList()
    },
    getList() {
      if (this.end) {
        return
      }
      orderList({
        size: this.size,
        page: this.page++,
        only_tiku_order: '1',
        status: this.status
      }).then(res => {
        if (res.data?.list?.length) {
          this.list = this.list.concat(
            res.data.list.map(item => {
              let info = item
              let num_text = `共${info.tiku_goods_details.question_num}题`
              if (info.goods_type == 8) {
                num_text = `共${info.tiku_goods_details.paper_num}份`
              }
              if (info.goods_type == 10) {
                num_text = `共${info.tiku_goods_details.exam_round_num}轮`
              }
              let system_id_name = info?.teaching_system?.system_id_name || ''
              if (info.goods_type == 10) {
                system_id_name = `开考时间:${info.tiku_goods_details.exam_time}`
              }
              let month_text = ''
              if (info.months == 0) {
                month_text = '永久'
              } else {
                month_text = info.months + '个月'
              }
              item.month_text = month_text
              item.tips = system_id_name
              item.num_text = num_text

              return item
            })
          )
        } else {
          this.end = true
        }
      })
    },
    copy(str) {
      console.log(str)
      uni.setClipboardData({
        data: str,
        success: function () {
          uni.hideToast() //关闭自带的toast
          uni.showToast({
            title: '复制成功',
            icon: 'none'
          })
        },
        fail: err => {
          console.log(err)
        }
      })
    },
    clearOrder(item) {
      uni.showModal({
        title: '提示',
        content: '确定取消订单',
        success(res) {
          if (res.confirm) {
            console.log('用户点击确定')
          } else if (res.cancel) {
            console.log('用户点击取消')
          }
        }
      })
    },
    wechatapplet(flow_id, finance_body_id, goods_id, professional_id_name) {
      let openid = uni.getStorageSync('__xingyun_weixinInfo__').openid
      wechatapplet({
        flow_id: flow_id,
        wechat_app_id: app_id,
        open_id: openid,
        finance_body_id: finance_body_id
      }).then(res => {
        const payParams = {
          appId: app_id,
          timeStamp: res.data.time_stamp,
          nonceStr: res.data.nonce_str,
          signType: res.data.sign_type,
          paySign: res.data.pay_sign,
          package: res.data.package
        }

        this.$xh.pay(
          payParams,
          () => {
            this.$xh.push(
              'jintiku',
              `pages/order/paySuccess` +
                `?goods_id=${goods_id}&professional_id_name=${professional_id_name}`
            )
            this.refresherrefresh()
          },
          () => {
            this.$xh.Toast('支付失败！')
          }
        )
      })
    },
    getPayModeListNew(obj) {
      let userinfo = uni.getStorageSync('__xingyun_userinfo__') || {}
      payModeListNew({
        account_use: 1, // 1收款 2付款
        is_match: 1,
        is_usable: 1,
        page: 1,
        size: 100,
        account_type: 1, // 1:线上支付 2:线下支付
        order_id: obj.order_id || obj.id,
        goods_ids: obj.goods_id,
        merchant_id: userinfo.merchant[0].merchant_id,
        brand_id: userinfo.merchant[0].brand_id,
        collection_scene: 1,
        collection_terminal: 8
      }).then(response => {
        console.log('财务主体列表', response?.data?.list)
        if (response?.data?.list?.length > 0) {
          let flag_row = response?.data?.list?.find(item => {
            return item?.pay_method == '6' && app_id == item?.wechat_pay_app_id
          })
          console.log(flag_row, 'flag_row')
          if (flag_row) {
            payModeListNewDetail({
              id: flag_row?.id
            }).then(value => {
              console.log('获取App_id', value?.data)
              this.wechatapplet(
                obj.flow_id,
                value?.data?.id,
                obj.goods_id,
                obj.professional_id_name
              )
            })
          }
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.not_data {
  width: 100%;
  padding-top: 35vh;
  .img {
    width: 229rpx;
    height: 180rpx;
    margin: 0 auto;
    img {
      width: 100%;
      height: 100%;
    }
  }
  .desc {
    text-align: center;
    font-size: 26rpx;
    margin-top: 40rpx;
    color: #ccc;
  }
}
.list {
  .item {
    background: #ffffff;
    width: 100%;
    padding: 24rpx 32rpx 0rpx 32rpx;
    border-radius: 12rpx;
    box-shadow: 0 0 30rpx rgba(27, 38, 55, 0.06);
    margin-bottom: 24rpx;
    .pay-time {
      width: 292rpx;
      height: 44rpx;
      background-size: 292rpx 44rpx;
      background-image: url(https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/4d97173977183148137274_timeback.png);
      display: flex;
      justify-content: flex-end;
      margin-bottom: 32rpx;

      // view {
      //   font-weight: 500;
      //   text-align: center;
      //   font-size: 12px;
      //   color: #2e68ff;
      //   height: 44rpx;
      //   line-height: 44rpx;
      //   width: 160rpx;
      // }
    }
    .order-info {
      display: flex;
      justify-content: space-between;
      padding-bottom: 32rpx;
      .status {
        font-weight: 400;
        font-size: 12px;
        color: rgba(3, 32, 61, 0.55);
        flex-shrink: 0;
      }
      .order_no {
        display: flex;
        text {
          font-weight: 400;
          font-size: 12px;
          color: rgba(3, 32, 61, 0.65);
        }
        .copy {
          margin-left: 16rpx;
          font-size: 20rpx;
          color: rgba(3, 32, 61, 0.75);
          text-align: center;
          width: 64rpx;
          height: 34rpx;
          line-height: 34rpx;
          background: rgba(3, 32, 61, 0.08);
          border-radius: 18rpx;
        }
      }
    }
    .header {
      display: flex;
      justify-content: space-between;
    }
    .price {
      display: flex;
      align-items: baseline;
      color: #ff5e00;
      flex-shrink: 0;
      .fuhao {
        font-weight: 500;
        font-size: 12px;
      }
      .jine {
        font-weight: 800;
        font-size: 16px;
      }
    }
    .bottom {
      border-top: 1px solid #e8e9ea;
      height: 76rpx;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .price-label {
        font-weight: 600;
        font-size: 12px;
        color: #29415a;
      }
      .price-num {
        font-weight: 800;
        font-size: 14px;
        color: #ff5430;
      }
    }
    .bottom-but {
      // height: 100rpx;
      padding-bottom: 32rpx;
      display: flex;
      justify-content: flex-end;
      align-items: center;
      .but {
        width: 158rpx;
        text-align: center;
        height: 60rpx;
        line-height: 58rpx;
        border-radius: 44rpx;
        border: 1rpx solid #f83131;
        font-weight: 400;
        font-size: 24rpx;
        color: #ff5430;
        margin-left: 16rpx;
      }
      .but:nth-child(1) {
        color: rgba(3, 32, 61, 0.85);
        border: 1px solid rgba(3, 32, 61, 0.4);
      }
    }
    .name {
      font-weight: 600;
      font-size: 15px;
      color: #212121;
    }
    .tags {
      display: flex;
      margin-bottom: 24rpx;
      .ee {
        // width: 36px;
        margin-right: 12rpx;
        height: 34rpx;
        line-height: 34rpx;
        background: #f5f6fa;
        border-radius: 4rpx;
        padding: 0 12rpx;
        font-size: 20rpx;
        color: rgba(44, 55, 61, 0.71);
        margin-top: 16rpx;
      }
      .ee:nth-child(1) {
        background: #ebf1ff;
        color: #2e68ff;
      }
    }
    .path {
      font-size: 12px;
      color: rgba(3, 32, 61, 0.65);
      padding-bottom: 24rpx;
    }
  }
}

.scroll-view {
  height: calc(100vh - 100rpx);
  // overflow-y: auto;
  .content {
    padding: 32rpx;
  }
}
</style>
