<template>
  <view
    :style="{
      opacity: show ? '1' : '0',
      'pointer-events': show ? '' : 'none'
    }"
    class="Models"
    @click="cancelFn"
  >
    <view
      class="content"
      :class="{ show: show, hide: close }"
      :style="{ height }"
      @click.stop="() => {}"
    >
      <view class="title" v-if="title">
        <text>
          {{ title }}
        </text>
      </view>
      <view class="title-copy" v-if="title"></view>
      <view class="close" @click="cancelFn" v-if="isclosed">
        <image
          src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/169477114446470a0169477114446449557_clear.png"
          mode="widthFix"
        />
      </view>
      <view class="desc">
        <slot />
      </view>
    </view>
  </view>
</template>
<script>
export default {
  props: {
    value: {
      type: Boolean,
      default: false
    },
    // 高度
    height: {
      type: String,
      default: '870rpx'
    },
    title: {
      type: String,
      default: ''
    },
    // 是否有关闭按钮 或者 是否可关闭
    isclosed: {
      type: Boolean,
      default: true
    }
  },
  components: {},
  data() {
    return {
      close: false,
      show: false
    }
  },
  created() {},
  watch: {
    value(val) {
      if (val) {
        this.show = true
      } else {
        this.cancelFn()
      }
    }
  },
  methods: {
    cancelFn() {
      if (!this.isclosed) {
        // 不可关闭
        return
      }
      this.close = true
      setTimeout(() => {
        this.$emit('input', false)
        this.close = false
        this.show = false
      }, 250)
    },
    successFn() {
      this.$emit('success')
    }
  },
  onLoad() {}
}
</script>
<style scoped lang="less">
.Models {
  position: fixed;
  z-index: 6001;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.4);
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  transition: all 0.25s;
  transform-origin: bottom center;
  .content {
    width: 100%;
    background-color: #fff;
    border-radius: 24rpx 24rpx 0px 0px;
    position: absolute;
    left: 0;
    bottom: 0;
    transition: all 0.25s;
    transform: translateY(100%);
    overflow-y: hidden;
    display: flex;
    flex-direction: column;
    .title {
      height: 88rpx;
      line-height: 118rpx;
      font-size: 32rpx;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 600;
      color: #03203d;
      text-align: center;
      font-weight: 800;
      position: absolute;
      left: 0;
      top: 0;
      text-align: center;
      width: 100%;
    }
    .title-copy {
      height: 88rpx;
      width: 100%;
    }
    .close {
      position: absolute;
      right: 40rpx;
      top: 32rpx;
      width: 24rpx;
      height: 24rpx;
      image {
        width: 24rpx;
        height: 24rpx;
      }
    }
    .desc {
      font-size: 28rpx;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: rgba(3, 32, 61, 0.75);
      line-height: 48rpx;
      // margin-bottom: 48rpx;
      overflow-y: scroll;
      flex: 1;
    }
  }
  .content.show {
    transform: translateY(0%);
  }
  .content.hide {
    transform: translateY(100%) !important;
  }
}
</style>
