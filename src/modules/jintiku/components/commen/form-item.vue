<template>
  <view
    class="form-item"
    :class="{
      left: labelPosition == 'left',
      top: labelPosition == 'top',
      'val-r': valuePos == 'right'
    }"
  >
    <div class="form-item-label">{{ label }}</div>
    <div class="form-item-content">
      <slot></slot>
      <image v-if="isPushIcon" class="push-icon-src" :src="pushIconSrc"></image>
    </div>
  </view>
</template>

<script>
export default {
  props: {
    label: {
      type: String,
      default: ''
    },
    labelPosition: {
      type: String,
      default: 'left'
    },
    isPushIcon: {
      type: Boolean,
      default: false
    },
    valuePos: {
      type: String,
      default: 'left'
    }
  },
  data() {
    return {
      pushIconSrc:
        'https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/16990649123925c6b16990649123927441_pushIcon.png'
    }
  }
}
</script>

<style lang="scss">
.form-item {
  display: flex;
  // align-items: center;
  // height: 80rpx;
  // padding: 8rpx 0;
  width: 100%;
  margin-bottom: 32rpx;
  .form-item-label {
    flex-shrink: 0;
    min-height: 64rpx;
    line-height: 64rpx;
    color: rgba(3, 32, 61, 0.65);
    font-size: 28rpx;
  }
  .form-item-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    min-height: 64rpx;

    .push-icon-src {
      flex-shrink: 0;
      width: 32rpx;
      height: 32rpx;
      margin-left: 16rpx;
    }
  }
  picker {
    width: 100%;
  }
  input,
  k-select-code,
  k-select-job,
  k-select-salary,
  k-select,
  k-select-city,
  k-select-date,
  k-select-date2 {
    font-weight: 500;
    font-size: 28rpx;
    color: #111a37;
    width: 100%;
  }
  .slot-default {
    width: 100%;
  }
}
.left {
  align-items: center;
  .form-item-content {
    flex: 1;
    display: flex;
    align-items: center;
    input {
      height: 64rpx;
      padding-left: 20rpx;
      // border: 1px solid #d8dde1;
    }
  }
}
.top {
  display: flex;
  flex-direction: column;
  .form-item-label {
    flex-shrink: 0;
    min-height: 44rpx;
    line-height: 44rpx;
    color: rgba(3, 32, 61, 0.65);
    font-size: 28rpx;
    margin-bottom: 20rpx;
  }
  .form-item-content {
    border-bottom: 1px solid #d8dde1;
    padding-bottom: 32rpx;
    input {
      height: 44rpx;
    }
  }
}
.val-r {
  input {
    text-align: right;
  }
}
</style>
