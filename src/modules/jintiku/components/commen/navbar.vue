<template>
  <view>
    <view :style="statusBarHeight"></view>
    <view style="height: 50px"></view>
    <view class="navigation">
      <view :style="statusBarHeight"></view>
      <view class="navbar">
        <view class="back" @click="back">
          <image
            class="navbarback"
            src="/static/imgs/jintiku/navbarback.png"
          ></image>
        </view>
        <view class="title">{{ title }}</view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  props: {
    title: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      statusBarHeight: 'height:25px;'
    }
  },
  created() {
    let BarHeight = wx.getSystemInfoSync().statusBarHeight || 25
    this.statusBarHeight = `height:${BarHeight}px;`
  },
  methods: {
    back() {
      uni.navigateBack({
        delta: 1,
        fail() {
          uni.switchTab({
            url: '/modules/jintiku/pages/index/index'
          })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.navigation {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  background: #fff;
  z-index: 1;
}
.navbar {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 50px;
  position: relative;
  .title {
    font-weight: 400;
    font-size: 16px;
    color: #000000;
  }
  .back {
    position: absolute;
    left: 0;
    padding-left: 16px;
    height: 100%;
    display: flex;
    align-items: center;
  }
  .navbarback {
    width: 16px;
    height: 20px;
  }
}
</style>
