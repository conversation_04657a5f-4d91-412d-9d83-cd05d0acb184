<!-- 组件内容 -->
<template>
  <view class="audio-page">
    <view class="box-left">
      <view class="page-btn" @click="clickAudio">
        <image :src="music_play ? stop_img : start_img" mode="widthFix"></image>
      </view>
    </view>
    <view class="box-content">
      <view class="progress">
        <text>{{ getMinuteTime(now_time) }}</text>
        <slider
          :value="(now_time / total_time) * 100"
          block-size="10"
          block-color="#2e68ff"
          activeColor="#2e68ff"
          @change="sliderChange"
        ></slider>
        <text>{{ getMinuteTime(total_time) }}</text>
      </view>
    </view>
  </view>
</template>
<script>
export default {
  name: 'WZSAudio',
  props: ['music', 'image', 'title'],
  data() {
    return {
      music_play: false,
      AUDIO: '',
      total_time: 0,
      now_time: 0,
      timeupdata: '',
      interval: '',
      start_img:
        'https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/832c174220650008492795_%E4%B8%8B%E8%BD%BD.png',
      stop_img:
        'https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/919617422065663948977_%E4%B8%8B%E8%BD%BD%20(1).png',
      intervalID: null
    }
  },
  watch: {
    music: {
      handler(nv, ov) {
        this.cleanMusic()
        this.playAudio()
      },
      immediate: true
    }
  },
  methods: {
    // 播放音乐
    playAudio() {
      this.AUDIO = uni.createInnerAudioContext()
      // #ifdef H5
      // this.AUDIO = uni.createInnerAudioContext()
      // #endif
      // #ifdef APP
      // this.AUDIO =
      //   uni.getSystemInfoSync().platform == 'ios'
      //     ? uni.getBackgroundAudioManager()
      //     : uni.createInnerAudioContext()
      // #endif
      this.AUDIO.src = this.music
      console.log(this.music)
      this.AUDIO.onCanplay(() => {
        this.intervalID = setInterval(() => {
          console.log('定时器', this.AUDIO.duration)
          if (this.AUDIO.duration !== 0) {
            clearInterval(this.intervalID) // 清除定时器
            this.intervalID = null
            if (this.AUDIO.duration != 0 && !isNaN(this.AUDIO.duration)) {
              this.total_time = Math.round(this.AUDIO.duration)
              clearInterval(this.interval)
            }
          }
        }, 500)
        console.log(this.AUDIO.duration) // =>0
      })
      this.AUDIO.onError(res => {
        console.log(res.errMsg)
        console.log(res.errCode)
      })
      this.timeupdata = setInterval(() => {
        console.log('定时器')
        if (this.music_play) {
          this.now_time++
          if (this.now_time >= this.total_time) {
            this.music_play = false
            this.now_time = 0
          }
        }
      }, 1000)
      this.AUDIO.play()
      setTimeout(() => {
        this.AUDIO.pause()
      }, 0)
    },
    clickAudio() {
      if (this.music_play) {
        this.music_play = false
        this.AUDIO.pause()
      } else {
        this.music_play = true
        this.AUDIO.play()
      }
    },
    // 拖动进度条
    sliderChange(e) {
      const second = (e.detail.value / 100) * this.total_time
      this.AUDIO.seek(second)
      this.now_time = second
    },
    // 秒转换分秒
    getMinuteTime(data) {
      let minute = parseInt(data / 60)
      let second = parseInt(data % 60)
      if (minute.toString().length == 1) minute = `0${minute}`
      if (second.toString().length == 1) second = `0${second}`
      return `${minute}:${second}`
    },
    cleanMusic() {
      if (this.music_play) {
        this.music_play = false
        this.AUDIO.pause()
      }
      this.now_time = 0
      this.timeupdata && clearInterval(this.timeupdata)
      this.AUDIO && this.AUDIO.destroy()
    }
  },
  destroyed() {
    this.cleanMusic()
    this.intervalID && clearInterval(this.intervalID)
  }
}
</script>
<style lang="scss">
.audio-page {
  width: 100%;
  height: 10.6vmin;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0.4vmin 0.4vmin 0.8vmin #ccc;

  .box-left {
    width: 10%;
    position: relative;
    display: flex;

    .box-img {
      width: 100%;
      height: 100%;
      position: absolute;
      left: 0;
      top: 0;
      z-index: 2;
    }

    .page-btn {
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      position: absolute;
      left: 0;
      top: 0;
      z-index: 3;

      image {
        width: 6.6vmin;
        height: 6.6vmin;
        background-color: rgba($color: #000000, $alpha: 0.3);
        border-radius: 50%;
      }
    }
  }

  .box-content {
    width: 90%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: 0 4vmin;
    box-sizing: border-box;
    font-size: 3.2vmin;

    .content-name {
      width: 100%;
      display: -webkit-box;
      /* 弹性盒模型 */
      -webkit-box-orient: vertical;
      /* 元素垂直居中*/
      -webkit-line-clamp: 1;
      /*  文字显示的行数*/
      overflow: hidden;
      /* 超出隐藏 */
    }

    .progress {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: space-between;

      slider {
        width: 80%;
      }
    }
  }
}
</style>
