<template>
  <view class="countdown">
    <view class="current" :style="{ width: (dtime / time) * 100 + '%' }"></view>
  </view>
</template>
<script>
export default {
  props: {
    time: {
      type: Number,
      default: 60 // 秒
    }
  },
  data() {
    return {
      dtime: this.time,
      isPause: false,
      destruction: false // 是否销毁
    }
  },
  created() {
    this.start()
  },
  destroyed() {
    this.destruction = true
  },
  methods: {
    setTime() {
      setTimeout(() => {
        if (this.destruction) {
          // 销毁了
          return
        }
        if (this.isPause) {
          this.setTime()
          return
        }
        // 一秒钟一次
        if (this.dtime <= 0) {
          // 结束
          this.$emit('end')
          return
        } else {
          this.dtime--
        }
        this.setTime()
      }, 1000)
    },
    start() {
      this.setTime()
    },
    pause() {
      // 暂停
      this.isPause = true
    },
    play() {
      // 开始
      this.isPause = false
    },
    reset(flag = false) {
      this.dtime = this.time
      if (flag) {
        this.setTime()
      }
    },
    destory() {
      this.destruction = true
    }
  }
}
</script>
<style scoped lang="less">
.countdown {
  width: 100%;
  height: 4rpx;
  background-color: #eee;
  transition: all 0.25s;
  position: relative;
  .current {
    position: absolute;
    right: 0;
    top: 0;
    height: 100%;
    background: #ff8308;
  }
}
</style>
