<!-- test-reminder-suspend -->
<template>
  <view class="test-reminder-suspend" v-if="modelValue">
    <view class="window">
      <view class="header_title">
        <view class="tltle"> 考试提醒 </view>
      </view>
      <view class="content"> 考试已暂停，暂停期间将不能继续作答。 </view>
      <view class="content" style="margin-top: 10rpx">
        考试恢复后，剩余考试时长不变。
      </view>
    </view>
  </view>
</template>
<script>
export default {
  components: {},
  name: 'test-reminder-suspend', // 考试须知
  props: {
    modelValue: {
      type: <PERSON>olean,
      default: false
    }
  },
  data() {
    return {
      time: 120
    }
  },
  created() {},
  methods: {
    onCloseClick() {
      this.$emit('close', false)
    }
  }
}
</script>
<style lang="scss" scoped>
.test-reminder-suspend {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 11;
  .window {
    width: 682rpx;
    height: 300rpx;
    background: #ffffff;
    border-radius: 40rpx;
    position: relative;
    padding: 32rpx 30rpx;
    margin-top: -50%;

    .header_title {
      width: 100%;
      text-align: center;
      .title {
        font-size: 32rpx;
        color: #161f30;
        font-weight: 500;
      }
      .close_img {
        width: 40rpx;
        height: 40rpx;
        position: absolute;
        top: 34rpx;
        right: 30rpx;
        img {
          width: 100%;
          height: 100%;
        }
      }
    }
    .content {
      font-size: 26rpx;
      color: #787e8f;
      text-align: center;
      margin-top: 30rpx;
    }
  }
}
</style>
