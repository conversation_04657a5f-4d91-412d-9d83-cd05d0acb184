<template>
  <view class="test-reminder-overtime" v-if="modelValue">
    <view class="window">
      <view class="header_title">
        <view class="tltle"> 考试提醒 </view>
        <view class="close_img" @click="onCloseClick">
          <img
            src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/16951976945998e98169519769459914953_icon_guanbi%402x.png"
            alt=""
          />
        </view>
      </view>
      <view class="content">
        考试加时{{ parseInt(addTimeInfo.time / 60) }}分钟，结束时间变更为{{
          addTimeInfo.end
        }}
      </view>
      <view class="button" @click="onCloseClick">我知道了</view>
    </view>
  </view>
</template>
<script>
export default {
  components: {},
  name: 'test-reminder-overtime', // 考试须知
  props: {
    modelValue: {
      type: <PERSON><PERSON>an,
      default: false
    },
    addTimeInfo: {
      type: Object
    }
  },
  data() {
    return {
      time: 120
    }
  },
  methods: {
    onCloseClick() {
      console.log(this.addTimeInfo)
      this.$emit('close', false)
    }
  }
}
</script>
<style lang="scss" scoped>
.test-reminder-overtime {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 11;
  .window {
    width: 682rpx;
    // height: 300rpx;
    background: #ffffff;
    border-radius: 40rpx;
    position: relative;
    padding: 32rpx 30rpx;
    margin-top: -50%;

    .header_title {
      width: 100%;
      text-align: center;
      .title {
        font-size: 32rpx;
        color: #161f30;
        font-weight: 500;
      }
      .close_img {
        width: 40rpx;
        height: 40rpx;
        position: absolute;
        top: 34rpx;
        right: 30rpx;
        img {
          width: 100%;
          height: 100%;
        }
      }
    }
    .content {
      font-size: 26rpx;
      color: #787e8f;
      text-align: center;
      margin-top: 30rpx;
    }
    .button {
      width: 400rpx;
      height: 72rpx;
      background: #2e68ff;
      color: #ffffff;
      font-size: 26rpx;
      margin: 54rpx auto 20rpx;
      border-radius: 34rpx;
      text-align: center;
      line-height: 72rpx;
    }
  }
}
</style>
