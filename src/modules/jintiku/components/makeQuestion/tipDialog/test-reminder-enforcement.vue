<!-- test-reminder-enforcement -->
<template>
  <view class="test-reminder-enforcement" v-if="modelValue">
    <view class="window">
      <view class="header_title">
        <view class="tltle"> 考试提醒 </view>
      </view>
      <view class="content"> 检测到有你有违规操作，系统将于5秒后强制交卷 </view>
      <view class="time">{{ time }}S</view>
    </view>
  </view>
</template>
<script>
export default {
  components: {},
  name: 'test-reminder-enforcement', // 考试须知
  props: {
    modelValue: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      time: 5
    }
  },
  created() {
    this.setTime()
  },
  methods: {
    onCloseClick() {
      this.$emit('close', false)
    },
    setTime() {
      if (this.time) {
        setTimeout(() => {
          this.time--
          this.setTime()
        }, 1000)
      } else {
        console.log('===================')
        this.$emit('submit', false)
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.test-reminder-enforcement {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 11;
  .window {
    width: 682rpx;
    height: 300rpx;
    background: #ffffff;
    border-radius: 40rpx;
    position: relative;
    padding: 32rpx 30rpx;
    margin-top: -50%;

    .header_title {
      width: 100%;
      text-align: center;
      .title {
        font-size: 32rpx;
        color: #161f30;
        font-weight: 500;
      }
      .close_img {
        width: 40rpx;
        height: 40rpx;
        position: absolute;
        top: 34rpx;
        right: 30rpx;
        img {
          width: 100%;
          height: 100%;
        }
      }
    }
    .content {
      font-size: 26rpx;
      color: #787e8f;
      text-align: center;
      margin-top: 30rpx;
    }
    .time {
      text-align: center;
      margin-top: 40rpx;
      font-size: 36rpx;
      color: #f04f54;
    }
  }
}
</style>
