<!-- test-reminder-enforcement -->
<template>
  <view class="test-reminder-enforcement" v-if="modelValue">
    <view class="window">
      <view v-if="realVisibility">
        <view class="header_title">
          <view class="tltle"> 实名认证 </view>
        </view>
        <view class="content">
          <view
            >检测到有你的账号未进行实名认证，为保护您的考试成绩安全，保障考试的公正、严肃，需要实名认证。</view
          >
          <view style="margin-top: 22rpx"
            >未实名认证将无法使用本应用参加考试、请您尽快进行实名认证，谢谢配合。</view
          >
        </view>
        <view class="button" @click="goDetail('pages/userInfo/authentication')">
          立即认证
        </view>
        <img
          src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/169880722515096a0169880722515067165_icon_guanbi%E5%A4%87%E4%BB%BD%203%402x.png"
          class="close_button"
          @click="realVisibility = false"
        />
      </view>
      <view v-else>
        <img
          src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/16988073879735ac4169880738797438478_%E7%BC%96%E7%BB%84%402x%20(3).png"
          class="guide_img"
        />
        <view class="content"> 到【个人中心】可随时进行实名认证 </view>
        <view class="button" @click="onCloseClick">我知道了</view>
      </view>
    </view>
  </view>
</template>
<script>
export default {
  components: {},
  name: 'test-reminder-enforcement', // 考试须知
  props: {
    modelValue: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      time: 5,
      realVisibility: true
    }
  },
  watch: {
    modelValue: {
      //深度监听，可监听到对象、数组的变化
      handler(newV, oldV) {
        //支付列表
        console.log(newV)
        if (newV) {
          this.realVisibility = true
        }
      }
    }
  },
  methods: {
    onCloseClick() {
      this.$emit('close', false)
    },
    goDetail(url) {
      // this.allTime = this.examTime / 60
      this.$xh.push('jintiku', url)
    }
  }
}
</script>
<style lang="scss" scoped>
.test-reminder-enforcement {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 11;
  .window {
    width: 682rpx;
    // height: 300rpx;
    background: #ffffff;
    border-radius: 40rpx;
    position: relative;
    padding: 32rpx 30rpx;
    margin-top: -50%;

    .header_title {
      width: 100%;
      text-align: center;
      .title {
        font-size: 32rpx;
        color: #161f30;
        font-weight: 500;
      }
      .close_img {
        width: 40rpx;
        height: 40rpx;
        position: absolute;
        top: 34rpx;
        right: 30rpx;
        img {
          width: 100%;
          height: 100%;
        }
      }
    }
    .content {
      font-size: 26rpx;
      color: #787e8f;
      text-align: center;
      margin-top: 30rpx;
    }
    .time {
      text-align: center;
      margin-top: 40rpx;
      font-size: 36rpx;
      color: #f04f54;
    }
  }
  .button {
    width: 400rpx;
    height: 72rpx;
    background: #2e68ff;
    color: #ffffff;
    font-size: 26rpx;
    margin: 54rpx auto 20rpx;
    border-radius: 34rpx;
    text-align: center;
    line-height: 72rpx;
  }
  .close_button {
    width: 52rpx;
    height: 52rpx;
    position: absolute;
    bottom: -80rpx;
    left: calc(50% - (52rpx / 2));
  }
  .guide_img {
    width: 100%;
    height: 418rpx;
  }
}
</style>
