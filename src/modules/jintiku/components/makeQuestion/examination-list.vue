<template>
  <view class="examination_list">
    <view v-if="data.length">
      <view
        v-for="(item, index) in data"
        :key="index"
        style="margin-bottom: 20rpx"
        class="content"
      >
        <view class="header_content">
          <view class="title">{{ item.mock_name }}</view>
          <view class="time">
            {{ item.start_time.slice(0, 16) }}
            -
            {{ item.end_time.slice(0, 16) }}
          </view>
          <view class="session_name">
            考试轮次：{{ transform(item.exam_details_num) }}轮
          </view>
        </view>
        <view
          class="session_content"
          v-for="(citem, cindex) in item.exam_rounds"
          :key="cindex"
        >
          <view class="session">
            <view class="session_name" style="width: 92rpx">
              第{{ transform(cindex + 1) }}轮：
            </view>
            <view class="session_text" style="width: 115rpx">
              {{ citem.exam_round_name }}
            </view>
            <view class="time session_text">
              {{ citem.start_time.slice(5, 16) }}
              -
              {{ citem.end_time.slice(11, 16) }}
            </view>
            <view
              class="button report"
              :class="{ forbidden: citem.btn_is_enable == 2 }"
              @click="
                goDetail('pages/statistics/scoreReporting', item, citem, cindex)
              "
              v-if="citem.status == 3"
            >
              成绩报告
            </view>
            <view
              class="button start"
              :class="{ forbidden: citem.btn_is_enable == 2 }"
              v-if="citem.status == 1 || citem.status == 2"
              @click="onBeginClick(item, citem, cindex)"
            >
              <!-- @click="
                goDetail(
                  'pages/examination/facialRecognition',
                  item,
                  citem,
                  cindex
                ) -->
              开始考试
            </view>
            <!-- pages/examination/facialRecognition -->
            <view
              class="button start"
              :class="{ forbidden: citem.btn_is_enable == 2 }"
              @click="goDetail('pages/examination/notice', item, citem, cindex)"
              v-if="citem.status == 4 || citem.status == 5"
            >
              {{ citem.status == '4' ? '待补考' : '去补考' }}
            </view>
          </view>
        </view>
      </view>
    </view>
    <view v-else class="not_data">
      <view class="placeholder">
        <view class="placeholder_img">
          <img
            src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/16952070777833eeb169520707778381399_%E7%BC%96%E7%BB%84%402x%20(3).png"
            alt=""
          />
        </view>
        <view class="placeholder_text">
          暂时没有考试安排，快去刷题练习吧~
        </view>
      </view>
      <view class="not_content" v-if="isShowNav">
        <index-nav></index-nav>
      </view>
    </view>
    <real-name-authentication
      :modelValue="realNameModelValue"
      @close="realNameModelValue = false"
    />
  </view>
</template>
<script>
import indexNav from '../../components/commen/index-nav.vue'
import realNameAuthentication from './tipDialog/real-name-authentication.vue'
export default {
  name: 'examination_list', // 选择题
  components: {
    indexNav,
    realNameAuthentication
  },
  props: {
    data: {
      type: Array
    },
    isShowNav: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      realNameModelValue: false,
      transformList: [
        '一',
        '二',
        '三',
        '四',
        '五',
        '六',
        '七',
        '八',
        '九',
        '十'
      ],
      navs: [
        {
          name: '真题闯关',
          img: 'https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/16986565033678e7f169865650336724144_1.png',
          url: 'pages/questionChallenge/index'
        },
        {
          name: '智能测评',
          img: 'https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/169865652858773fa169865652858785885_2.png',
          url: 'pages/intelligentEvaluation/index'
        },
        {
          name: '考点词条',
          img: 'https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/16986565775223526169865657752259627_3.png',
          url: 'pages/examEntry/index'
        },
        {
          name: '模考大赛',
          img: 'https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/16986565946988bf0169865659469836993_4.png',
          url: 'pages/modelExaminationCompetition/index'
        }
      ]
    }
  },
  created() {},
  methods: {
    goDetail(url, item, citem, cindex) {
      if (citem.status == '4') {
        this.$xh.Toast('补考还未开启哦！')
        return
      }
      if (citem.btn_is_enable == 2) return false
      if (citem?.status == 1 || citem?.status == 5 || citem?.status == 3) {
        if (citem.status == 3) {
          let userId = wx.getStorageSync('__xingyun_userinfo__').student_id
          this.$xh.push(
            'jintiku',
            `${url}?examination_id=${citem.mock_id}&examination_session_id=${citem.id}&status=${citem.status}&paper_version_id=${citem.exam_paper_id}&user_id=${userId}`
          )
        } else {
          let startTime = this.$xh.iosTime(citem.start_time)
          let endTime = this.$xh.iosTime(citem.end_time)
          let time =
            citem.status == 5
              ? (new Date(endTime).getTime() - new Date(startTime).getTime()) /
                1000
              : (new Date(endTime).getTime() - new Date().getTime()) / 1000
          let totalTime =
            (new Date(endTime).getTime() - new Date(startTime).getTime()) / 1000
          this.$xh.push(
            'jintiku',
            `${url}?id=${item.id}&eid=${citem.id}&pid=${
              item.professional_id
            }&status=${citem.status}&pvid=${
              citem.exam_paper_id
            }&session=${this.transform(cindex + 1)}&session_name=${
              citem.exam_round_name
            }&time=${time}&totalTime=${totalTime}&mock_name=${item.mock_name}`
          )
        }
      } else {
        // this.$xh.push('jintiku', url)
      }
    },
    goDetailItem(url) {
      this.$xh.push('jintiku', url)
    },
    transform(num) {
      let number = Math.floor(num)
      const chineseNumbers = [
        '零',
        '一',
        '二',
        '三',
        '四',
        '五',
        '六',
        '七',
        '八',
        '九',
        '十'
      ]
      if (num <= 10) {
        return chineseNumbers[num]
      } else if (num > 10 && num < 100) {
        let unit = num % 10
        let ten = '十'
        let decade = Math.floor(num / 10)
        return `${chineseNumbers[decade]}${ten}${
          unit == 0 ? '' : chineseNumbers[unit]
        }`
      }
    },
    onBeginClick(item, citem, cindex) {
      this.goDetail(
        // 'pages/examination/facialRecognition',
        'pages/examination/notice',
        item,
        citem,
        cindex
      )
      // uni.getStorageSync('__xingyun_userinfo__').is_real_name == '1'
      //   ? this.goDetail(
      //       // 'pages/examination/facialRecognition',
      //       'pages/examination/notice',
      //       item,
      //       citem,
      //       cindex
      //     )
      //   : (this.realNameModelValue = true)
    }
  }
}
</script>
<style lang="scss" scoped>
.examination_list {
  .content {
    background: #ffffff;
    width: 100%;
    padding: 42rpx 32rpx;
    border-radius: 12rpx;
    box-shadow: 0 0 30rpx rgba(27, 38, 55, 0.06);
    .header_content {
      border-bottom: 1rpx solid #e8e9ea;
      .title {
        font-size: 30rpx;
        color: #212121;
        font-weight: 600;
        margin-bottom: 16rpx;
      }
      .time {
        font-size: 24rpx;
        color: #898a8d;
        margin-bottom: 16rpx;
      }
      .session_name {
        padding: 6rpx 12rpx;
        background: #ebf1ff;
        border-radius: 4rpx;
        color: #2e68ff;
        font-size: 20rpx;
        width: auto;
        display: inline-block;
        margin-bottom: 32rpx;
      }
    }
    .session_content {
      padding-top: 36rpx;
      font-size: 22rpx;
      .session {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        .session_name {
          color: #161f30;
        }
        .session_text {
          color: rgba(3, 32, 61, 0.65);
          text-overflow: ellipsis;
          white-space: nowrap;
          overflow: hidden;
        }
        .time {
          margin-left: 32rpx;
        }
      }
    }
  }
  .button {
    width: 160rpx;
    height: 56rpx;
    border-radius: 64rpx;
    color: #ffffff;
    font-size: 28rpx;
    text-align: center;
    line-height: 56rpx;
    margin-left: auto;
    font-weight: 400;
    margin-right: 4rpx;
  }
  .report {
    background: #32c5ff;
  }
  .start {
    background: #2e68ff;
  }
  .forbidden {
    // background: #ccc;
    background: rgba(3, 32, 61, 0.35);
  }
  .disabled {
    background: rgba(3, 32, 61, 0.35);
  }
  .not_data {
    width: 100%;
    .placeholder {
      margin-top: 80rpx;
      .placeholder_img {
        width: 229rpx;
        height: 180rpx;
        margin: 0 auto;
        img {
          width: 100%;
          height: 100%;
        }
      }
      .placeholder_text {
        text-align: center;
        font-size: 26rpx;
        margin-top: 40rpx;
        color: #ccc;
      }
    }
    .not_content {
      background: #fff;
      height: calc(100vh - 500rpx);
      position: relative;
      z-index: 1;
      margin-top: 175rpx;
      border-top-right-radius: 20rpx;
      border-top-left-radius: 20rpx;
      padding: 60rpx 24rpx;
    }
  }
}
</style>
