<template>
  <swiper
    class="swiper"
    :indicator-dots="indicatorDots"
    :autoplay="autoplay"
    :current="current"
    :duration="300"
    :circular="false"
    :disable-touch="disableTouch"
    :skip-hidden-item-layout="skipHiddenItemLayout"
    @change="swiperChange"
  >
    <swiper-item v-for="(item, index) in lists" :key="index">
      <view
        class="swiper-item"
        v-if="index == current || index == current - 1 || index == current + 1"
      >
        <view class="select-question-box">
          <select-question
            v-for="(jtem, j) in item.stem_list"
            :key="j"
            :info="item"
            :stem="jtem"
            @selecte="selecte"
            @input="inputChange"
            :answer="false"
            :current="current"
          ></select-question>
        </view>
      </view>
    </swiper-item>
  </swiper>
</template>
<script>
import selectQuestion from './select-question.vue'
import questionAnswer from './question-answer.vue'
import { isSelectedType } from '../../utils/index'
export default {
  components: {
    selectQuestion,
    questionAnswer
  },
  props: {
    indicatorDots: {
      type: Boolean,
      default: false
    },
    autoplay: {
      type: Boolean,
      default: false
    },
    lists: {
      type: Array,
      default: []
    },
    index: {
      type: Number,
      default: 0
    },
    disableTouch: {
      type: Boolean,
      default: false
    },
    skipHiddenItemLayout: {
      type: Boolean,
      default: false
    },
    examination_id: {
      type: String,
      default: 0
    },
    examKey: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      current: 0
    }
  },
  methods: {
    prev() {
      if (this.current <= 0) {
        this.$xh.Toast('已经是第一题了哦！')
        return
      }
      this.current--
      this.$emit('update:index', this.current)
    },
    next() {
      if (this.current >= this.lists.length - 1) {
        // this.$xh.Toast('已经是最后一题了哦！')
        this.$emit('last')
        return
      }
      this.current++
      this.$emit('update:index', this.current)
    },
    swiperChange(e) {
      this.current = e.detail.current
      this.$emit('update:index', this.current)
    },
    selecte(info) {
      let isNext = false
      let data = this.lists.map(res => {
        if (res.sub_question_id == info.sub_question_id) {
          isNext = info.stem_list[0].multiple
          return { ...info, doubt: false }
        }
        return res
      })
      this.$emit('update:lists', data)
      let anwersList = uni.getStorageSync('__anwers_list__') || {}
      if (!anwersList[this.examKey]) {
        anwersList[this.examKey] = {
          lists: data
        }
        uni.setStorageSync('__anwers_list__', anwersList)
      } else {
        anwersList[this.examKey].lists = data
        uni.setStorageSync('__anwers_list__', anwersList)
      }
      if (!isNext && isSelectedType(info.type)) {
        // 选择题才往下一题走
        // this.next()
      }
    },
    inputChange(info) {
      let data = this.lists.map(res => {
        if (res.sub_question_id == info.sub_question_id) {
          return { ...info, doubt: false }
        }
        return res
      })
      this.$emit('update:lists', data)
      let anwersList = uni.getStorageSync('__anwers_list__') || {}
      if (!anwersList[this.examKey]) {
        anwersList[this.examKey] = {
          lists: data
        }
        uni.setStorageSync('__anwers_list__', anwersList)
      } else {
        anwersList[this.examKey].lists = data
        uni.setStorageSync('__anwers_list__', anwersList)
      }
    }
  },
  watch: {
    index: {
      handler(val) {
        this.current = val
      },
      immediate: true
    }
  }
}
</script>
<style scoped lang="less">
.swiper {
  height: 100%;
  .swiper-item {
    overflow-y: auto;
    padding: 48rpx 38rpx;
    height: 100%;
    background-color: #fff;
    .question-answer-box {
      margin-top: 86rpx;
    }
    .answer {
      margin-top: 60rpx;
    }
  }
}
</style>
