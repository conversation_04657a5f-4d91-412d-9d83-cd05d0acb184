<template>
  <view class="write_from_memory">
    <head-height :statusBarHeight.sync="statusBarHeight" />
    <view class="priview-time" :style="{ top: statusBarHeight + 48 + 'px' }">
      <view class="button" @click="handleReturnClick(1)">看标题</view>
      <view class="title">我的默写</view>
      <view class="button" @click="handleReturnClick(2)">对答案</view>
    </view>
    <view class="content">
      <textarea class="input_content" maxlength="10000" v-model="data" />
    </view>
  </view>
</template>
<script>
import headHeight from '../../components/commen/head-height.vue'
export default {
  name: '',
  components: {
    headHeight
  },
  data() {
    return {
      statusBarHeight: 0,
      data: ''
    }
  },
  methods: {
    handleReturnClick(type) {
      let tmpData = {
        type: type,
        data: this.data
      }
      this.$emit('close', tmpData)
    }
  }
}
</script>
<style lang="less" scoped>
.write_from_memory {
  height: 100vh;
  width: 100vw;
  background: #ffffff;
  position: fixed;
  top: 0;
  .priview-time {
    position: fixed;
    left: 0;
    top: 140rpx;
    width: 100vw;
    height: 96rpx;
    background-color: #fff;
    // border-bottom: 1px solid #eee;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 40rpx;
    .button {
      font-size: 32rpx;
      color: #2e68ff;
    }
    .title {
      color: #000000;
      font-size: 34rpx;
    }
  }
  .content {
    margin-top: calc(70rpx + 96rpx);
    padding: 30rpx 32rpx;
    .input_content {
      width: auto;
      padding: 32rpx 16rpx;
      height: 620rpx;
      background: #f5f5f5;
      border-radius: 16rpx;
      font-size: 28rpx;
      color: #333333;
      overflow-y: auto;
    }
  }
}
</style>
