<template>
  <swiper
    class="swiper"
    :indicator-dots="indicatorDots"
    :autoplay="autoplay"
    :current="swiper.current"
    :duration="swiper.duration"
    :circular="false"
    :disable-touch="disableTouch"
    :skip-hidden-item-layout="skipHiddenItemLayout"
    @animationfinish="animationfinish"
  >
    <swiper-item v-for="(item, index) in swiper.list" :key="item.id">
      <view class="swiper-item">
        <view class="select-question-box">
          <select-question
            v-for="(jtem, j) in item.stem_list"
            :key="j"
            :info="item"
            :stem="jtem"
            @selecte="selecte"
            @input="inputChange"
            :answer="false"
            :current="current"
          ></select-question>
        </view>
      </view>
    </swiper-item>
  </swiper>
</template>
<script>
import selectQuestion from './select-question.vue'
import questionAnswer from './question-answer.vue'
import { isSelectedType } from '../../utils/index'
export default {
  components: {
    selectQuestion,
    questionAnswer
  },
  props: {
    indicatorDots: {
      type: <PERSON><PERSON>an,
      default: false
    },
    autoplay: {
      type: Boolean,
      default: false
    },
    lists: {
      type: Array,
      default: []
    },
    index: {
      type: Number,
      default: 0
    },
    disableTouch: {
      type: Boolean,
      default: false
    },
    skipHiddenItemLayout: {
      type: Boolean,
      default: false
    },
    examination_id: {
      type: String,
      default: 0
    },
    examKey: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      // current: 0,
      swiper: {
        list: [], //展示数据
        currentIndex: 0, //真实的index
        current: 0, //swiper当前的index
        recordCurrent: 0, //swiper上次的index
        duration: 300 //动画时常
      }
    }
  },
  mounted() {
    this.upSwiper(0)
  },
  methods: {
    animationfinish({ detail }) {
      // 用户滑动
      if (detail.source == 'touch') {
        if (this.swiper.recordCurrent == detail.current) {
          return
        }
        // 滑动后的index
        this.upSwiper(
          this.swiper.currentIndex + detail.current - this.swiper.recordCurrent,
          detail.current
        )
      }
    },
    upSwiper(index, touchCurrent) {
      // 防止越界
      if (index < 0 || index >= this.lists.length) {
        return
      }
      this.swiper.currentIndex = index
      this.$emit('update:index', index)
      // 防闪更新数据
      let list = []
      for (
        let i = this.swiper.currentIndex - 1;
        i <= this.swiper.currentIndex + 1;
        ++i
      ) {
        let item = this.lists[this.swiper.currentIndex]
        if (typeof item !== 'undefined') {
          list.push(item)
        }
      }
      let current = 0
      // 只要不是第一个页面 current都是1
      if (index != 0) {
        current = 1
        this.swiper.current = touchCurrent
      }
      this.swiper.duration = 0
      this.$nextTick(() => {
        this.swiper.list = list
        this.swiper.recordCurrent = current
        this.$nextTick(() => {
          this.swiper.current = current
          this.swiper.duration = 300
        })
        // 更新数据
        setTimeout(() => {
          let list = []
          for (
            let i = this.swiper.currentIndex - 1;
            i <= this.swiper.currentIndex + 1;
            ++i
          ) {
            let item = this.lists[i]
            if (typeof item !== 'undefined') {
              list.push(item)
            }
          }
          this.swiper.list = list
        }, 300)
      })
    },
    prev() {
      console.log('prev')
      if (this.swiper.currentIndex <= 0) {
        this.$xh.Toast('已经是第一题了哦！')
        return
      }
      this.swiper.currentIndex--
      this.upSwiper(this.swiper.currentIndex)
    },
    next() {
      console.log('next')
      if (this.swiper.currentIndex >= this.lists.length - 1) {
        // this.$xh.Toast('已经是最后一题了哦！')
        this.$emit('last')
        return
      }
      this.swiper.currentIndex++
      this.upSwiper(this.swiper.currentIndex)
    },
    selecte(info) {
      let isNext = false
      let data = this.lists.map(res => {
        if (res.sub_question_id == info.sub_question_id) {
          // isNext = info.stem_list[0].multiple
          return { ...info, doubt: false }
        }
        return res
      })
      this.$emit('update:lists', data)
      let anwersList = uni.getStorageSync('__anwers_list__') || {}
      if (!anwersList[this.examKey]) {
        anwersList[this.examKey] = {
          lists: data
        }
        uni.setStorageSync('__anwers_list__', anwersList)
      } else {
        anwersList[this.examKey].lists = data
        uni.setStorageSync('__anwers_list__', anwersList)
      }
      if (!isNext && isSelectedType(info.type)) {
        // 选择题才往下一题走
        // this.next()
      }
    },
    inputChange(info) {
      let data = this.lists.map(res => {
        if (res.sub_question_id == info.sub_question_id) {
          return { ...info, doubt: false }
        }
        return res
      })
      this.$emit('update:lists', data)
      let anwersList = uni.getStorageSync('__anwers_list__') || {}
      if (!anwersList[this.examKey]) {
        anwersList[this.examKey] = {
          lists: data
        }
        uni.setStorageSync('__anwers_list__', anwersList)
      } else {
        anwersList[this.examKey].lists = data
        uni.setStorageSync('__anwers_list__', anwersList)
      }
    }
  },
  watch: {
    index: {
      handler(val) {
        console.log('handler')
        if (this.swiper.currentIndex != val) {
          this.upSwiper(val)
        }
        // this.upSwiper(val)
        // this.current = val
      },
      immediate: true
    }
  }
}
</script>
<style scoped lang="less">
.swiper {
  height: 100%;
  .swiper-item {
    overflow-y: auto;
    padding: 48rpx 38rpx;
    height: 100%;
    background-color: #fff;
    .question-answer-box {
      margin-top: 86rpx;
    }
    .answer {
      margin-top: 60rpx;
    }
  }
}
</style>
