<template>
  <picker-shell :value="value" @input="input" title="纠错" ref="picker">
    <view class="filtrate_select">
      <view class="select_content">
        <view class="title">错误类型</view>
        <view class="select">
          <view
            class="select_item button"
            v-for="(item, index) in selectList"
            :key="index"
            @click="onSelect(item.id)"
            :class="{ select_item_click: err_type == item.id }"
          >
            {{ item.label }}
          </view>
        </view>
        <view class="title">错误描述</view>
        <view>
          <textarea
            class="textarea"
            v-model="description"
            placeholder="请输入错误描述"
          ></textarea>
        </view>
        <view class="uploading" @click="handleUploadingImg">
          <img
            src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/16969082605186245169690826051830329_Upload%E4%B8%8A%E4%BC%A0%EF%BC%8F%E5%9B%BE%E7%89%87%E4%B8%8A%E4%BC%A0%EF%BC%8F%E9%BB%98%E8%AE%A4%E5%A4%87%E4%BB%BD%203%402x.png"
            alt=""
            v-if="!file_path"
          />
          <img :src="completepath(file_path)" alt="" v-else />
        </view>
        <view class="button_box">
          <view class="button" @click="submit">提交</view>
        </view>
      </view>
    </view>
  </picker-shell>
</template>
<script>
import pickerShell from '../commen/picker-shell.vue'
import { correction } from '../../api/commen'
export default {
  name: 'error-correction', // 纠错组件
  components: {
    pickerShell
  },
  props: {
    value: {
      type: Boolean,
      default: false
    },
    question_id: {
      type: String,
      default: ''
    },
    question_version_id: {
      type: String,
      default: ''
    },
    version: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      err_type: '',
      selectList: [
        {
          id: '1',
          label: '题干'
        },
        {
          id: '2',
          label: '答案'
        },
        {
          id: '3',
          label: '解析'
        },
        {
          id: '4',
          label: '知识点'
        },
        {
          id: '5',
          label: '其他'
        }
      ],
      description: '',
      file_path: ''
    }
  },
  watch: {
    value(val) {
      if (val) {
        this.err_type = ''
        this.description = ''
        this.file_path = ''
      }
    }
  },
  methods: {
    completepath(url) {
      return this.$xh.completepath(url)
    },
    input(val) {
      this.$emit('input', val)
    },
    onSelect(id) {
      this.err_type = id
    },
    submit() {
      if (!this.err_type) {
        this.$xh.Toast('请选择纠错类型')
        return
      }
      if (!this.description) {
        this.$xh.Toast('请输入详细内容')
        return
      }
      correction({
        description: this.description,
        err_type: this.err_type,
        file_path: [this.file_path],
        question_id: this.question_id,
        question_version_id: this.question_version_id,
        version: this.version
      }).then(() => {
        this.$emit('input', false)
        setTimeout(() => {
          this.$xh.Toast('感谢您的意见！')
        }, 500)
      })
    },
    close() {
      this.$emit('close', false)
    },
    handleUploadingImg() {
      this.$xh.upLoad().then(url => {
        this.file_path = url
      })
    }
  }
}
</script>
<style lang="less" scoped>
.filtrate_select {
  .select_content {
    z-index: 15;
    width: 100%;
    border-radius: 40rpx 40rpx 0rpx 0rpx;
    background: #fff;
    bottom: 0;
    padding: 32rpx 24rpx 42rpx;
    .header {
      position: relative;
      .name {
        font-size: 32rpx;
        color: #161f30;
        font-weight: 500;
        text-align: center;
      }
      .close {
        width: 40rpx;
        height: 40rpx;
        position: absolute;
        top: 0rpx;
        right: 0rpx;
      }
    }
    .title {
      font-size: 24rpx;
      color: #000000;
      margin-bottom: 24rpx;
      font-weight: 400;
      margin-top: 40rpx;
    }
    .select {
      width: 100%;
      display: flex;
      justify-content: flex-start;
      flex-wrap: wrap;
      margin-top: 40rpx;

      .select_item {
        width: 214rpx;
        height: 68rpx;
        background: #f6f7f8;
        border-radius: 32rpx;
        color: #161f30;
        font-size: 24rpx;
        text-align: center;
        line-height: 68rpx;
        margin: 0 10rpx 32rpx;
      }
      .select_item_click {
        border: 2rpx solid #2e68ff;
        background: #ebf1ff;
        color: #2e68ff;
        border-radius: 32rpx;
      }
    }
    .button_box {
      display: flex;
      justify-content: center;
      align-items: center;
      background: #fff;
      margin: 62rpx 0 38rpx;
      .button {
        width: 600rpx;
        height: 72rpx;
        font-size: 28rpx;
        text-align: center;
        line-height: 72rpx;
        color: #fff;
        background: #2e68ff;
        border-radius: 34rpx;
      }
    }
  }
  .textarea {
    width: 100%;
    height: 266rpx;
    border: 2rpx solid #dde0e2;
    border-radius: 20rpx;
    padding: 12rpx;
    box-sizing: border-box;
    font-size: 24rpx;
  }
  .uploading {
    width: 248rpx;
    height: 248rpx;
    margin-top: 40rpx;
    img {
      width: 100%;
      height: 100%;
    }
  }
}
</style>
