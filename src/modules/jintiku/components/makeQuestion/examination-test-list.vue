<template>
  <view class="examination-test-list">
    <examinationTestItem
      v-for="(item, index) in data"
      :key="index"
      :item="item"
      :isPay="isPay"
    ></examinationTestItem>

    <view class="not_data" v-if="data.length == 0">
      <view class="img">
        <img
          src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/16954369620338446169543696203498545_%E7%BC%96%E7%BB%84%402x%20(4).png"
          alt=""
        />
      </view>
      <view class="desc">暂无数据！</view>
    </view>
  </view>
</template>

<script>
import countDown from './count-down.vue'
import examinationTestItem from './examination-test-item.vue'

export default {
  components: {
    countDown,
    examinationTestItem
  },
  props: {
    data: {
      type: Array
    },
    isPay: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {}
  },
  created() {},
  methods: {}
}
</script>

<style lang="scss" scoped>
.examination-test-list {
  .not_data {
    width: 100%;
    .img {
      width: 229rpx;
      height: 180rpx;
      margin: 0 auto;
      img {
        width: 100%;
        height: 100%;
      }
    }
    .desc {
      text-align: center;
      font-size: 26rpx;
      margin-top: 40rpx;
      color: #ccc;
    }
  }
}
</style>
