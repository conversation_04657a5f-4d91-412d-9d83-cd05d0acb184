<template>
  <view class="img-and-video">
    <view class="position-img" v-if="stem.resource_info.img">
      <!-- 底图 -->
      <image
        class="bg-img"
        :src="stem.resource_info.img.path"
        mode="widthFix"
      />
      <!-- 坐标 -->
      <view
        class="position"
        v-for="(info, index) in stem.resource_info.data"
        :key="index"
        :style="{
          width: getCoord(info.coord).width,
          height: getCoord(info.coord).height,
          left: getCoord(info.coord).left,
          top: getCoord(info.coord).top
        }"
        @click="playAudio(info.file, index)"
      >
        <image
          src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/17098008382958704170980083829567047_%E6%89%AC%E5%A3%B0%E5%99%A8%20(1).png"
          mode="widthFix"
        />
        <view
          class="ripple-effect"
          v-if="playIng && currentIndex == index"
        ></view>
      </view>
    </view>
    <view
      class="video"
      v-if="stem.resource_info.video && !isAudio(stem.resource_info.video)"
    >
      <video
        id="myVideo"
        class="myVideo"
        controls
        :src="completepath(stem.resource_info.video)"
        @error="videoErrorCallback"
      ></video>
    </view>
    <view
      class="audio"
      v-if="stem.resource_info.video && isAudio(stem.resource_info.video)"
    >
      <audio :src="completepath(stem.resource_info.video)" controls></audio>
    </view>
  </view>
</template>
<script>
export default {
  props: {
    stem: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      currentIndex: 0,
      playIng: false,
      isCompletepathError: false
    }
  },
  mounted() {
    this.initVideo()
  },
  methods: {
    isAudio(url) {
      const fileExtension = url.split('.').pop()
      let formatList = ['WAV', 'mp3']
      return !!formatList.find(s => s == fileExtension)
    },
    initVideo() {
      this.videoContext = uni.createVideoContext('myVideo', this)
    },
    completepath(url) {
      //public开头的是中台上传的
      if (url.startsWith('public')) {
        return `https://ysys-assets.oss-cn-beijing.aliyuncs.com/${url}`
      } else {
        return this.$xh.completepath(url)
      }
    },
    getCoord(coord) {
      if (coord) {
        let result = coord.split(',')
        return {
          width: result[0] * 100 + '%',
          height: result[1] * 100 + '%',
          left: result[2] * 100 + '%',
          top: result[3] * 100 + '%'
        }
      }
      return {
        width: 0,
        height: 0,
        left: 0,
        top: 0
      }
    },
    playAudio(url, index) {
      this.currentIndex = index
      this.$xh.Toast('音频播放中')
      this.playIng = true
      console.log(url)
      this.$xh.playAudio(
        this.$xh.completepath(url),
        () => {
          this.playIng = false
        },
        () => {
          this.$xh.Toast('音频播放失败！')
          this.playIng = false
        }
      )
    },
    videoErrorCallback() {
      this.$xh.Toast('视频地址错误！')
    },
    stopPlay() {
      this.$xh.pauseAudio()
      this.videoContext.pause()
      this.playIng = false
    }
  }
  // destroyed() {
  //   console.log(1)
  //   this.$xh.pauseAudio()
  // }
}
</script>
<style scoped lang="less">
.img-and-video {
  width: 100%;
  position: relative;
  .bg-img {
    width: 100%;
    height: auto;
  }
  .position {
    position: absolute;
    display: flex;
    align-items: center;
    justify-content: center;
    image {
      width: 50rpx;
      height: 50rpx;
    }
  }
  .video {
    width: 100%;
    .myVideo {
      width: 100%;
    }
  }
  .audio {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}
.ripple-effect {
  position: absolute;
  width: 50rpx;
  height: 50rpx;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.3);
  transform: scale(0);
  animation-name: ripple-animate;
  animation-duration: 1s;
  animation-timing-function: ease-out;
  animation-iteration-count: infinite; /* 设置为无限循环播放 */
}
@keyframes ripple-animate {
  to {
    transform: scale(2.5);
    opacity: 0;
  }
}
</style>
