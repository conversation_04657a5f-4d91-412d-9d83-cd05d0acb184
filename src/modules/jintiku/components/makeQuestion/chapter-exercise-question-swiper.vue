<template>
  <swiper
    class="swiper"
    :indicator-dots="indicatorDots"
    :autoplay="autoplay"
    :current="current"
    :duration="300"
    :circular="false"
    @change="swiperChange"
  >
    <swiper-item v-for="(item, index) in lists" :key="index">
      <view
        class="swiper-item"
        v-if="index == current || index == current - 1 || index == current + 1"
      >
        <view class="select-question-box">
          <view v-for="(stem, j) in item.stem_list" :key="j" class="container">
            <!-- 渲染题 -->
            <select-question
              :info="item"
              :stem="stem"
              @selecte="selecte"
              :answer="isShowAnswer(item, stem) || showAnswer ? true : false"
              :isShowAnalysis="
                isShowAnswer(item, stem) || item.lookAnswer || showAnswer
              "
              :questionNumber="index + 1"
            ></select-question>

            <!-- 渲染解析 -->
            <view
              class="answer"
              v-if="isShowAnswer(item, stem) || item.lookAnswer || showAnswer"
            >
              <question-answer
                :info="stem"
                :parentInfo="item"
                :showSelfAnswer="showSelfAnswer"
                :showAnalysis="showAnalysis"
                :showNav="showNav"
                :questionType="item.type"
              ></question-answer>
            </view>
          </view>
        </view>
      </view>
    </swiper-item>
  </swiper>
</template>
<script>
import selectQuestion from './select-question.vue'
import questionAnswer from './question-answer.vue'
import { questionHelper } from '../../utils/index'
export default {
  components: {
    selectQuestion,
    questionAnswer
  },
  props: {
    showAnswer: {
      // 是否强制显示答案
      type: Boolean,
      default: false
    },
    indicatorDots: {
      type: Boolean,
      default: false
    },
    autoplay: {
      type: Boolean,
      default: false
    },
    lists: {
      type: Array,
      default: []
    },
    index: {
      type: Number,
      default: 0
    },
    // 是否显示自己选择的答案
    showSelfAnswer: {
      type: Boolean,
      default: true
    },
    showAnalysis: {
      // 是否显示全站统计
      type: Boolean,
      default: true
    },
    showNav: {
      // 是否显示底部导航
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      current: 0
    }
  },
  methods: {
    isShowAnswer(item, stem) {
      if (stem.multiple) {
        // 多选
        return stem.selected.length >= stem.answer.length
      } else {
        // 单选
        return stem.selected.length > 0
      }
    },
    prev() {
      if (this.current <= 0) {
        this.$xh.Toast('已经是第一题了哦！')
        return
      }
      this.current--
      this.$emit('update:index', this.current)
    },
    next() {
      if (this.current >= this.lists.length - 1) {
        this.$xh.Toast('已经是最后一题了哦！')
        return
      }
      this.current++
      this.$emit('update:index', this.current)
    },
    swiperChange(e) {
      this.current = e.detail.current
      this.$emit('update:index', this.current)
    },
    selecte(info) {
      this.$emit(
        'update:lists',
        this.lists.map(res => {
          if (res.sub_question_id == info.sub_question_id) {
            return {
              ...res,
              ...info
            }
          }
          return res
        })
      )
      if (questionHelper.diffAnswer(info)) {
        // 获取用户是否做对了
        this.next()
      }
      // TODO
      // console.log(info.stem_list)
      // let multiple = info.stem_list[0].multiple
      // if( multiple ){
      //   // 多选判断
      // } else {
      //   // 单选判断
      // }
      // let answer = info.stem_list[0].answer.map(item => item + '').sort()
      // let selected = info.stem_list[0].selected.map(item => item + '').sort()
      // if (JSON.stringify(answer) == JSON.stringify(selected)) {
      //   // 做对了
      //   this.next()
      // }
      // if (info.questionanswer == info.selected) {
      //   // 做对了
      //   // this.next()
      // }
    }
  },
  watch: {
    index: {
      handler(val) {
        this.current = val
      },
      immediate: true
    }
  }
}
</script>
<style scoped lang="less">
.swiper {
  height: 100%;
  .swiper-item {
    overflow-y: auto;
    padding: 48rpx 38rpx;
    height: 100%;
    background-color: #fff;
    .question-answer-box {
      margin-top: 86rpx;
    }
    .answer {
      margin-top: 60rpx;
    }
  }
}
</style>
