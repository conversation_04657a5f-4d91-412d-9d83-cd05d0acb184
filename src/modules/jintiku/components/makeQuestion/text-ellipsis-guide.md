# 文字省略功能实现指南

## 🎯 功能说明

为标签文字添加了两行省略功能，当文字超过两行时，会在第二行末尾显示省略号（...）。

## 📝 实现方式

### 1. HTML 结构
```vue
<view class="name text-ellipsis-2">{{ item.name+item.name+item.name }}</view>
```

### 2. CSS 样式
```scss
.text-ellipsis-2 {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    line-clamp: 2; // 标准属性，提高兼容性
    overflow: hidden;
    text-overflow: ellipsis;
    word-break: break-word;
    line-height: 1.4; // 设置行高，确保两行显示效果更好
}
```

## 🔧 样式属性解释

| 属性 | 作用 | 说明 |
|------|------|------|
| `display: -webkit-box` | 设置为弹性盒子布局 | WebKit 内核浏览器支持 |
| `-webkit-box-orient: vertical` | 设置子元素垂直排列 | 必须配合 -webkit-line-clamp 使用 |
| `-webkit-line-clamp: 2` | 限制显示行数为2行 | WebKit 内核浏览器支持 |
| `line-clamp: 2` | 标准属性 | 提高兼容性 |
| `overflow: hidden` | 隐藏超出部分 | 必须设置 |
| `text-overflow: ellipsis` | 超出部分显示省略号 | 配合 overflow 使用 |
| `word-break: break-word` | 单词换行 | 防止长单词撑破布局 |
| `line-height: 1.4` | 行高 | 确保两行显示效果更好 |

## 🌟 使用场景

### 适用情况
- ✅ 标题文字过长
- ✅ 描述文本需要限制行数
- ✅ 卡片布局中的文字内容
- ✅ 列表项的文字显示

### 不适用情况
- ❌ 需要显示完整内容的重要信息
- ❌ 用户必须看到全部文字的场景

## 📱 兼容性

| 浏览器 | 支持情况 | 说明 |
|--------|----------|------|
| Chrome | ✅ 完全支持 | WebKit 内核 |
| Safari | ✅ 完全支持 | WebKit 内核 |
| Firefox | ⚠️ 部分支持 | 需要 line-clamp 标准属性 |
| Edge | ✅ 完全支持 | Chromium 内核 |
| 微信小程序 | ✅ 完全支持 | WebKit 内核 |

## 🎨 扩展用法

### 单行省略
```scss
.text-ellipsis-1 {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
```

### 三行省略
```scss
.text-ellipsis-3 {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
    line-clamp: 3;
    overflow: hidden;
    text-overflow: ellipsis;
    word-break: break-word;
    line-height: 1.4;
}
```

### 自定义行数
```scss
.text-ellipsis-n {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: var(--lines, 2); // 使用 CSS 变量
    line-clamp: var(--lines, 2);
    overflow: hidden;
    text-overflow: ellipsis;
    word-break: break-word;
    line-height: 1.4;
}
```

使用方式：
```vue
<view class="text-ellipsis-n" style="--lines: 4;">长文本内容...</view>
```

## 🔍 调试技巧

### 1. 检查是否生效
- 在开发者工具中查看元素是否应用了正确的样式
- 检查文字是否确实超过了指定行数

### 2. 常见问题
- **省略号不显示**：检查是否设置了 `overflow: hidden`
- **行数不正确**：调整 `line-height` 值
- **换行异常**：检查 `word-break` 设置

### 3. 性能优化
- 避免在大量元素上使用，可能影响渲染性能
- 考虑使用虚拟滚动等技术优化长列表

## 📋 最佳实践

1. **设置合适的行高**：确保文字显示清晰
2. **测试不同长度的文本**：验证省略效果
3. **考虑响应式设计**：不同屏幕尺寸下的显示效果
4. **提供查看全文的方式**：如点击展开功能

现在您的标签文字会自动在两行后显示省略号，提供更好的用户体验！
