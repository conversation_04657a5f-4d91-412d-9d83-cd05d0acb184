<template>
  <picker-shell height="478rpx" :value="value" @input="input" :isclosed="false">
    <view class="resolve-authorization-sheet">
      <view class="title"> 太棒了！您已完成试卷 </view>
      <view class="desc"> 查看成绩与题目解析，请点击下方授权 </view>
      <view class="btns">
        <view class="btn flex-center button back" @click="back">返回</view>
        <view class="btn flex-center button login" @click="login">授权</view>
      </view>
    </view>
  </picker-shell>
</template>
<script>
import pickerShell from '../commen/picker-shell.vue'
export default {
  // 答题卡查看
  name: 'answer-sheet',
  props: {
    value: {
      type: Boolean,
      default: false
    }
  },
  components: {
    pickerShell
  },
  data() {
    return {}
  },
  created() {},
  methods: {
    input(val) {
      this.$emit('input', val)
    },
    back() {
      uni.navigateBack()
    },
    login() {
      this.$xh.push('jintiku_baidu', 'pages/login/login')
      this.$emit('input', false)
    }
  }
}
</script>
<style scoped lang="less">
.resolve-authorization-sheet {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-end;
  .title {
    height: 56rpx;
    font-size: 40rpx;
    font-family: PingFangSC-Semibold, PingFang SC;
    font-weight: 600;
    color: #03203d;
    line-height: 56rpx;
    margin-bottom: 28rpx;
  }
  .desc {
    height: 44rpx;
    font-size: 32rpx;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: rgba(3, 32, 61, 0.75);
    line-height: 44rpx;
    margin-bottom: 126rpx;
  }
  .btns {
    padding-bottom: 48rpx;
    display: flex;
    align-items: center;
    .btn {
      width: 302rpx;
      height: 80rpx;
      border: 2rpx solid #7781fa;
      border-radius: 44rpx;
      font-size: 32rpx;
      font-weight: 500;
      background-color: #fff;
      color: #7781fa;
    }
    .login {
      background: linear-gradient(270deg, #6d7dff 0%, #b3a1ff 100%);
      color: #ffffff;
      margin-left: 66rpx;
      border: 2rpx solid transparent;
    }
  }
}
</style>
