<template>
  <view
    class="swiper"
    :style="{
      transform: `translateX(${-index * 100}%)`
    }"
    @touchmove.stop="touchmove"
  >
    <view class="swiper-item" v-for="(item, index) in lists" :key="index">
      <view class="item">
        <scroll-view scroll-y="true" class="scroll-Y">
          <select-question
            v-for="(jtem, j) in item.stem_list"
            :key="j"
            :info="item"
            :stem="jtem"
            @selecte="selecte"
            :answer="false"
          ></select-question>
        </scroll-view>
      </view>
    </view>
  </view>
</template>
<script>
import selectQuestion from './select-question.vue'
import questionAnswer from './question-answer.vue'
import { questionHelper } from '../../utils/index'
export default {
  components: {
    selectQuestion,
    questionAnswer
  },
  props: {
    indicatorDots: {
      type: Boolean,
      default: false
    },
    autoplay: {
      type: Boolean,
      default: false
    },
    lists: {
      type: Array,
      default: []
    },
    index: {
      type: Number,
      default: 0
    },
    disableTouch: {
      type: <PERSON>olean,
      default: false
    },
    skipHiddenItemLayout: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      current: 0
    }
  },
  methods: {
    prev() {
      if (this.current <= 0) {
        this.$xh.Toast('已经是第一题了哦！')
        return
      }
      this.current--
      this.$emit('update:index', this.current)
      this.$emit('prev')
    },
    next() {
      if (this.current >= this.lists.length - 1) {
        // this.$xh.Toast('已经是最后一题了哦！')
        this.$emit('success')
        return
      }
      this.current++
      this.$emit('update:index', this.current)
      this.$emit('next')
    },
    swiperChange(e) {
      this.current = e.detail.current
      this.$emit('update:index', this.current)
    },
    touchmove() {
      return false
    },
    selecte(info) {
      this.$emit(
        'update:lists',
        this.lists.map(res => {
          if (res.sub_question_id == info.sub_question_id) {
            return {
              ...res,
              ...info
            }
          }
          return res
        })
      )
      if (questionHelper.isMultiple(info)) {
        // 是多选
        if (
          questionHelper.getAnswer(info).length ==
          questionHelper.getSelected(info).length
        ) {
          this.next()
        }
      } else {
        // 不是多选
        this.next()
      }
    }
  },
  watch: {
    index: {
      handler(val) {
        this.current = val
      },
      immediate: true
    }
  }
}
</script>
<style scoped lang="less">
.swiper {
  height: 100%;
  display: flex;
  transition: all 0.25s;
  .swiper-item {
    // overflow-y: auto;
    flex-shrink: 0;
    width: 100vw;
    padding: 48rpx 38rpx;
    height: 100%;
    background-color: #fff;
    .question-answer-box {
      margin-top: 86rpx;
    }
    .answer {
      margin-top: 60rpx;
    }
    .item {
      height: 100%;
      // overflow-y: scroll;
      .scroll-Y {
        height: 100%;
      }
    }
  }
}
</style>
