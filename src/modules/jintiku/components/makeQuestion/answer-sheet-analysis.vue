<template>
  <picker-shell :value="value" @input="input" title="答题卡" ref="picker">
    <view class="bar">
      <view class="yes">
        <view class="flag"></view>
        <view class="text">正确</view>
      </view>
      <view class="error">
        <view class="flag"></view>
        <view class="text">错误</view>
      </view>
      <view class="no">
        <view class="flag"></view>
        <view class="text">未做</view>
      </view>
    </view>
    <view class="questions">
      <view
        class="question flex-center"
        v-for="(item, index) in lists"
        :key="index"
        :class="{
          success: item.user_option && item.user_option == item.questionanswer,
          error: item.user_option && item.user_option != item.questionanswer
        }"
        @click="change(index)"
      >
        {{ index + 1 }}
      </view>
    </view>
  </picker-shell>
</template>
<script>
import pickerShell from '../commen/picker-shell.vue'
export default {
  // 答题卡查看
  name: 'answer-sheet',
  props: {
    value: {
      type: Boolean,
      default: false
    },
    lists: {
      type: Array,
      default: () => []
    }
  },
  components: {
    pickerShell
  },
  data() {
    return {}
  },
  created() {},
  methods: {
    input(val) {
      this.$emit('input', val)
    },
    change(index) {
      this.$refs.picker.cancelFn()
      this.$emit('change', index)
    }
  }
}
</script>
<style scoped lang="less">
.bar {
  display: flex;
  align-items: center;
  justify-content: center;
  .yes,
  .error,
  .no {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 40rpx;
    margin-right: 94rpx;
    .flag {
      width: 16rpx;
      height: 16rpx;
      background-color: #567dfa;
      margin-right: 12rpx;
      border-radius: 50%;
    }
    .text {
      font-size: 28rpx;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #333333;
    }
  }
  .no {
    margin-right: 0;
    .flag {
      background-color: #e4e4e4;
    }
  }
  .error {
    .flag {
      background-color: #f76c5d;
    }
  }
  .yes {
    .flag {
      background-color: #847cf7;
    }
  }
}
.questions {
  padding: 60rpx 40rpx 50rpx;
  padding-bottom: 0;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  .question {
    width: 80rpx;
    height: 80rpx;
    font-size: 32rpx;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 600;
    color: #000000;
    border-radius: 50%;
    background-color: #f6f6f6;
    margin-right: 64rpx;
    margin-bottom: 44rpx;
    &:nth-child(5n) {
      margin-right: 0;
    }
  }
  .question.success {
    background: #eae7fc;
    color: #847cf7;
  }
  .question.error {
    background: #fff3f2;
    color: #f76c5d;
  }
}
</style>
