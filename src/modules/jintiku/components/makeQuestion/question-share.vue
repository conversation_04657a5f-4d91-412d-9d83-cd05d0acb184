<template>
  <picker-shell height="378rpx" :value="value" @input="input" ref="picker">
    <view class="question-share">
      <view class="title">选择操作</view>
      <view class="btns">
        <view class="btn" @click="setemit('download')">
          <image :src="require('../../static/imgs/down.png')" mode="widthFix" />
          <text>保存图片</text>
        </view>
        <view class="btn" @click="setemit('wchat')">
          <image
            :src="require('../../static/imgs/wchat.png')"
            mode="widthFix"
          />
          <text>微信</text>
        </view>
        <view class="btn" @click="setemit('friend')">
          <image
            :src="require('../../static/imgs/friend.png')"
            mode="widthFix"
          />
          <text>朋友圈</text>
        </view>
        <view class="btn" @click="setemit('baidu')">
          <image
            :src="require('../../static/imgs/baidu.png')"
            mode="widthFix"
          />
          <text>百度好友</text>
        </view>
      </view>
    </view>
    <view class="canvas-share"> </view>
  </picker-shell>
</template>
<script>
import pickerShell from '../commen/picker-shell2.vue'
export default {
  // 答题卡查看
  name: 'answer-sheet',
  props: {
    value: {
      type: Boolean,
      default: false
    }
  },
  components: {
    pickerShell
  },
  data() {
    return {}
  },
  created() {},
  methods: {
    input(val) {
      this.$emit('input', val)
    },
    setemit(emitname) {
      if (emitname == 'download') {
        this.$refs.picker.cancelFn()
      }
      this.$emit(emitname)
    }
  }
}
</script>
<style scoped lang="less">
.question-share {
  height: 100%;
  // display: flex;
  padding: 40rpx;
  .title {
    height: 50rpx;
    font-size: 36rpx;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 800;
    color: #03203d;
    line-height: 50rpx;
  }
  .btns {
    margin-top: 60rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    .btn {
      display: flex;
      align-items: center;
      flex-direction: column;
      image {
        width: 92rpx;
        height: 92rpx;
        margin-bottom: 16rpx;
      }
      text {
        height: 28rpx;
        font-size: 24rpx;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: rgba(3, 32, 61, 0.85);
        line-height: 28rpx;
      }
    }
  }
}
</style>
