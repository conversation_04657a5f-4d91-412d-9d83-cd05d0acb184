<template>
  <swiper
    class="swiper"
    :indicator-dots="indicatorDots"
    :autoplay="autoplay"
    :current="swiper.current"
    :duration="swiper.duration"
    :circular="false"
    :disable-touch="disableTouch"
    :skip-hidden-item-layout="skipHiddenItemLayout"
    @animationfinish="animationfinish"
  >
    <swiper-item v-for="(item, index) in swiper.list" :key="index">
      <view class="swiper-item">
        <view class="select-question-box">
          <view v-for="(stem, j) in item.stem_list" :key="j" class="container">
            <!-- 渲染题 -->
            <select-question
              :info="item"
              :stem="stem"
              @selecte="selecte"
              :answer="isShowAnswer(item, stem) || showAnswer ? true : false"
              :isShowAnalysis="
                isShowAnswer(item, stem) || item.lookAnswer || showAnswer
              "
              :questionNumber="item.questionNumber"
            ></select-question>

            <!-- 渲染解析 -->
            <view
              class="answer"
              v-if="isShowAnswer(item, stem) || item.lookAnswer || showAnswer"
            >
              <question-answer
                :info="stem"
                :parentInfo="item"
                :showSelfAnswer="showSelfAnswer"
                :showAnalysis="showAnalysis"
                :showNav="showNav"
                :questionType="item.type"
              ></question-answer>
            </view>
          </view>
        </view>
      </view>
    </swiper-item>
  </swiper>
</template>
<script>
import selectQuestion from './select-question.vue'
import questionAnswer from './question-answer.vue'
import { questionHelper } from '../../utils/index'
export default {
  components: {
    selectQuestion,
    questionAnswer
  },
  props: {
    showAnswer: {
      // 是否强制显示答案
      type: Boolean,
      default: false
    },
    indicatorDots: {
      type: Boolean,
      default: false
    },
    autoplay: {
      type: Boolean,
      default: false
    },
    lists: {
      type: Array,
      default: []
    },
    index: {
      type: Number,
      default: 0
    },
    // 是否显示自己选择的答案
    showSelfAnswer: {
      type: Boolean,
      default: true
    },
    showAnalysis: {
      // 是否显示全站统计
      type: Boolean,
      default: true
    },
    showNav: {
      // 是否显示底部导航
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      // current: 0,
      swiper: {
        list: [], //展示数据
        currentIndex: 0, //真实的index
        current: 0, //swiper当前的index
        recordCurrent: 0, //swiper上次的index
        duration: 300 //动画时常
      }
    }
  },
  mounted() {
    this.lists.forEach((item, key) => {
      item.questionNumber = key + 1
    })
    this.upSwiper(0)
  },
  methods: {
    animationfinish({ detail }) {
      // 用户滑动
      if (detail.source == 'touch' || this.nextStatus == 1) {
        this.nextStatus = 0
        if (this.swiper.recordCurrent == detail.current) {
          return
        }
        // 滑动后的index
        this.upSwiper(
          this.swiper.currentIndex + detail.current - this.swiper.recordCurrent,
          detail.current
        )
      }
    },
    upSwiper(index, touchCurrent) {
      // 防止越界
      if (index < 0 || index >= this.lists.length) {
        return
      }
      this.swiper.currentIndex = index
      this.$emit('update:index', index)
      // 防闪更新数据
      let list = []
      for (
        let i = this.swiper.currentIndex - 1;
        i <= this.swiper.currentIndex + 1;
        ++i
      ) {
        let item = this.lists[this.swiper.currentIndex]
        if (typeof item !== 'undefined') {
          list.push(item)
        }
      }
      let current = 0
      // 只要不是第一个页面 current都是1
      if (index != 0) {
        current = 1
        this.swiper.current = touchCurrent
      }
      this.swiper.duration = 0
      this.$nextTick(() => {
        this.swiper.list = list
        this.swiper.recordCurrent = current
        this.$nextTick(() => {
          this.swiper.current = current
          this.swiper.duration = 300
        })
        // 更新数据
        setTimeout(() => {
          let list = []
          for (
            let i = this.swiper.currentIndex - 1;
            i <= this.swiper.currentIndex + 1;
            ++i
          ) {
            let item = this.lists[i]
            if (typeof item !== 'undefined') {
              list.push(item)
            }
          }
          this.swiper.list = list
        }, 300)
        //获取题
        if (this.swiper.currentIndex >= this.lists.length - 1) {
          this.$emit('getNextPage')
          return
        }
      })
    },
    isShowAnswer(item, stem) {
      if (stem.multiple) {
        // 多选
        return stem.selected.length >= stem.answer.length
      } else {
        // 单选
        return stem.selected.length > 0
      }
    },
    prev() {
      if (this.swiper.currentIndex <= 0) {
        this.$xh.Toast('已经是第一题了哦！')
        return
      }

      this.swiper.currentIndex--
      this.upSwiper(this.swiper.currentIndex)
    },
    next() {
      if (this.swiper.currentIndex >= this.lists.length - 1) {
        // this.$xh.Toast('已经是最后一题了哦！')
        this.$emit('last')
        return
      }
      //自动下一题时展示动画效果
      this.nextStatus = 1
      this.swiper.current++
      // this.swiper.currentIndex++
      // this.upSwiper(this.swiper.currentIndex)
    },

    selecte(info) {
      this.$emit(
        'update:lists',
        this.lists.map(res => {
          if (res.sub_question_id == info.sub_question_id) {
            return {
              ...res,
              ...info
            }
          }
          return res
        })
      )
      if (questionHelper.diffAnswer(info)) {
        // 获取用户是否做对了
        this.next()
      }
    }
  },
  watch: {
    index: {
      handler(val) {
        if (this.swiper.currentIndex != val) {
          this.upSwiper(val)
        }
      },
      immediate: true
    },
    lists: {
      handler(val) {
        this.swiper.list = this.swiper.list.map(ee => {
          for (let key = 0; key < this.lists.length; key++) {
            let item = this.lists[key]
            item.questionNumber = key + 1
            if (ee.id == item.id) {
              return item
            }
          }
        })
      },
      immediate: true
    }
  }
}
</script>
<style scoped lang="less">
.swiper {
  height: 100%;
  .swiper-item {
    overflow-y: auto;
    padding: 48rpx 38rpx;
    height: 100%;
    background-color: #fff;
    .question-answer-box {
      margin-top: 86rpx;
    }
    .answer {
      margin-top: 60rpx;
    }
  }
}
</style>
