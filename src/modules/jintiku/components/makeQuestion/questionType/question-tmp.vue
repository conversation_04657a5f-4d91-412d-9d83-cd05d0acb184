<template>
  <view class="question_a1">
    <view class="title_box">
      <view
        class="thematic_stem question_title"
        v-if="isOrdinaryCases(data.type)"
      >
        <view>{{ data.sort }}、病例：</view>
        <view v-html="formattingCases(data.thematic_stem)" class="content" />
      </view>
      <view class="question_title" v-if="getQuestionTitleShow(data.type)">
        <view>{{ data.sort }}、</view>
        <view style="display: flex">
          <view
            v-if="data.stem_list && data.stem_list.length"
            v-html="data.stem_list[0].content"
            style="word-break: break-all"
          ></view>
          <view v-else> 本题暂无题目 </view>
          <!-- 里面有音频就渲染音频 -->
          <view
            v-if="data.stem_list[0].content.includes('audio')"
            style="width: 550rpx"
          >
            <view
              class="audio"
              v-for="(src, a) in getAudioAll(data.stem_list[0].content)"
              :key="a"
            >
              <my-audio
                :music="src"
                :name="'(' + (i + 1) + ')'"
                controls
              ></my-audio>
            </view>
          </view>
        </view>
      </view>
      <!-- 这里是图片和音频的单独解析区 -->
      <render-img-and-video
        :stem="data"
        v-if="
          data.resource_info &&
          (data.resource_info.img.path || data.resource_info.video.path)
        "
      />
      <view class="question_title select_title" v-if="isB1(data.type)">
        {{ data.sort }}、B1第{{ question_sort + 1 }}组题
      </view>
      <!-- <view class="question_title select_title" v-if="data.type == 5">
        <view
          class="select"
          v-for="(item, index) in JSON.parse(data.stem_list[0].option)"
          :key="index"
        >
          <view>{{ selectList[index] }}.</view>
          <view v-html="item" style="word-break: break-all"></view>
        </view>
      </view> -->
    </view>
    <view v-for="(info, i) in data.stem_list" :key="i">
      <view
        v-if="isOrdinaryCases(data.type) || isB1(data.type)"
        class="select_list_title"
      >
        <view>({{ i + 1 }})、</view>
        <view
          v-html="info.content ? info.content : '暂无'"
          style="word-break: break-all"
        ></view>
        <view v-if="info.content.includes('audio')" style="width: 550rpx">
          <view
            class="audio"
            v-for="(src, a) in getAudioAll(info.content)"
            :key="a"
          >
            <my-audio
              :music="src"
              :name="'(' + (i + 1) + ')'"
              controls
            ></my-audio>
          </view>
        </view>
      </view>
      <view class="select_box" v-if="!isSubjective(data.type)">
        <view class="select" v-for="(item, index) in info.option" :key="index">
          <view>{{ selectList[index] }}.</view>
          <view v-html="item"></view>
        </view>
      </view>
      <view class="explain_box" style="margin-top: 24rpx" v-else>
        <view class="explain"> 作答： </view>
        <view
          class="content"
          v-html="
            info.sub_answer.length
              ? info.sub_answer.join('') || '未作答'
              : '未作答'
          "
        >
        </view>
      </view>
      <view class="is_right">
        <view
          class="is_right_button correct"
          :class="{
            correct: info.answer_status == 1,
            error: info.answer_status == 2,
            half: info.answer_status == 3
          }"
          v-if="info.answer_status != 0"
        >
          {{ getStateText(info.answer_status) }}
        </view>
        <view
          class="score"
          :class="{
            blue_text: info.answer_status == 1 || isSubjective(data.type),
            red_text: info.answer_status == 2,
            yellow_text: info.answer_status == 3
          }"
          style="display: flex"
        >
          得分：
          <!-- 不是简答题 -->
          <view v-if="!isSubjective(data.type)"> {{ info.get_score }}分 </view>
          <!-- 阅卷结束 && 是简答题 -->
          <view v-else-if="isCorrect == 1 && isSubjective(data.type)">
            {{ info.get_score }}分
          </view>
          <!-- 阅卷中 && 是简答题 -->
          <view v-else-if="isCorrect == 2 && isSubjective(data.type)">
            待阅卷
          </view>
        </view>
      </view>
      <view class="answer" v-if="!isSubjective(data.type)">
        <view
          class="blue_text"
          v-if="
            info.answer_status != 1 &&
            isSelectedType(data.type) &&
            isPublishAnswer == '1'
          "
          style="margin-right: 22rpx; display: flex"
        >
          <view style="flex-shrink: 0">正确答案：</view>
          <view> {{ info.answerName.sort().join('、') }} </view>
        </view>
        <view
          class="blue_text"
          v-if="
            info.answer_status != 1 &&
            isFillBlanks(data.type) &&
            isPublishAnswer == '1'
          "
          style="margin-right: 22rpx; display: flex"
        >
          <view style="flex-shrink: 0">正确答案：</view>
          <view>{{ info.answerName.join('、') }} </view>
        </view>
        <view style="color: #000000; display: flex">
          <view style="flex-shrink: 0">您的答案：</view>
          <!-- 选择题 -->
          <view v-if="isSelectedType(data.type)">
            {{ info.sub_answer_name.sort().join('、') || '未作答' }}
          </view>
          <!-- 填空题 -->
          <view v-if="isFillBlanks(data.type)">
            {{ info.sub_answer.join('、') || '未作答' }}
          </view>
        </view>
      </view>
    </view>
    <view class="explain_box" v-if="isPublishAnswer == '1'">
      <view class="explain"> 解析： </view>
      <view v-html="data.parse" class="content" v-if="data.parse" />
      <view v-else class="content">暂无解析</view>
    </view>
    <view
      v-if="data.parse && data.parse.includes('audio')"
      style="width: 550rpx"
    >
      <view class="audio" v-for="(src, a) in getAudioAll(data.parse)" :key="a">
        <my-audio
          style="text-align: left"
          :music="src"
          name="解析"
          controls
        ></my-audio>
      </view>
    </view>
    <view class="explain_box">
      <view class="explain"> 知识点： </view>
      <view
        v-html="data.knowledge_ids_name"
        class="content"
        v-if="data.knowledge_ids_name"
      />
      <view v-else class="content">暂无</view>
    </view>
    <view class="explain_box">
      <view class="explain"> 难易度： </view>
      <view class="star">
        <view v-for="item in 5" :key="item" style="margin-right: 9rpx">
          <image
            src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/16953700953269c48169537009532642625_%E7%BC%96%E7%BB%84%E5%A4%87%E4%BB%BD%205%402x.png"
            alt=""
            v-if="item < data.level"
          />
          <image
            src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/16953700763015ff616953700763011336_Fill%202%402x.png"
            alt=""
            v-else
          />
        </view>
      </view>
    </view>
  </view>
</template>
<script>
import { isSubjective, getAudioAll } from '../../../utils/index'
import myAudio from '../../../components/commen/my-audio.vue'
import renderImgAndVideo from '../render-img-and-video.vue'
import {
  isFillBlanks,
  isSelectedType,
  isOrdinaryCases,
  isSingle,
  isMultiple,
  isRightWrong,
  isB1
} from '../../../utils/index'
export default {
  components: {
    myAudio,
    renderImgAndVideo
  },
  name: 'question-a-one', // 答题详情
  props: {
    data: {},
    isCorrect: {
      type: String
    },
    question_sort: {},
    isPublishAnswer: {
      type: String
    }
  },
  data() {
    return {
      selectList: [
        'A',
        'B',
        'C',
        'D',
        'E',
        'F',
        'G',
        'H',
        'I',
        'J',
        'K',
        'L',
        'M',
        'N',
        'O',
        'P',
        'Q',
        'R',
        'S',
        'T',
        'U',
        'V',
        'W',
        'X',
        'Y',
        'Z'
      ]
    }
  },
  created() {
    this.init()
  },
  methods: {
    isFillBlanks,
    isSelectedType,
    isSubjective,
    isOrdinaryCases,
    isSingle,
    isMultiple,
    isRightWrong,
    isB1,
    init() {
      if (!this.data.stem_list) {
        return
      }
      this.data.stem_list.map(res => {
        // if (isSubjective(this.data.type)) {
        //   return false
        // }
        try {
          res.answerName = JSON.parse(res.answer).map(item => {
            if (isFillBlanks(this.data.type)) {
              return item
            } else {
              return this.selectList[item] ? this.selectList[item] : '暂无'
            }
          })
        } catch (error) {
          res.answerName = []
        }
        try {
          res.sub_answer = JSON.parse(res.sub_answer)
        } catch (error) {
          res.sub_answer = []
        }
        try {
          res.sub_answer_name = res.sub_answer
            ? res.sub_answer.map(item => {
                return this.selectList[item] ? this.selectList[item] : ''
              })
            : []
        } catch (error) {}
        try {
          res.option = JSON.parse(res.option)
        } catch (error) {
          res.option = []
        }
      })
    },
    getStateText(type) {
      switch (type) {
        case '1':
          return '正确'
        case '2':
          return '错误'
        case '3':
          return '半对'
        default:
          return '--'
      }
    },
    getAudioAll: getAudioAll,
    getQuestionTitleShow(type) {
      return (
        isSingle(type) ||
        isMultiple(type) ||
        isFillBlanks(type) ||
        isRightWrong(type) ||
        isSubjective(type)
      )
    },
    formattingCases(data) {
      return data
        .replace(/style=""/gi, '')
        .replace(/<img([\s\w"-=/.:;]+)>/gi, "<img style='max-width: 100%;' $1>")
    }
  }
}
</script>
<style lang="scss" scoped>
.question_a1 {
  padding: 20rpx 24rpx;
  border: 1rpx solid #eeeeee;
  border-radius: 12rpx;
  .title_box {
    display: flex;
    justify-content: flex-start;
    align-items: flex-start;
    font-size: 26rpx;
    color: #161f30;
    .question_title {
      display: flex;
      // flex-wrap: wrap;
      position: relative;
    }
    .thematic_stem {
      flex-wrap: wrap;
      view:last-child {
        flex: 1;
      }
    }
    .select_title {
      flex: 1;
      width: 100%;
      flex-wrap: wrap;
      .select {
        display: flex;
        width: 45%;
        margin: 20rpx 20rpx 0 0;
      }
    }
  }
  .select_list_title {
    display: flex;
    justify-content: flex-start;
    align-items: flex-start;
    font-size: 26rpx;
    color: #161f30;
    margin-top: 20rpx;
  }
  .select_box {
    font-size: 26rpx;
    color: #161f30;
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    .select {
      // flex: 0.5;
      margin: 20rpx 20rpx 0 0;

      width: 45%;
      display: flex;
      justify-content: flex-start;
      align-items: flex-start;
    }
  }
  .is_right {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    margin: 20rpx 0 25rpx 0;
    font-size: 28rpx;
    .is_right_button {
      // padding: 22rpx 14rpx;
      width: 100rpx;
      height: 56rpx;
      text-align: center;
      line-height: 56rpx;
      border-radius: 8rpx;
      color: #ffffff;
      margin-right: 20rpx;
    }
    .correct {
      background: #2e68ff;
    }
    .error {
      background: #f04f54;
    }
    .half {
      background: #f99300;
    }
    .score {
      margin-right: 20rpx;
    }
    .error_score {
      color: #f04f54;
    }
    .blue_text {
      color: #2e68ff;
    }
    .red_text {
      color: #f04f54;
    }
    .yellow_text {
      color: #f99300;
    }
  }
  .answer {
    margin: 25rpx 0 20rpx 0;
    font-size: 28rpx;
    display: flex;
    .blue_text {
      color: #2e68ff;
    }
  }
  .explain_box {
    display: flex;
    justify-content: flex-start;
    align-items: flex-start;
    font-size: 26rpx;
    flex-wrap: wrap;

    .explain {
      color: #161f30;
      font-weight: 500;
      line-height: 52rpx;
    }
    .content {
      color: #161f30;
      line-height: 52rpx;
      flex: 1;
      word-break: break-all;
    }
    .star {
      // margin-left: auto;
      display: flex;
      justify-content: flex-start;
      align-items: flex-start;
      margin-top: 10rpx;
      image {
        width: 24rpx;
        height: 24rpx;
      }
      image:last-child {
        margin-right: 0;
      }
    }
  }
}
</style>
