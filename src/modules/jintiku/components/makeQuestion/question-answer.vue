<template>
  <view class="select-question">
    <!-- v-if="questionType != 8" -->
    <view class="answer" v-if="isSelectedType(questionType)">
      <view class="success">
        <text>正确答案</text>
        <text class="orange" v-for="(answer, i) in info.answer" :key="i">
          {{ ABC[answer] }}
        </text>
      </view>
      <view class="error" v-if="showSelfAnswer">
        <text>您的答案</text>
        <text v-if="!info.selected.length" class="zis">未选择</text>
        <text v-for="(selected, i) in info.selected" :key="i" class="zis">
          {{ ABC[selected] }}
        </text>
      </view>
    </view>
    <!-- 填空题 -->
    <view v-if="isFillBlanks(questionType)">
      <view class="answer">
        <view class="success">
          <text>正确答案</text>
          <view class="mt20">
            <view
              class="orange flex mb10"
              v-for="(answer, i) in info.option"
              :key="i"
            >
              <text class="mr10">{{ ABC[i] }}</text>
              .
              <view v-html="answer"></view>
            </view>
          </view>
        </view>
      </view>
      <view class="answer">
        <view class="error" v-if="showSelfAnswer">
          <text>您的答案</text>
          <text v-if="!info.selected.length" class="zis">未作答</text>
          <view class="mt20">
            <view
              class="orange flex mb10"
              v-for="(answer, i) in info.selected"
              :key="i"
            >
              <text class="mr10">{{ ABC[i] }}</text>
              .
              <view v-html="answer"></view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <view class="teacher-analysis">
      <view class="title">
        <view class="line"> </view>
        <text>名师解析</text>
      </view>
      <view class="desc" v-html="info.parse"> </view>
    </view>
    <view class="teacher-analysis" v-if="showAnalysis">
      <view class="title">
        <view class="line"> </view>
        <text>全站统计</text>
      </view>
      <view class="static">
        <view class="static-list">
          <view class="static-title">被作答次数</view>
          <view class="static-text">
            {{ info && info.doQuestionNum ? info.doQuestionNum : '0' }}次
          </view>
        </view>
        <view class="static-line"></view>
        <view class="static-list">
          <view class="static-title">全站正确率</view>
          <view class="static-text">
            {{ info && info.accuracy ? info.accuracy + '%' : '暂无' }}
          </view>
        </view>
        <view class="static-line"></view>
        <view class="static-list">
          <view class="static-title">易错选项</view>
          <view class="static-text">
            {{ info && info.errorOption ? ABC[info.errorOption] : '暂无' }}
          </view>
        </view>
      </view>
    </view>
    <view class="teacher-analysis">
      <view class="title">
        <view class="line"> </view>
        <text>知识点</text>
      </view>
      <view class="desc">
        <text v-if="!info.knowledge_ids_name.length">暂无相关知识点</text>
        <text v-for="(res, i) in info.knowledge_ids_name" :key="i">
          {{ res }}
        </text>
      </view>
    </view>
    <!-- 底部导航 -->
    <!-- showNav -->
    <view class="navs" v-if="false">
      <view class="nav" @click="goDetail('pages/examEntry/index')">
        <image
          src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/1695032802747e693169503280274754832_kdkj.png"
          mode="widthFix"
        />
        <text>考点词条</text>
      </view>
      <view class="line"></view>
      <view class="nav" @click="goDetail('pages/questionChallenge/index')">
        <image
          src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/16950328580883781169503285808899897_paper.png"
          mode="widthFix"
        />
        <text>真题闯关</text>
      </view>
    </view>
  </view>
</template>
<script>
import { isSubjective, isFillBlanks, isSelectedType } from '../../utils/index'
export default {
  name: 'question-answer', // 答案解析
  props: {
    showSelfAnswer: {
      // 是否显示自己选择的答案
      type: Boolean,
      default: true
    },
    showAnalysis: {
      // 是否显示全站统计
      type: Boolean,
      default: true
    },
    showNav: {
      // 是否显示底部导航
      type: Boolean,
      default: true
    },
    info: {},
    parentInfo: {
      default: () => {}
    },
    questionType: {
      type: String
    }
  },
  data() {
    return {
      ABC: [
        'A',
        'B',
        'C',
        'D',
        'E',
        'F',
        'G',
        'H',
        'I',
        'J',
        'K',
        'L',
        'M',
        'N',
        'O',
        'P',
        'Q',
        'R',
        'S',
        'T',
        'U',
        'V',
        'W',
        'X',
        'Y',
        'Z'
      ]
    }
  },
  methods: {
    isSelectedType,
    isFillBlanks,
    goDetail(url) {
      this.$xh.push('jintiku', url)
    }
  }
}
</script>
<style lang="less" scoped>
.select-question {
  .answer {
    display: flex;
    align-items: center;
    margin-bottom: 60rpx;
    .success,
    .error {
      text {
        height: 44rpx;
        font-size: 32rpx;
        font-family: PingFangSC-Semibold, PingFang SC;
        font-weight: 600;
        color: #333333;
        line-height: 44rpx;
      }

      .zis {
        color: #f76c5d;
        margin-left: 20rpx;
      }
    }
    .success {
      margin-right: 74rpx;
      .orange {
        color: #847cf7;
        margin-left: 10rpx;
      }
    }
  }
  .teacher-analysis {
    .title {
      height: 32rpx;
      font-size: 32rpx;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 600;
      color: #387dfc;
      line-height: 32rpx;
      margin-bottom: 22rpx;
      display: flex;
      align-items: center;
      .line {
        width: 12rpx;
        height: 46rpx;
        background: #387dfc;
        border-radius: 6px;
        margin-right: 22rpx;
      }
    }
    .desc {
      // min-height: 120rpx;
      font-size: 26rpx;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      // color: rgba(47, 51, 57, 0.75);
      color: #333333;
      line-height: 40rpx;
      margin-bottom: 30rpx;
    }
    .static {
      height: 140rpx;
      background: #f6f6f6;
      border-radius: 20rpx;
      padding: 30rpx 45rpx;
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin: 20rpx 0;
      margin-bottom: 40rpx;
      .static-list {
        .static-title {
          font-size: 28rpx;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #999999;
          margin-bottom: 22rpx;
          text-align: center;
        }
        .static-text {
          font-size: 32rpx;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #333333;
          line-height: 32rpx;
          text-align: center;
        }
      }
      .static-line {
        width: 2rpx;
        height: 38rpx;
        background: #d2d2d2;
      }
    }
  }
  .navs {
    display: flex;
    align-items: center;
    justify-content: space-around;
    margin-top: 50rpx;
    .nav {
      display: flex;
      align-items: center;
      justify-content: center;
      image {
        width: 24rpx;
        height: 32rpx;
        margin-right: 8rpx;
      }
      text {
        font-size: 28rpx;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #999999;
      }
    }
    .line {
      width: 2rpx;
      height: 32rpx;
      background: #f0f0f0;
      border-radius: 1px;
    }
  }
}
.mb20 {
  margin-bottom: 20rpx;
}
.mt20 {
  margin-top: 20rpx;
}
.mb10 {
  margin-bottom: 10rpx;
}
.mr10 {
  margin-right: 10rpx;
}
</style>
