<template>
  <view class="bottom-utils">
    <view class="utils">
      <slot />
      <view
        class="gjb end button"
        @click="lookAnswer"
        v-if="utils.includes('lookResolution')"
      >
        <image
          src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/169508959924626a6169508959924677188_look.png"
          mode="widthFix"
        />
        <text>
          {{
            lists[current].lookAnswer ||
            lists[current].stem_list[0].selected.length
              ? '重新做题'
              : '查看解析'
          }}
        </text>
      </view>
      <view
        class="gjb dtk button"
        @click="sheetShow = true"
        v-if="
          utils.includes('answerSheet') || utils.includes('errorAnswerSheet')
        "
      >
        <image
          src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/16950896298723a451695089629872551_dtk.png"
          mode="widthFix"
        />
        <text>答题卡</text>
      </view>
      <view
        class="gjb dtk button"
        @click="collect"
        v-if="utils.includes('collect')"
      >
        <image
          src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/169509153447289aa169509153447267180_start.png"
          mode="widthFix"
          v-if="currentData.is_collect && currentData.is_collect == '1'"
        />
        <image
          src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/16950894788017a96169508947880185018_c.png"
          mode="widthFix"
          v-else
        />
        <text>
          {{
            currentData.is_collect && currentData.is_collect == '1'
              ? '已收藏'
              : '收藏'
          }}
        </text>
      </view>
      <view
        class="gjb dtk button"
        @click="setError"
        v-if="utils.includes('errorCorrection')"
      >
        <image
          src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/1695089531914e2a1169508953191595864_jiucuo.png"
          mode="widthFix"
        />
        <text>纠错</text>
      </view>

      <view class="gjb next" v-if="isnextChapter">
        <view class="btn button flex-center" @click="$emit('nextChapter')">
          下一节
        </view>
      </view>
    </view>
    <!-- 答题卡 -->
    <answer-sheet
      v-model="sheetShow"
      :questions="lists"
      v-if="utils.includes('answerSheet')"
      @change="sheetchange"
    />
    <!-- 错题卡 -->
    <error-answer-sheet
      v-model="sheetShow"
      :questions="lists"
      v-if="utils.includes('errorAnswerSheet')"
      @change="sheetchange"
    />
    <!-- 纠错 -->
    <error-correction
      v-model="errorShow"
      :question_id="currentData.question_id"
      :question_version_id="currentData.id"
      :version="currentData.version"
    ></error-correction>
  </view>
</template>
<script>
import answerSheet from '../../components/makeQuestion/answer-sheet.vue'
import errorAnswerSheet from '../../components/makeQuestion/error-answer-sheet.vue'
import errorCorrection from '../../components/makeQuestion/error-correction.vue'
import { collect } from '../../api/commen'
export default {
  props: {
    current: {
      type: Number
    },
    lists: {
      type: Array
    },
    utils: {
      type: Array,
      default: [
        'lookResolution', // 查看解析
        'answerSheet', // 答题卡
        // 'errorAnswerSheet', // 错题卡
        'collect', // 收藏
        'errorCorrection' // 纠错
      ]
    },
    isnextChapter: {
      type: Boolean,
      default: false
    }
  },
  components: {
    answerSheet,
    errorCorrection,
    errorAnswerSheet
  },
  onShow() {},
  computed: {
    currentData() {
      if (!this.lists.length) {
        return {}
      }
      return this.lists[this.current]
    }
  },
  data() {
    return {
      sheetShow: false,
      errorShow: false
    }
  },
  methods: {
    lookAnswer() {
      if (this.lists[this.current].stem_list[0].selected.length) {
        // 如果用户选择答案了 那么就是重做
        this.lists[this.current].lookAnswer = false
        this.lists[this.current].stem_list[0].selected = []
        this.lists[this.current].user_option = ''
      } else {
        // 没选择答案就是查看解析
        this.lists[this.current].lookAnswer =
          !this.lists[this.current].lookAnswer
      }
      this.$emit('update:lists', this.lists)
    },
    sheetchange(index) {
      this.$emit('update:current', index)
    },
    // 纠错
    setError() {
      this.errorShow = true
    },
    collect() {
      // if (this.currentData.is_collect == '1') {
      //   this.$xh.Toast('已收藏！')
      //   return
      // }
      // 收藏接口
      collect({
        question_version_id: this.currentData.id,
        status: this.currentData.is_collect == '1' ? '2' : '1',
        type: '1'
      }).then(data => {
        // 收藏成功
        this.currentData.is_collect =
          this.currentData.is_collect == '1' ? '2' : '1'
        this.currentData.is_collect == '1'
          ? this.$xh.Toast('收藏成功')
          : this.$xh.Toast('取消收藏')
        this.$emit('update:lists', this.lists)
      })
    }
  }
}
</script>

<style scoped lang="less">
.utils {
  position: fixed;
  left: 0;
  bottom: 0;
  width: 100vw;
  height: 80rpx;
  padding-bottom: 42rpx;
  padding-top: 36rpx;
  background-color: #fff;
  display: flex;
  align-items: center;
  justify-content: space-around;
  height: 158rpx;
  .gjb {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    // width: 120rpx;
    image {
      width: 40rpx;
      height: 40rpx;
      margin-bottom: 14rpx;
    }
    text {
      font-size: 24rpx;
      color: rgba(41, 65, 90, 0.75);
    }
    .btn {
      width: 192rpx;
      height: 80rpx;
      border-radius: 40rpx;
      border: 1px solid #847cf7;
      color: #847cf7;
      font-size: 26rpx;
    }
  }
  .next {
    .btn {
      border-radius: 40rpx;
      border: 1px solid transparent;
      color: #fff;
      background: linear-gradient(270deg, #6d7dff 0%, #b3a1ff 100%);
    }
  }
}
</style>
