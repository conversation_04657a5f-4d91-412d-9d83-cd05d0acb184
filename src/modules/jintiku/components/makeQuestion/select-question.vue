<template>
  <view
    class="select-question"
    :style="{ marginTop: isFouce ? '-100rpx' : '0' }"
  >
    <view class="title">
      <view>
        <text v-if="info.type != 9">[{{ info.type_name }}题型]</text>
        <text v-else>[{{ info.type_name }}]</text>
        <text v-if="stem.multiple && !isFillBlanks(info.type)">（多选）</text>
      </view>
      <view v-if="info.thematic_stem" style="display: flex; flex-wrap: wrap">
        <view>病例：</view>
        <view v-html="info.thematic_stem" />
      </view>
      <view class="question_content">
        <view class="question_number">{{ questionNumber }}、</view>
        <view v-html="stem.content" v-if="stem.content" />
      </view>
      <view v-if="stem.content && stem.content.includes('audio')">
        <view
          class="audio"
          v-for="(src, a) in getAudioAll(stem.content)"
          :key="a"
        >
          <my-audio
            style="text-align: left"
            :music="src"
            :name="'[' + info.type_name + '题型]'"
            controls
          ></my-audio>
        </view>
      </view>
      <!-- 这里是图片和音频的单独解析区 -->
      <render-img-and-video
        :stem="stem"
        v-if="
          stem.resource_info &&
          (stem.resource_info.img || stem.resource_info.video)
        "
        ref="audioHandle"
      />
    </view>
    <!-- 渲染简答题 -->
    <view v-if="isSubjective(info.type) && !answer">
      <textarea
        class="textarea"
        maxlength="9999"
        v-model="info.sub_answer"
        placeholder="请输入"
        :cursor="60"
        :adjust-position="true"
        :show-confirm-bar="false"
        @input="onInputChange"
        @focus="isFouce = true"
        @blur="isFouce = false"
      />
      <!-- v-model="info.sub_answer" -->
      <!-- <view class="editor-box">
        <editor
          :id="id"
          :class="['id', `editor`]"
          placeholder="请输入"
          @input="onInputChange"
          @ready="onEditorReady"
        ></editor>
      </view> -->
    </view>
    <!-- 渲染填空题 做题时候的渲染-->
    <view v-else-if="isFillBlanks(info.type) && !answer">
      <view
        class="tk-options flex"
        v-for="(item, index) in stem.answer"
        :key="index"
      >
        <text>({{ index + 1 }})、</text>
        <input
          class="uni-input"
          :placeholder="`请输入第${index + 1}个填空内容`"
          placeholder-class="placeholder-input"
          :value="stem.selected[index]"
          @input="tkinput($event, index)"
        />
      </view>
    </view>
    <!-- 渲染填空题 看答案时候的渲染 -->
    <view v-else-if="isFillBlanks(info.type) && answer">
      <!-- 渲染空数据 -->
    </view>
    <!-- 渲染非简答题就是选择题 v-if="!isSubjective(info.type)" -->
    <view class="selects" v-else>
      <view
        class="select"
        v-for="(item, index) in stem.option"
        :key="index"
        :class="{
          selected: stem.selected.includes(index) && !answer,
          success: answer && stem.answer.includes(index),
          error:
            answer &&
            stem.selected.includes(index) &&
            !stem.answer.includes(index)
        }"
        @click="select(item, index)"
      >
        <view class="english"> {{ transition[index] }}</view>
        <view class="line" />
        <view class="desc" v-html="item" />
      </view>
    </view>
    <view style="display: none">{{ updataNum }}</view>
  </view>
</template>
<script>
import { isSubjective, getAudioAll, isFillBlanks } from '../../utils'
import myAudio from '../../components/commen/my-audio.vue'
import renderImgAndVideo from './render-img-and-video.vue'
export default {
  name: 'select-question', // 选择题
  components: {
    myAudio,
    renderImgAndVideo
  },
  props: {
    info: {
      type: Object
    },
    stem: {
      type: Object
    },
    answer: {
      // 是否是解析阶段
      type: Boolean,
      default: false
    },
    isShowAnalysis: {
      // 是否显示解析了
      type: Boolean,
      default: false
    },
    questionNumber: {
      type: Number,
      default: 1
    },
    current: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      key: 0,
      transition: [
        'A',
        'B',
        'C',
        'D',
        'E',
        'F',
        'G',
        'H',
        'I',
        'J',
        'K',
        'L',
        'M',
        'N',
        'O',
        'P',
        'Q',
        'R',
        'S',
        'T',
        'U',
        'V',
        'W',
        'X',
        'Y',
        'Z'
      ],
      editorCtx: null,
      id: 'edit' + (Math.random() * 100).toFixed(0),
      isFouce: false,
      currentNmuber: 0,
      updataNum: 0
    }
  },
  watch: {
    current: {
      handler(nv, ov) {
        console.log('我变了')
        this.$refs.audioHandle?.stopPlay && this.$refs.audioHandle?.stopPlay()
      }
    }
  },
  methods: {
    isSubjective,
    isFillBlanks,
    onEditorReady(e) {
      const query = uni.createSelectorQuery().in(this)
      query
        .select(`#${this.id}`)
        .context(res => {
          this.editorCtx = res.context
          if (this.info.sub_answer) {
            this.editorCtx.setContents({
              html: this.info.sub_answer
            })
          }
        })
        .exec()
      // uni
      //   .createSelectorQuery()
      //   .select(`#${this.id}`)
      //   .context(res => {
      //     console.log(res)
      //     this.editorCtx = res.context
      //   })
      //   .exec()
      //   .in(this)
      // }, 100)
    },
    select(item, index) {
      if (this.answer || this.isShowAnalysis) {
        // 如果有答案了 就不让选了
        return
      }
      if (this.stem.multiple) {
        // 多选题逻辑
        let isSelece = this.stem.selected.includes(index)
        if (!isSelece) {
          // 多选题的选中
          this.stem.selected.push(index)
        } else {
          // 多选题的取消选中
          let idx = this.stem.selected.indexOf(index)
          this.stem.selected.splice(idx, 1)
        }
      } else {
        // 单选题的选中
        this.stem.selected.pop()
        this.stem.selected.push(index)
      }
      this.$emit('selecte', {
        ...this.info,
        stem_list: [
          this.stem, // 当前这个小题的所有信息
          ...this.info.stem_list.filter(item => {
            return item.id != this.stem.id
          })
        ],
        user_option: this.stem.selected.join(',')
      })
      this.updataNum++
    },
    tkinput(e, i) {
      let value = e.detail.value
      this.stem.selected[i] = value
      this.$emit('selecte', {
        ...this.info,
        stem_list: [
          this.stem,
          ...this.info.stem_list.filter(item => {
            return item.id != this.stem.id
          })
        ],
        user_option: this.stem.selected.join(',')
      })
      this.updataNum++
      console.log(this.info)
    },
    onInputChange(e) {
      // this.info.type == 8 || this.info.type == 9 || this.info.type == 10
      if (isSubjective(this.info.type) || isFillBlanks(this.info.type)) {
        // 8就是简答题
        this.stem.selected[0] = this.info.sub_answer
        this.$emit('input', {
          ...this.info,
          stem_list: [
            this.stem,
            ...this.info.stem_list.filter(item => {
              return item.id != this.stem.id
            })
          ],
          user_option: this.stem.selected.join(',')
        })
      }
    },
    focus() {
      uni.pageScrollTo({
        scrollTop: 0, // 滚动到页面的目标位置（单位px）
        duration: 300 // 滚动动画的时长，默认300ms，单位 ms
      })
    },
    getAudio(html) {
      try {
        const regex = /<audio\s*src="([^"]*)"[^>]*>/
        const match = html.match(regex)
        if (match && match[1]) {
          return match[1]
        }
      } catch (error) {
        return ''
      }
      return ''
    },
    getAudioAll: getAudioAll
  }
}
</script>
<style lang="less" scoped>
.editor-box {
  border: 1px solid #ccc;
  border-radius: 20rpx;
  padding: 20rpx;
  .editor {
    width: 100%;
    height: 300px;
    background-color: #fff;
  }
}
.audio {
  width: 100%;
  height: auto;
  margin-top: 20rpx;
}
.select-question {
  .title {
    font-size: 32rpx;
    color: #000000;
    line-height: 48rpx;
    margin-bottom: 52rpx;
    view:first-child {
      color: #387dfc;
      margin-right: 4rpx;
    }
    .question_content {
      margin-top: 12rpx;
      display: flex;
      justify-content: flex-start;
      align-items: flex-start;
      // line-height: 60rpx;
      .question_number {
        color: #000 !important;
      }
    }
  }
  .selects {
    .select {
      width: 100%;
      min-height: 60rpx;
      border-radius: 50rpx;
      border: 2rpx solid #ececec;
      display: flex;
      align-items: center;
      padding: 15px 40rpx;
      margin-bottom: 32rpx;
      .english {
        width: 22rpx;
        font-weight: 600;
        margin-right: 28rpx;
        color: #333333;
      }
      .line {
        width: 2rpx;
        height: 32rpx;
        background-color: #cacaca;
        margin-right: 28rpx;
      }
      .desc {
        flex: 1;
        font-size: 32rpx;
      }
    }
    .select:last-child {
      margin-bottom: 0;
    }
    .selected {
      border: 2rpx solid #567dfa;
      background: rgba(124, 191, 247, 0.18);
    }
    .error {
      position: relative;
      border: 2rpx solid rgba(247, 119, 105, 0.6);
      background: #fff3f2;
      &::before {
        position: absolute;
        content: '';
        right: 28rpx;
        top: 0;
        bottom: 0;
        margin: auto 0;
        width: 48rpx;
        height: 48rpx;
        background: url('https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/16950952878505e2f169509528785094708_error.png')
          no-repeat;
        background-size: cover;
      }
    }
    .success {
      border: 2rpx solid rgba(132, 124, 247, 0.5);
      background: rgba(132, 124, 247, 0.18);
    }
  }
  .textarea {
    border: 2rpx solid #ccc;
    width: 100%;
    border-radius: 32rpx;
    margin-bottom: 52rpx;
    padding: 24rpx;
    box-sizing: border-box;
    font-size: 26rpx;
    height: 600rpx;
  }
  .save {
    display: flex;
    justify-content: flex-end;
    .save_button {
      width: 100rpx;
      height: 56rpx;
      background: #2574fe;
      border-radius: 8rpx;
      text-align: center;
      line-height: 56rpx;
      color: #fff;
      font-size: 28rpx;
    }
  }
}
.img-and-video {
  width: 100%;
  position: relative;
  .bg-img {
    width: 100%;
    height: auto;
  }
  .position {
    position: absolute;
    display: flex;
    align-items: center;
    justify-content: center;
    image {
      width: 50rpx;
      height: 50rpx;
    }
  }
  .video {
    width: 100%;
    .myVideo {
      width: 100%;
    }
  }
}
.ripple-effect {
  position: absolute;
  width: 50rpx;
  height: 50rpx;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.3);
  transform: scale(0);
  animation-name: ripple-animate;
  animation-duration: 1s;
  animation-timing-function: ease-out;
  animation-iteration-count: infinite; /* 设置为无限循环播放 */
}
@keyframes ripple-animate {
  to {
    transform: scale(2.5);
    opacity: 0;
  }
}
.tk-options {
  margin-bottom: 32rpx;
  width: 100%;
  text {
    font-size: 32rpx;
    color: #333;
    margin-right: 18rpx;
  }
  input {
    border: 1px solid #ccc;
    border-radius: 10rpx;
    padding: 0px 40rpx;
    flex: 1;
    height: 66rpx;
    line-height: 66rpx;
    font-size: 28rpx;
  }
  .placeholder-input {
    color: #eee;
  }
}
</style>
