<template>
  <u-count-down
    :time="time"
    format="HH:mm:ss"
    autoStart
    millisecond
    @change="onChange"
    @finish="finish()"
  >
    <view class="time">
      <view class="time__item">{{
        timeData.hours > 9 ? timeData.hours : '0' + timeData.hours
      }}</view>
      <view class="b">:</view>
      <view class="time__item">{{
        timeData.minutes > 9 ? timeData.minutes : '0' + timeData.minutes
      }}</view>
      <view class="b">:</view>
      <view class="time__item">{{
        timeData.seconds > 9 ? timeData.seconds : '0' + timeData.seconds
      }}</view>
    </view>
  </u-count-down>
</template>

<script>
export default {
  props: {
    time: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      timeData: {}
    }
  },
  methods: {
    onChange(e) {
      this.timeData = e
    },
    finish() {
      this.$emit('finish')
    }
  }
}
</script>

<style lang="scss">
.time {
  display: flex;
  align-items: center;
  .time__item {
    width: 34rpx;
    text-align: center;
    height: 34rpx;
    line-height: 34rpx;
    background: #ffffff;
    border-radius: 8rpx;
    font-weight: 400;
    font-size: 24rpx;
    color: #1f232e;
  }
  .b {
    color: #ffffff;
    width: 20rpx;
    text-align: center;
    height: 34rpx;
    line-height: 34rpx;
  }
}
</style>
