<template>
	<view :class="seckill?'item item-background':'item'" @click="goDetail(item)">
		<view class="header">
			<view class="name text-ellipsis-2">{{ item.name }}</view>
			<view class="price2" v-if="seckill">
				<view class="original_price">
					<view class="huaxian" style="text-decoration: line-through; margin-left: 12px">
						{{ info.original_price }}
					</view>
					<image class="miaoshajia"
						src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/92ac17422896646546913_miaoshajia.png">
					</image>
				</view>
				<view class="sale_price">
					<view class="fuhao">￥</view>
					<view class="jine">{{ info.sale_price }}</view>
				</view>
			</view>
			<view class="price" v-if="!isPay && !seckill && item.permission_status == '2'">
				<view class="huaxian" style="text-decoration: line-through; margin-right: 12px">
					{{ info.original_price }}
				</view>

				<view class="fuhao">￥</view>
				<view class="jine">{{ info.sale_price }}</view>
			</view>
		</view>

		<view class="tags">
			<view :class="seckill?'ee ee-seckill-q':'ee'">
				<view class="ee-seckill-q-row" v-if="seckill">
					共 <view style="color: red;">
						{{item.tiku_goods_details.question_num}}
					</view>
					题
				</view>
				<view v-else>
					{{ info.num_text }}
				</view>
			</view>
			<view v-if="item.type == 8" class="ee ee-tag-class">
				共{{ item.tiku_goods_details.question_num }}题
			</view>
			<view class="ee ee-tag-class" v-if="item.permission_status == '2'">{{
        info.month_text
      }}</view>
			<view class="ee" v-if="item.permission_status == '1'">
				{{ validity(item) }}
			</view>
		</view>

		<view class="path" v-if="info.system_id_name && !seckill">
			<text class="text"> {{ info.system_id_name }}</text>
		</view>
		<view class="bottom" v-if="isPay && item.type == 18">
			<view class="progress" v-if="item.type == 18">
				<view class="progress-content">
					<view class="pro" :style="{
              width: pro(item)
            }"></view>
				</view>
				<view class="text">
					<text>{{ item.teaching_package_finish_info.do_num }}</text>/{{ item.teaching_package_finish_info.question_num }}
				</view>
			</view>
			<view class="" v-else></view>
			<view class="but" v-if="item.type == 18"> 立即刷题 </view>
			<view class="but" v-else> 立即测试 </view>
		</view>
		<view class="bottom-time" v-if="seckill">
			<view class="pay-time">
				<view class="label">秒杀倒计时</view>
				<countDown :time="time" @finish="setTime()"></countDown>
			</view>
		</view>
	</view>
</template>

<script>
	import countDown from './count-down.vue'
	import {
		getOrderV2
	} from '../../api/index'
	import {
		app_id
	} from '../../config'
	import {
		goToLogin
	} from '../../utils/index'
	export default {
		components: {
			countDown,
		},
		props: {
			item: {
				type: Object,
				default: {}
			},
			seckill: {
				type: Boolean,
				default: false
			},
			isPay: {
				type: Boolean,
				default: false
			}
		},
		data() {
			return {
				time: 88888,
				info: {}
			}
		},
		created() {
			this.info = this.months_prices(this.item)
			this.setTime()
		},
		methods: {
			getOrder(item) {
				let payable_amount = 0
				let student_id = uni.getStorageSync('__xingyun_userinfo__').student_id
				let goods_id = item.id
				let employee_id = this.$store.state.jintiku.employee_id
				let data = {
					business_scene: 1,
					goods: [{
						goods_id: goods_id,
						// goods_months_price_id: '',
						// months: this.info.month,
						class_campus_id: '',
						class_city_id: '',
						goods_num: '1'
					}],
					deposit_amount: Number(payable_amount),
					payable_amount: Number(payable_amount),
					real_amount: Number(payable_amount),
					remark: '',
					student_adddatas_id: '',
					student_id: student_id,
					total_amount: Number(payable_amount),
					app_id: app_id,
					pay_method: '',
					order_type: 10,
					discount_amount: 0,
					coupons_ids: [],
					employee_id: employee_id || '508948528815416786',
					delivery_type: 1 // 默认总部邮寄
				}
				getOrderV2({
					...data
				}).then(res => {
					item.permission_status = '1'
					this.goDetail(item)
				})
			},
			validity(item) {
				if (/0001/g.test(item.validity_start_date)) {
					return '有效期：永久'
				}
				return `有效期：${item.validity_start_date}~${item.validity_end_date}`
			},
			setTime() {
				let key = '__tiku_goods_time__'
				let time = uni.getStorageSync(key)
				let d = new Date()
				let total_time = 3600 * 8 * 1000
				let setStartTime = () => {
					uni.setStorageSync(key, d.getTime())
					this.time = total_time
				}
				if (time) {
					this.time = total_time - (d.getTime() - time)
					if (this.time < 0) {
						setStartTime()
					}
				} else {
					setStartTime()
				}
			},
			pro(item) {
				let do_num = item.teaching_package_finish_info.do_num
				let question_num = item.teaching_package_finish_info.question_num
				if (do_num == 0 || question_num == 0) {
					return '0%'
				} else {
					return `${(Number(do_num) / Number(question_num)) * 100}%`
				}
			},
			goDetail(item) {
				if (item.permission_status == '1' || item?.months_prices?.length) {
					this.$xh.push(
						'jintiku',
						`pages/test/detail?professional_id=${item.professional_id}&id=${item.id}`
					)
				} else {
					if (!this.$store.state.jintiku.token) {
						goToLogin()
						return
					}
					this.getOrder(item)
				}
				return
				if (item.permission_status == '1') {
					if (item.type == 18) {
						this.$xh.push(
							'jintiku',
							`pages/chapterExercise/index?professional_id=${item.professional_id}&goods_id=${item.id}&total=${item.tiku_goods_details.question_num}`
						)
					}
					if (item.type == 10) {
						this.$xh.push(
							'jintiku',
							`pages/modelExaminationCompetition/examInfo?product_id=${item.id}&title=${item.name}&page=home`
						)
					}
					if (item.type == 8) {
						this.$xh.push('jintiku', `pages/test/exam?id=${item.id}`)
					}
				} else {
					if (item?.months_prices?.length) {
						this.$xh.push(
							'jintiku',
							`pages/test/detail?professional_id=${item.professional_id}&id=${item.id}`
						)
					} else {
						this.getOrder(item)
					}
				}
			},
			months_prices(item) {
				let info = item.tiku_goods_details
				let system_id_name = item?.teaching_system?.system_id_name || ''
				if (item.type == 10) {
					system_id_name = `开考时间:${info.exam_time}`
				}

				let num_text = `共${item.tiku_goods_details.question_num}题`
				if (item.type == 8) {
					num_text = `共${info.paper_num}份`
				}
				if (item.type == 10) {
					num_text = `共${info.exam_round_num}轮`
				}
				if (!item.months_prices || item.months_prices.length == 0) {
					return {
						sale_price: item.sale_price,
						original_price: '0',
						month_text: '永久',
						system_id_name,
						num_text
					}
				}
				let data = item.months_prices?.sort((a, b) => {
					if (a.month == 0) {
						return 1
					} else {
						return Number(a.month) - Number(b.month)
					}
				})[0]
				let sale_price = 0
				let original_price = '0'
				let month_text = ''

				if (data) {
					sale_price = data.sale_price
					original_price = data.original_price
					if (data.month == 0) {
						month_text = '永久'
					} else {
						month_text = data.month + '个月'
					}
				}
				return {
					sale_price,
					original_price,
					month_text,
					system_id_name,
					num_text
				}
			}
		}
	}
</script>

<style lang="scss" scoped>
	.bottom-time {
		width: 100%;
		height: 72rpx;
		right: -32rpx;
		bottom: 0;
		position: relative;
	}

	.pay-time {
		position: absolute;
		bottom: 0;
		right: 0;
		width: 362rpx;
		height: 94rpx;
		background-image: url(https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/5e91174186068572265789_daojishiback.png);
		display: flex;
		background-size: 362rpx 94rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		padding-top: 22rpx;

		.label {
			font-weight: 400;
			font-size: 28rpx;
			color: #082980;
			margin-right: 10rpx;
		}
	}

	.item-background {
		background: linear-gradient(90deg,
				#FBF1FF 0%,
				#D8F0FF 100%,
				rgba(255, 255, 255, 0) 100%);
	}

	.item {
		width: 100%;
		padding: 24rpx 32rpx 0rpx 32rpx;
		border-radius: 32rpx;
		box-shadow: 0 0 30rpx rgba(27, 38, 55, 0.06);
		margin-bottom: 24rpx;
		background-color: #F4F9FF;
		min-height: 213rpx;

		.header {
			display: flex;
			justify-content: space-between;
		}

		.price2 {
			display: flex;
			align-items: baseline;
			color: #ff5e00;
			flex-shrink: 0;

			.sale_price {
				display: flex;
				align-items: baseline;
			}

			.original_price {
				display: flex;
				align-items: center;
			}

			.miaoshajia {
				width: 70rpx;
				height: 30rpx;
				margin-right: 10rpx;
				margin-left: 8rpx;
			}

			.fuhao {
				font-weight: 500;
				font-size: 24rpx;
				color: #D845A6;
			}

			.jine {
				font-weight: 800;
				font-size: 40rpx;
			}

			.huaxian {
				font-weight: 800;
				font-size: 32rpx;
				color: #a3a3a3;
				margin-right: 10rpx;
			}
		}

		.huaxian {
			font-weight: 800;
			font-size: 28rpx;
			color: #a3a3a3;
		}

		.price {
			display: flex;
			align-items: baseline;
			color: #ff5e00;
			flex-shrink: 0;

			.fuhao {
				font-weight: 500;
				font-size: 24rpx;
				
			}

			.jine {
				font-weight: 800;
				font-size: 32rpx;
			}
		}

		.bottom {
			border-top: 1px solid #e8e9ea;
			height: 100rpx;
			display: flex;
			justify-content: space-between;
			align-items: center;

			.progress {
				display: flex;
				align-items: center;

				.progress-content {
					width: 200rpx;
					height: 14rpx;
					background: rgba($color: #2e68ff, $alpha: 0.1);
					border-radius: 16rpx;
					margin-right: 16rpx;
					overflow: hidden;

					.pro {
						background-color: #2e68ff;
						height: 100%;
					}
				}

				.text {
					font-size: 12px;

					color: rgba($color: #03203d, $alpha: 0.85);

					text {
						color: #2e68ff;
					}
				}
			}

			.but {
				width: 160rpx;
				text-align: center;
				height: 56rpx;
				line-height: 56rpx;
				background: #2e68ff;
				font-size: 12px;
				color: #ffffff;
				border-radius: 64rpx;
			}
		}

		.name {
			font-weight: 600;
			font-size: 15px;
			color: #212121;
		}

		.tags {
			display: flex;
			padding-bottom: 24rpx;
			
			.ee {
				margin-right: 12rpx;
				background: #f5f6fa;
				border-radius: 8rpx;
				padding: 4rpx 16rpx;
				font-size: 20rpx;
				color: rgba(44, 55, 61, 0.71);
				margin-top: 20rpx;
			}
			    .ee:nth-child(1) {
			      background: #ebf1ff;
			      color: #2e68ff;
			    }

		}

		.ee-seckill-q {
			background: #FFD27C !important;
		}

		.ee-seckill-q-row {
			display: flex;
			flex-direction: row;
			font-weight: 600;
			color: black;
		}

		.ee-tag-class {
			color: #4981D7 !important;
			border: 1rpx solid #4981D7 !important;
			background: transparent !important;
		}

		// 小程序专用两行文字省略样式
		.text-ellipsis-2 {
			overflow: hidden;
			text-overflow: ellipsis;
			display: -webkit-box;
			-webkit-line-clamp: 2;
			line-clamp: 2; // 标准属性
			-webkit-box-orient: vertical;
			word-break: break-all; // 小程序推荐使用 break-all
			line-height: 1.4;
			max-height: 2.8em; // 2行的最大高度 (1.4 * 2)

			/* #ifdef MP-WEIXIN */
			// 微信小程序特殊处理
			white-space: normal;
			/* #endif */

			/* #ifdef MP-ALIPAY */
			// 支付宝小程序特殊处理
			-webkit-line-clamp: 2;
			/* #endif */
		}

		.path {
			padding-bottom: 24rpx;

			.text {
				font-size: 12px;
				color: rgba(3, 32, 61, 0.65);
				margin-right: 24rpx;
			}
		}
	}
</style>