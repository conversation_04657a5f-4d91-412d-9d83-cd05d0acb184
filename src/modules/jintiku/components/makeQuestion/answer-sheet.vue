<template>
  <picker-shell :value="value" @input="input" title="答题卡" ref="picker">
    <view class="bar">
      <view class="yes">
        <view class="flag"></view>
        <view class="text">已做</view>
      </view>
      <view class="no">
        <view class="flag"></view>
        <view class="text">未做</view>
      </view>
      <view class="doubt">
        <view class="flag"></view>
        <view class="text">标疑</view>
      </view>
    </view>
    <view class="questions">
      <view
        class="question flex-center"
        :class="{
          done: item.user_option != '' || item.stem_list[0].selected.length,
          doubt: item.doubt
        }"
        v-for="(item, index) in questions"
        :key="index"
        @click="change(index)"
      >
        {{ index + 1 }}
      </view>
    </view>
  </picker-shell>
</template>
<script>
import pickerShell from '../commen/picker-shell.vue'
export default {
  // 答题卡查看
  name: 'answer-sheet',
  props: {
    value: {
      type: <PERSON>olean,
      default: false
    },
    questions: {
      default: () => []
    }
  },
  components: {
    pickerShell
  },
  data() {
    return {
      list: []
    }
  },
  created() {},
  methods: {
    input(val) {
      this.$emit('input', val)
    },
    change(index) {
      this.$refs.picker.cancelFn()
      this.$emit('change', index)
    }
  }
}
</script>
<style scoped lang="less">
.bar {
  display: flex;
  align-items: center;
  justify-content: center;
  .yes,
  .no,
  .doubt {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 40rpx;
    .flag {
      width: 16rpx;
      height: 16rpx;
      background-color: #567dfa;
      margin-right: 12rpx;
      border-radius: 50%;
    }
    .text {
      font-size: 28rpx;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #333333;
    }
  }
  .no {
    margin-left: 94rpx;
    .flag {
      background-color: #e4e4e4;
    }
  }
  .doubt {
    margin-left: 94rpx;
    .flag {
      background-color: #fb9e0c;
    }
  }
}
.questions {
  padding: 60rpx 40rpx 50rpx;
  padding-bottom: 0;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  .question {
    width: 80rpx;
    height: 80rpx;
    font-size: 32rpx;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 600;
    color: #000000;
    border-radius: 50%;
    background-color: #f6f6f6;
    margin-right: 64rpx;
    margin-bottom: 44rpx;
    &:nth-child(5n) {
      margin-right: 0;
    }
  }
  .question.done {
    background: #e7f3fe;
    color: #567dfa;
  }
  .question.doubt {
    background-color: #fb9e0c;
    color: #ffffff;
  }
}
</style>
