<template>
  <picker-shell :value="value" @input="input" :title="title" ref="picker">
    <view class="select_content">
      <view class="select">
        <view
          class="select_item button"
          v-for="(item, index) in selectList"
          :key="index"
          :class="{ select_item_click: selectId.includes(item.id) }"
          @click="onSelect(item.id)"
        >
          {{ item.label }}
        </view>
      </view>
      <view class="button_box">
        <view class="reset button" @click="reset">重置</view>
        <view class="confirm button" @click="submit">确定</view>
      </view>
    </view>
  </picker-shell>
</template>
<script>
import pickerShell from '../commen/picker-shell.vue'
import { getQuestionType } from '../../api/commen'
export default {
  name: 'select-question-type',
  components: {
    pickerShell
  },
  props: {
    value: {
      type: Boolean,
      default: false
    },
    selectList: {
      type: Array
    },
    name: {
      type: String
    },
    type: {
      type: String
    },
    title: {
      type: String
    },
    isMultiple: {
      type: <PERSON>olean,
      default: false
    }
  },
  data() {
    return {
      selectId: [],
      selectName: ''
    }
  },
  methods: {
    reset() {
      this.selectId = []
      // this.input(false)
    },
    input(val) {
      this.$emit('input', val)
    },
    onSelect(id) {
      if (this.isMultiple) {
        let tmphave = this.selectId.includes(id)
        if (tmphave) {
          let idx = this.selectId.indexOf(id)
          this.selectId.splice(idx, 1)
        } else {
          this.selectId.push(id)
        }
      } else {
        this.selectId[0] = id
      }
      console.log(this.selectId)
    },
    submit() {
      console.log(this.selectId, 'id有吗？？？')
      this.input(false)
      this.$emit('success', this.selectId)
    }
  },
  onShow() {
    console.log(this.selectList)
  },
  watch: {
    value(value) {
      if (value) {
        this.selectId = []
      }
    }
  }
}
</script>
<style lang="less" scoped>
.select_content {
  padding: 0 24rpx 0;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  .select {
    width: 100%;
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    margin-top: 40rpx;
    .select_item {
      width: 214rpx;
      height: 68rpx;
      background: #f6f7f8;
      border-radius: 32rpx;
      color: #161f30;
      font-size: 24rpx;
      text-align: center;
      line-height: 68rpx;
      margin-bottom: 32rpx;
    }
    .select_item_click {
      border: 2rpx solid #2e68ff;
      background: #ebf1ff;
      color: #2e68ff;
      border-radius: 32rpx;
    }
  }
  .button_box {
    display: flex;
    justify-content: center;
    align-items: center;
    background: #fff;
    margin: 62rpx 0 38rpx;
    .button {
      width: 352rpx;
      height: 88rpx;
      font-size: 28rpx;
      text-align: center;
      line-height: 88rpx;
    }
    .reset {
      color: #03203d;
      background: #eceef0;
      border-radius: 44rpx 0rpx 0rpx 44rpx;
    }
    .confirm {
      color: #fff;
      background: #2e68ff;
      border-radius: 0rpx 44rpx 44rpx 0rpx;
    }
  }
}
</style>
