<template>
  <picker-shell :value="value" @input="input" title="选择题型" ref="picker">
    <view class="select_content">
      <view class="select">
        <view
          class="select_item button"
          v-for="(item, index) in selectList"
          :key="index"
          :class="{ select_item_click: selectId == item.id }"
          @click="onSelect(item)"
        >
          {{ item.label }}
        </view>
      </view>
      <view class="button_box">
        <view class="reset button" @click="reset">重置</view>
        <view class="confirm button" @click="submit">确定</view>
      </view>
    </view>
  </picker-shell>
</template>
<script>
import pickerShell from '../commen/picker-shell.vue'
import { getQuestionType } from '../../api/commen'
export default {
  name: 'select-question-type',
  components: {
    pickerShell
  },
  props: {
    value: {
      type: Boolean,
      default: false
    },
    name: {
      type: String
    },
    type: {
      type: String
    }
  },
  data() {
    return {
      selectId: '',
      selectName: '',
      selectList: [
        {
          id: '1',
          label: 'A1'
        },
        {
          id: '2',
          label: 'A2'
        },
        {
          id: '3',
          label: 'A3'
        },
        {
          id: '4',
          label: 'A4'
        },
        {
          id: '5',
          label: 'B1'
        },
        {
          id: '6',
          label: 'B2'
        },
        {
          id: '7',
          label: 'X'
        }
      ]
    }
  },
  methods: {
    reset() {
      this.selectId = ''
      this.$emit('update:type', '')
      this.$emit('update:name', '')
      this.input(false)
    },
    input(val) {
      this.$emit('input', val)
    },
    onSelect(item) {
      this.selectId = item.id
      this.selectName = item.label
    },
    submit() {
      console.log(this.selectId, 'id有吗？？？')
      this.$emit('update:type', this.selectId)
      this.$emit('update:name', this.selectName)
      this.input(false)
      this.$emit('success')
    }
  },
  created() {
    getQuestionType({}).then(data => {
      this.selectList = data.data.map(item => {
        return {
          id: item.type,
          label: item.type_name
        }
      })
    })
  }
}
</script>
<style lang="less" scoped>
.select_content {
  padding: 0 24rpx 0;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  .select {
    width: 100%;
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    margin-top: 40rpx;
    .select_item {
      width: 214rpx;
      height: 68rpx;
      background: #f6f7f8;
      border-radius: 32rpx;
      color: #161f30;
      font-size: 24rpx;
      text-align: center;
      line-height: 68rpx;
      margin-bottom: 32rpx;
    }
    .select_item_click {
      border: 2rpx solid #2e68ff;
      background: #ebf1ff;
      color: #2e68ff;
      border-radius: 32rpx;
    }
  }
  .button_box {
    display: flex;
    justify-content: center;
    align-items: center;
    background: #fff;
    margin: 62rpx 0 38rpx;
    .button {
      width: 352rpx;
      height: 88rpx;
      font-size: 28rpx;
      text-align: center;
      line-height: 88rpx;
    }
    .reset {
      color: #03203d;
      background: #eceef0;
      border-radius: 44rpx 0rpx 0rpx 44rpx;
    }
    .confirm {
      color: #fff;
      background: #2e68ff;
      border-radius: 0rpx 44rpx 44rpx 0rpx;
    }
  }
}
</style>
