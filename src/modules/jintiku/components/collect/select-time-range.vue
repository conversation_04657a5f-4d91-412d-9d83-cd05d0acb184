<template>
  <picker-shell :value="value" @input="input" title="选择题型" ref="picker">
    <view class="select_content">
      <view>
        <view class="select" style="margin-top: 40rpx">
          <view
            class="select_item button"
            v-for="(item, index) in selectList"
            :key="index"
            :class="{ select_item_click: selectId.includes(item.id) }"
            @click="onSelect(item)"
          >
            {{ item.label }}
          </view>
        </view>
        <view class="time-range" v-if="selectId == '4'">
          <picker
            mode="date"
            :value="start_date"
            :start="startDate"
            :end="endDate"
            @change="bindDateChange1"
          >
            <view class="time">
              {{ start_date ? start_date : '请选择开始时间' }}</view
            >
          </picker>
          <view class="time"> ： </view>
          <picker
            mode="date"
            :value="end_date"
            :start="startDate"
            :end="endDate"
            @change="bindDateChange2"
          >
            <view class="time">
              {{ end_date ? end_date : '请选择结束时间' }}
            </view>
          </picker>
        </view>
      </view>
      <view class="button_box">
        <view class="confirm button" @click="submit">确定</view>
      </view>
    </view>
  </picker-shell>
</template>
<script>
import pickerShell from '../commen/picker-shell.vue'
export default {
  name: 'select-question-type',
  components: {
    pickerShell
  },
  props: {
    value: {
      type: Boolean,
      default: false
    },
    name: {
      type: String
    },
    type: {
      type: String
    },
    start_date: {
      type: String
    },
    end_date: {
      type: String
    },
    selectList: {
      type: Array,
      default: [
        {
          id: '1',
          label: '近三天'
        },
        {
          id: '2',
          label: '本周'
        },
        {
          id: '3',
          label: '当月'
        },
        {
          id: '4',
          label: '自定义时间'
        }
      ]
    }
  },
  computed: {
    startDate() {
      return this.getDate('start')
    },
    endDate() {
      return this.getDate('end')
    }
  },
  data() {
    return {
      date1: '',
      date2: '',
      selectId: '',
      selectName: ''
    }
  },
  watch: {
    value: {
      handler(val) {
        if (val) {
          this.selectId = this.type
          this.selectName = this.name
        }
      },
      immediate: true
    }
  },
  methods: {
    getDate(type) {
      const date = new Date()
      let year = date.getFullYear()
      let month = date.getMonth() + 1
      let day = date.getDate()
      if (type === 'start') {
        year = year - 60
      } else if (type === 'end') {
        year = year + 2
      }
      month = month > 9 ? month : '0' + month
      day = day > 9 ? day : '0' + day
      return `${year}-${month}-${day}`
    },
    bindDateChange1: function (e) {
      this.$emit('update:start_date', e.detail.value)
    },
    bindDateChange2: function (e) {
      this.$emit('update:end_date', e.detail.value)
    },
    input(val) {
      this.$emit('input', val)
    },
    onSelect(item) {
      this.selectId = item.id
      this.selectName = item.label
    },
    submit() {
      if (this.selectId == '4') {
        if (!this.start_date) {
          this.$xh.Toast('请选择开始时间！')
          return
        }
        if (!this.end_date) {
          this.$xh.Toast('请选择结束时间！')
          return
        }
      }
      this.$emit('update:type', this.selectId)
      this.$emit('update:name', this.selectName)
      this.input(false)
      this.$emit('success')
    }
  }
}
</script>
<style lang="less" scoped>
.select_content {
  padding: 0 24rpx 0;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  .select {
    width: 100%;
    display: flex;
    justify-content: flex-start;
    flex-wrap: wrap;
    // margin-top: 40rpx;
    .select_item {
      width: 214rpx;
      height: 68rpx;
      background: #f6f7f8;
      border-radius: 32rpx;
      color: #161f30;
      font-size: 24rpx;
      text-align: center;
      line-height: 68rpx;
      // margin-bottom: 32rpx;
      margin: 0rpx 11rpx 32rpx;
    }
    .select_item_click {
      border: 2rpx solid #2e68ff;
      background: #ebf1ff;
      color: #2e68ff;
      border-radius: 32rpx;
    }
  }
  .button_box {
    display: flex;
    justify-content: center;
    align-items: center;
    background: #fff;
    margin: 62rpx 0 38rpx;
    .button {
      width: 352rpx;
      height: 88rpx;
      font-size: 28rpx;
      text-align: center;
      line-height: 88rpx;
    }
    .confirm {
      color: #fff;
      background: #2e68ff;
      border-radius: 44rpx;
    }
  }
  .time-range {
    display: flex;
    align-items: center;
    justify-content: space-around;
    // .time {
    //   width: 280rpx;
    //   height: 68rpx;
    //   font-size: 28rpx;
    //   text-align: center;
    //   line-height: 68rpx;
    //   border-radius: 44rpx;
    //   border: 1px solid #ccc;
    // }
  }
}
</style>
