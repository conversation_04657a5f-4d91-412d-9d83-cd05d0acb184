<template>
  <view class="subscript" v-if="accuracy >= 80">
    <image
      class="img"
      src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/18e8174185885967052130_subscript.png"
    ></image>
    <view class="num">
      <view class="text">{{ accuracy }}</view>
      <view class="text">%</view>
    </view>
  </view>
</template>

<script>
export default {
  props: {
    info: {
      type: Object,
      default: {
        correct_question_num: 0, //做对
        error_question_num: 0, //做错
        not_answered_question_num: 0 //未答
      }
    }
  },
  data() {
    return {
      accuracy: 0
    }
  },
  created() {
    let total =
      Number(this.info.correct_question_num) +
      Number(this.info.error_question_num) +
      Number(this.info.not_answered_question_num)
    this.accuracy = Math.floor(
      (Number(this.info.correct_question_num) / total) * 100
    )
  }
}
</script>

<style lang="scss" scoped>
.subscript {
  display: flex;
  align-items: center;
  padding-left: 24rpx;
  .img {
    width: 122rpx;
    height: 32rpx;
  }
  .num {
    display: flex;
    align-items: baseline;
    margin-left: -6rpx;
    .text:nth-child(1) {
      font-size: 44rpx;
      color: #ff7e0a;
    }
    .text:nth-child(2) {
      font-size: 24rpx;
      color: #ff7e0a;
    }
  }
}
</style>
