<template>
  <view class="com-container">
    <!-- 递归自己 -->
    <view v-if="data.section_type && data.section_type == 2">
      <child-com
        :res="data"
        :index="index"
        :isExpand="!isExpand"
        :disabled="disabled"
        :type="type"
        :key="data.id"
        :isfree="isfree"
      />
    </view>
    <view v-else>
      <!-- 头 -->
      <view>
        <tree-head
          @expand="expand"
          :item="data"
          :isExpand="isExpand"
          :disabled="disabled"
          :level="level"
          :type="type"
          :key="data.id"
          :isfree="isfree"
        />
      </view>
      <view
        v-if="data.child && data.child.length && isExpand"
        class="tree-item-container-parent"
        :style="{ height: isExpand ? 'auto' : '0' }"
      >
        <view
          v-for="(item, i) in data.child"
          :key="item.id"
          class="tree-item-container"
        >
          <tree-item
            :data="item"
            :index="i"
            :type="type"
            :disabled="disabled"
            :level="level + 1"
            :key="data.id"
            :isfree="isfree"
          />
        </view>
      </view>
    </view>
  </view>
</template>
<script>
import treeHead from './tree-head.vue'
import chilidCom from './child-com.vue'
export default {
  name: 'tree-item',
  components: {
    treeHead,
    chilidCom
  },
  props: {
    data: {
      type: Object
    },
    index: {
      type: Number,
      default: 0
    },
    disabled: {
      type: Boolean,
      default: false
    },
    type: {
      type: String,
      default: '1'
    },
    level: {
      type: Number,
      default: 1
    },
    isfree: {
      type: Boolean,
      default: false
    }
  },
  data() {
    let isExpand = this.data.is_checked == '1' ? true : false
    if (this.level == '1') {
      isExpand = true
    }
    return {
      isExpand
    }
  },
  methods: {
    expand() {
      this.isExpand = !this.isExpand
      if (!this.data.child || !this.data.child.length) {
        this.$xh.Toast('该章节下暂无任何知识点！')
      }
    }
  }
}
</script>
<style scoped lang="less">
.tree-item-container-parent {
  height: 0;
  overflow: hidden;
}
</style>
