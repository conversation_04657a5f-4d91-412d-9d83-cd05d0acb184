<template>
  <view
    class="contents"
    :style="{
      paddingLeft: 32 + (res.leval - 1) * 40 + 'rpx',
      marginTop: index == 0 ? '24rpx' : '0'
    }"
    :class="{ isExpand: isExpand || res.leval - 1 === 0 }"
  >
    <view class="content" @click="goDetail(res)">
      <view class="content-head">
        <view class="content-left">
          <view class="circle"></view>
          <text class="unit">{{ res.sectionname }}</text>
        </view>
        <image
          src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/16950046792662774169500467926615379_edit.png"
          mode="widthFix"
          class="edit"
          v-if="res.is_checked != '1' && !disabled"
        />
        <!-- v-if="!res.isPreCurrent" -->
        <text class="pre-picter" v-if="res.is_checked == '1'"> 上次练习 </text>
      </view>
      <view class="content-static">
        <view class="static"> 做对 {{ res.correct_question_num }} </view>
        <view class="static"> 做错 {{ res.error_question_num }} </view>
        <view class="static"> 未答 {{ res.not_answered_question_num }} </view>
      </view>
    </view>
  </view>
</template>
<script>
export default {
  props: {
    res: {
      type: Object
    },
    index: {
      type: Number,
      default: 0
    },
    isExpand: {
      type: Boolean,
      default: false
    },
    disabled: {
      type: Boolean,
      default: false
    },
    type: {
      type: String,
      default: '1'
    },
    isfree: {
      type: Boolean,
      default: false
    }
  },
  inject: ['updatePrePriceData'],
  data() {
    return {}
  },
  methods: {
    goDetail(item) {
      if (this.disabled) {
        return
      }

      if (item.question_number == '0') {
        this.$xh.Toast('暂无题目！')
        return
      }
      if (item.not_answered_question_num == '0') {
        // 跳转看题
        this.$xh.push(
          'jintiku',
          `pages/makeQuestion/lookAnalysisQuestion?disabled=${this.disabled}&knowledge_id=${item.id}&type=${this.type}&chapter_id=${item.sectionprent}&teaching_system_package_id=${item.teaching_system_package_id}`
        )
        return
      }
      // 先把其他消除
      // this.setDataInfo(this.lists)
      // item.isPreCurrent = true
      // item.is_checked = '1'
      this.updatePrePriceData(item.id)
      this.$xh.push(
        'jintiku',
        `pages/makeQuestion/makeQuestion?disabled=${
          this.disabled
        }&knowledge_id=${item.id}&type=${this.type}&chapter_id=${
          item.sectionprent
        }&teaching_system_package_id=${
          item.teaching_system_package_id
        }&isfree=${this.isfree ? 1 : 0}&professional_id=${item.professional_id}`
      )
    }
  }
}
</script>
<style scoped lang="less">
.contents {
  // padding: 48rpx 0;
  // height: 0;
  overflow: hidden;
  padding: 0;
  transition: all 0.25s;
  padding-right: 32rpx;
  height: 0rpx;
  // margin-top: 24rpx;
  .content {
    margin: 0 16rpx;
    .content-head {
      display: flex;
      align-items: center;
      justify-content: space-between;
      .content-left {
        display: flex;
        align-items: center;
        .circle {
          width: 20rpx;
          height: 20rpx;
          margin-right: 30rpx;
          background: #387dfc;
          border-radius: 50%;
          margin-left: -8rpx;
        }
        .unit {
          font-size: 26rpx;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #333333;
          margin-right: 4rpx;
        }
        .desc-title {
          font-size: 26rpx;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #333333;
        }
      }
      .edit {
        width: 24rpx;
        height: 24rpx;
      }
      .pre-picter {
        color: #387dfc;
        font-size: 24rpx;
      }
    }
    .content-static {
      height: 94rpx;
      border-left: 2rpx dashed #387dfc;
      padding-top: 20rpx;
      padding-left: 40rpx;
      padding-right: 80rpx;
      display: flex;
      justify-content: space-between;

      .static {
        // margin-right: 80rpx;
        font-size: 24rpx;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #999999;
      }
    }
  }
}
.isExpand {
  height: 120rpx;
}
</style>
