<template>
  <view class="treeChapter">
    <view v-for="(item, i) in list" :key="item.id">
      <tree-item
        :data="item"
        :disabled="disabled"
        :type="type"
        :level="level"
        :key="item.id"
        :isfree="isfree"
      ></tree-item>
    </view>
  </view>
</template>
<script>
import treeItem from './tree-item.vue'
export default {
  components: {
    treeItem
  },
  props: {
    list: {
      type: Array,
      default: () => []
    },
    disabled: {
      type: Boolean,
      default: false
    },
    type: {
      type: String,
      default: '1'
    },
    isfree: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      level: 1
    }
  }
}
</script>
<style scoped lang="less"></style>
