# 学习日历组件使用说明

## 问题解决

如果您遇到了 "relative module was not found" 的错误，这是因为导入路径不正确。

## 正确的使用方法

### 1. 组件位置
组件已放置在：`src/modules/jintiku/components/study-calendar.vue`

### 2. 在首页中使用

在 `src/modules/jintiku/pages/index/index.vue` 中：

```vue
<script>
// 正确的导入路径
import StudyCalendar from '../../components/study-calendar.vue'

export default {
  components: {
    StudyCalendar
  },
  data() {
    return {
      // 学习统计数据
      studyStats: {
        persistDays: 31,
        totalQuestions: 700,
        accuracyRate: 12,
        isCheckedIn: true
      }
    }
  },
  methods: {
    // 处理打卡事件
    async handleCheckIn() {
      try {
        // 调用打卡API
        const result = await this.checkInAPI()
        if (result.success) {
          this.studyStats.isCheckedIn = true
          this.studyStats.persistDays += 1
          
          uni.showToast({
            title: '打卡成功！',
            icon: 'success'
          })
        }
      } catch (error) {
        uni.showToast({
          title: '打卡失败，请重试',
          icon: 'error'
        })
      }
    },
    
    // 模拟打卡API
    async checkInAPI() {
      return new Promise((resolve) => {
        setTimeout(() => {
          resolve({ success: true })
        }, 1000)
      })
    }
  }
}
</script>
```

### 3. 在模板中使用

```vue
<template>
  <view class="question-index">
    <!-- 现有内容 -->
    
    <!-- 在轮播图后添加学习日历 -->
    <view class="banner-view">
      <u-swiper style="height: 200rpx;" :radius="'32rpx'" :list="bannerList" @click="bannerClick"></u-swiper>
    </view>
    
    <!-- 学习日历组件 -->
    <study-calendar 
      :persist-days="studyStats.persistDays"
      :total-questions="studyStats.totalQuestions"
      :accuracy-rate="studyStats.accuracyRate"
      :is-checked-in="studyStats.isCheckedIn"
      @check-in="handleCheckIn"
    />
    
    <!-- 其他现有内容 -->
  </view>
</template>
```

## 完整示例

我已经创建了一个完整的示例文件：
`src/modules/jintiku/pages/index/index-with-calendar.vue`

您可以参考这个文件来了解如何正确集成组件。

## 路径说明

从 `src/modules/jintiku/pages/index/index.vue` 到组件的相对路径：
- `../../components/study-calendar.vue` ✅ 正确
- `../../../../components/study-calendar.vue` ❌ 如果组件在 src/components 下

## 组件属性

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| persistDays | Number/String | 31 | 坚持天数 |
| totalQuestions | Number/String | 700 | 做题总数 |
| accuracyRate | Number/String | 12 | 正确率 |
| isCheckedIn | Boolean | true | 是否已打卡 |

## 事件

| 事件名 | 说明 | 回调参数 |
|--------|------|----------|
| check-in | 用户点击打卡按钮时触发 | - |

## 注意事项

1. 确保项目支持 SCSS
2. 组件使用了 uni-app 的 API
3. 建议在真机上测试显示效果
4. 可以根据需要调整组件样式和数据
