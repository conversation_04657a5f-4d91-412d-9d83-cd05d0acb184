<template>
  <div class="teaching-type-tab">
    <div
      class="teaching-type-item"
      v-for="item in teachingTypeOption"
      @click="clickFun(item)"
    >
      <div
        class="teaching-type-item-name"
        :class="{ 'teaching-type-item-name-active': item.id === modelValue }"
      >
        {{ item.name }}
      </div>
      <div class="teaching-type-item-line" v-if="item.id === modelValue"></div>
    </div>
  </div>
</template>

<script>
export default {
  name: "teaching-type-tab",
  components: {},
  props: {
    modelValue: {
      type: String,
    },
  },
  emits: ["update:modelValue", "change"],
  data() {
    return {
      teachingTypeOption: [
        { name: "录播课", id: "3" },
        { name: "直播课", id: "1" },
        { name: "面授课", id: "2" },
      ],
    };
  },
  mounted() {},
  onMounted() {},
  methods: {
    clickFun(item) {
      this.$emit("update:modelValue", item.id);
      this.$emit("change", item.id);
    },
  },
};
</script>

<style scoped lang="scss">
.teaching-type-tab {
  display: flex;
  justify-content: space-around;
  font-weight: 400;
  font-size: 14px;
  color: rgba(51, 51, 51, 0.85);
  padding-top: 16px;
  .teaching-type-item {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    .teaching-type-item-name {
      margin-bottom: 3px;
    }
    .teaching-type-item-line {
      width: 22px;
      height: 3px;
      border-radius: 1.5px;
      background: #018CFF;
    }
    .teaching-type-item-name-active {
      font-weight: 600;
      font-size: 17px;
      color: #333333;
    }
  }
}
</style>
