<template>
  <div class="my-course">
    <ModuleStudyMyCourseTeachingTypeTab
      v-model="teaching_type"
      @change="teachingChange"
    />
    <ModuleStudyCourse ref="studyPlanList" :list="list" />
    <ModuleStudyNotLearn v-if="!list.length && !loading" />
    <div v-if="loading" style="text-align: center; margin-top: 50px">
      <van-loading />
    </div>
  </div>
</template>
<script lang="ts" setup>
const { study } = useApi();
const list = ref([]) as any;
const teaching_type = ref("3");
const studyPlanList = ref();
const loading = ref(true);
const restrict = ref(false);
const form = reactive({
  page: 1,
  size: 10,
});
const total = ref(0);

let getData = () => {
  loading.value = true;
  study
    .myCourse({
      ...form,
      teaching_type: teaching_type.value,
    })
    .then(({ data }: any) => {
      restrict.value = false;
      if (data.list && data.list.length) {
        let copyList = data.list.map((item: any) => {
          let teacher = [];
          // 教师只展示前两个
          if (item.class.teacher && item.class.teacher?.length) {
            if (item.class.teacher.length > 2) {
              teacher = item.class.teacher.slice(0, 2);
            } else {
              teacher = item.class.teacher;
            }
          }
          return {
            ...item,
            evaluation_type: item.evaluation_type.filter(
              (btn: any) => btn.paper_version_id && btn.paper_version_id != "0"
            ),
            class: {
              ...item.class,
              teacher,
            },
          };
        });
        list.value = [...list.value, ...copyList];
        total.value = data.total;
      }
    })
    .catch((err) => {
      console.log(err);
      showFailToast(err.msg[0]);
      if (err.code == 100002) {
        showFailToast(err.msg[0]);
        studyLoginLose();
      }
    })
    .finally(() => {
      restrict.value = false;
      loading.value = false;
    });
};

let scrollGet = () => {
  // 获取视口的高度
  let windowHeight =
    window.innerHeight ||
    document.documentElement.clientHeight ||
    document.body.clientHeight;

  // 滚动到多少了
  let scrollTop = document.documentElement.scrollTop;
  // 一共能滚动多少
  let scrollHeight = studyPlanList.value?.$el.scrollHeight + 39 - windowHeight;
  // 提前量
  let fast = 100;

  if (scrollTop >= scrollHeight - fast) {
    if (restrict.value) {
      return;
    }
    if (form.page >= Math.ceil(total.value / 10)) {
      return;
    }
    restrict.value = true;
    form.page = form.page + 1;
    getData();
  }
};

const teachingChange = (val: any) => {
  teaching_type.value = val;
  form.page = 1;
  list.value = [];
  getData();
};

onMounted(() => {
  getData();
  setTimeout(() => {
    window.addEventListener("scroll", scrollGet);
  }, 500);
});
</script>
<style lang="scss" scoped>
.my-course {
  background-color: #f2f5f7;
  min-height: 100vh;
}
</style>
