<template>
  <view class="select-date-box" :class="{ fixed: fixed }">
    <view class="select-date" :style="{ paddingTop: paddingTop }">
      <view class="current-weekList">
        <view
          class="current-weekList-item"
          v-for="(item, index) in currentWeekList"
          :key="index"
          @click="selectDate(item)"
          :class="{ active: selected === item.date }"
        >
          <text class="week">{{ isToday(item) }}</text>
          <text class="date">{{ formatDate(item.date, selected) }}</text>
          <view
            class="dot"
            v-if="dotArr.includes(item.date)"
            style="
              width: 5px;
              height: 5px;
              border-radius: 2.5px;
              background-color: #018CFF;
              margin-top: 6px;
            "
          ></view>
        </view>
      </view>
      <view class="moreDates" @click="changeCalendarShow">
        <view class="text">
          <view>更多</view>
          <view>日期</view>
        </view>
      </view>
    </view>
    <view
      class="learn-uni-calendar"
      :style="{ height: calendarShow ? '58vh' : '0px' }"
    >
      <uni-calendar
        :insert="true"
        :lunar="false"
        :showMonth="false"
        :start-date="minDate"
        :end-date="maxDate"
        :range="false"
        :date="selected"
        ref="calendar"
        @confirm="confirm"
        @change="changeDate"
        :dotArr="dotArr"
      />
    </view>
    <view
      class="learn-uni-calendar-mask"
      @click="calendarShow = false"
      v-if="calendarShow"
    ></view>
  </view>
</template>
<script>
import UniCalendar from "./uni-calendar/uni-calendar.vue";

// 获取当前时间的方法， 返回：2021-01-01
export function getNowFormatDate() {
  const date = new Date(+new Date());
  const seperator1 = "-";
  const year = date.getFullYear();
  let month = date.getMonth() + 1;
  let strDate = date.getDate();
  if (month >= 1 && month <= 9) {
    month = "0" + month;
  }
  if (strDate >= 0 && strDate <= 9) {
    strDate = "0" + strDate;
  }
  const currentdate = year + seperator1 + month + seperator1 + strDate;
  return currentdate;
}

// 返回一年之前的今天，格式为 "YYYY-MM-DD"
function getMinDate() {
  const date = new Date();
  const year = date.getFullYear() - 1; // 获取一年之前的年份
  let month = date.getMonth() + 1; // getMonth 返回的月份从0开始，所以需要加1
  let day = date.getDate();
  // 如果月份和日期小于10，前面补0
  if (month < 10) {
    month = "0" + month;
  }
  if (day < 10) {
    day = "0" + day;
  }
  return `${year}-${month}-${day}`; // 返回格式为 "YYYY-MM-DD" 的日期字符串
}

// 返回一年之后的今天，格式为 "YYYY-MM-DD"
function getMaxDate() {
  const date = new Date();
  const year = date.getFullYear() + 1; // 获取一年之后的年份
  let month = date.getMonth() + 1; // getMonth 返回的月份从0开始，所以需要加1
  let day = date.getDate();
  // 如果月份和日期小于10，前面补0
  if (month < 10) {
    month = "0" + month;
  }
  if (day < 10) {
    day = "0" + day;
  }
  return `${year}-${month}-${day}`; // 返回格式为 "YYYY-MM-DD" 的日期字符串
}

export default {
  name: "ModuleStudySelectDate",
  components: { UniCalendar },
  props: {
    selected: {
      type: String,
      default: getNowFormatDate(),
    },
    paddingTop: {
      type: String,
      default: "0rpx",
    },
    fixed: {
      type: Boolean,
      default: false,
    },
    dotArr: {
      type: Array,
      default() {
        return [];
      },
    },
  },
  options: {
    styleIsolation: "shared",
  },
  data() {
    return {
      // [{date: '2021-10-10', week: '周日'}]
      currentWeekList: [],
      // // 当天日期
      // selected: new Date().toISOString().split('T')[0],
      // 一年之前的今天
      minDate: getMinDate(),
      // 一年之后的今天
      maxDate: getMaxDate(),
      calendarShow: false,
    };
  },
  watch: {
    selected: {
      handler(val) {
        this.getWeekList(new Date(val.replace(/-/g, "/")));
        this.calendarShow = false;
      },
      immediate: true,
    },
  },
  methods: {
    // 返回当前周
    getWeekList(date) {
      const day = date.getDay();
      const currentWeekList = [];
      for (let i = 0; i < 7; i++) {
        const newDate = new Date(date);
        newDate.setDate(date.getDate() - day + i);
        const formattedMonth = String(newDate.getMonth() + 1).padStart(2, "0");
        const formattedDate = String(newDate.getDate()).padStart(2, "0");
        currentWeekList.push({
          date: `${newDate.getFullYear()}-${formattedMonth}-${formattedDate}`,
          week: `周${
            ["日", "一", "二", "三", "四", "五", "六"][newDate.getDay()]
          }`,
        });
      }
      this.currentWeekList = currentWeekList;
    },
    selectDate(item) {
      this.$emit("change", item.date);
    },
    isToday(week) {
      if (week.date === getNowFormatDate()) {
        return "今天";
      }
      return week.week;
    },
    formatDate(date, selected) {
      let dataArr = date.split("-");
      if (date === selected) {
        return dataArr[1] + "-" + dataArr[2];
      }
      return dataArr[dataArr.length - 1];
    },
    changeCalendarShow() {
      // this.$refs.calendar.open()
      this.$emit("more");
      console.log(this.calendarShow);
      this.calendarShow = !this.calendarShow;
    },
    confirm(e) {
      this.$emit("change", e.fulldate);
      this.calendarShow = false;
    },
    changeDate(item) {
      this.$emit("change", item.fulldate);
    },
  },
};
</script>
<style scoped lang="less">
.select-date-box {
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.08);
  .select-date {
    background: #fff;
    display: flex;
    padding-bottom: 7px;
    padding-top: 5px;
    justify-content: space-between;
    font-size: 10px;

    .current-weekList {
      height: 42px;
      display: flex;
      flex: 1;
      padding-left: 7px;

      .current-weekList-item {
        position: relative;
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;

        .week {
          font-weight: 400;
          color: #5b6e81;
        }

        .date {
          font-size: 12px;
          color: #262629;
          font-weight: 500;
          margin-top: 4px;
        }
        .dot {
          position: absolute;
          bottom: 0;
          left: calc(50% - 2.5px);
        }
      }

      .active {
        background: #018CFF;
        border-radius: 6px;

        .week {
          color: #fff;
        }

        .date {
          color: #fff;
        }
      }
    }

    .moreDates {
      background: #fff;
      display: flex;
      justify-content: center;
      align-items: center;
      width: 51px;
      font-weight: 400;
      font-size: 10px;
      color: #262629;
      line-height: 16px;
      background: linear-gradient(180deg, #fafcff 0%, #ffffff 100%);
      box-shadow: -2px 0px 2px 0px rgba(232, 234, 239, 0.22);

      .text {
        width: 25px;
        padding-left: 2px;
      }

      img {
        width: 7px;
        height: 7px;
      }
    }
  }

  .learn-uni-calendar {
    display: block;
    transition: 0.3s;
    height: 0px;
    overflow: hidden;
  }
}
.learn-uni-calendar-mask {
  z-index: -1;
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: #000;
  opacity: 0.5;
}

.fixed {
  background: #fff;
  position: fixed;
  width: 100vw;
  left: 0;
  z-index: 10;

  /* #ifdef MP */
  top: calc(176rpx);
  /* #endif */

  /* #ifdef H5 */
  top: 96rpx;
  /* #endif */
}
</style>
