<template>
  <div class="my-detail">
<!--    <iframe class="live-iframe" :src="`https://www.baidu.com`"  style="border:0px"></iframe>-->
    <iframe class="live-iframe" allowfullscreen="true" webkitallowfullscreen="true" mozallowfullscreen="true" :src="url"></iframe>
  </div>
</template>
<script setup lang="ts">
const route = useRoute()
let url =ref('') as any
url.value = route.query.url


</script>
<style lang="scss" scoped>
.my-detail{
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: #fff;
  .live-iframe{
    width: 100%;
    height: 100%;
  }
}

</style>
