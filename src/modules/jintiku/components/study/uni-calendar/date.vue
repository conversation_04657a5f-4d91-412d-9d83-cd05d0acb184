<template>
  <div class="date">
    <div @click="showVanPicker">
      <slot name="default" />
    </div>
    <van-popup v-model:show="show" position="bottom">
      <van-picker
          ref="vanPicker"
          show-toolbar
          :columns="columns"
          @cancel="show = false"
          @confirm="onConfirm"
      />
    </van-popup>
  </div>
</template>
<script lang="ts">
import {defineComponent, reactive, toRefs, ref, onMounted} from 'vue'

export default defineComponent({
  props: {
    value: {
      type: String,
      default: ''
    }
  },
  setup(props, {emit}) {
    const vanPicker=ref(null)

    const generateYears = () => {
      const currentYear = new Date().getFullYear();
      const years = [];
      for (let year = currentYear - 1; year <= currentYear + 1; year++) {
        years.push({text: year.toString() + '年', value: year.toString()});
      }
      return years;
    };
    const generateMonths = () => {
      const months = [];
      for (let month = 1; month <= 12; month++) {
        const monthText = month.toString() + '月'; // 去掉前导零
        const monthValue = month.toString().padStart(2, '0'); // 保持前导零
        months.push({text: monthText, value: monthValue});
      }
      return months;
    };

    let state = reactive({
      show: false,
      columns: [
        generateYears(),
        generateMonths()
      ]
    })

    const showVanPicker = () => {
      state.show = true
      // console.log(vanPicker.value)
      // vanPicker.value.setValues([props.value.split('-')[0], props.value.split('-')[1]])
    }


    const onConfirm = (val: any) => {
      emit('change', {detail:{value: val.selectedValues.join('-')}})
      state.show = false
    }

    return {
      ...toRefs(state),
      vanPicker,
      onConfirm,
      showVanPicker
    }
  }
})
</script>
<style lang="scss" scoped>

</style>
