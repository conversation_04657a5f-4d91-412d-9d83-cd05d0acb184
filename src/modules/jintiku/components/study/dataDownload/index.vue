<template>
  <div class="data-download">
    <van-search
        v-model="value"
        left-icon=""
        placeholder="搜索文档名称"
        right-icon="search"
        @input="search"
    />
    <div class="file-list">
      <div v-for="item in copyList" class="file-item">
        <div class="file-icon">
          <img :src="item.src" alt="">
        </div>
        <div class="file-message">
          <div class="file-name">{{ item.name }}</div>
          <div class="file-size-page">
            <div class="file-info">
              <div class="file-size">{{ item.num }}下载</div>
              <div class="file-page">{{ item.Pages }}页</div>
            </div>
            <div class="file-operate">
              <!--              <div class="file-operate-item file-download" @click="download(item)">下载</div>-->
              <div class="file-operate-item file-preview" @click="preview(item)">预览</div>
              <!--                            <div class="file-operate-item file-not" @click="preview(item)">-->
              <!--                              <img src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/4c2517335433227937920_jinzhi.png" alt="">-->
              <!--                              <span>未解锁</span>-->
              <!--                            </div>-->
            </div>
          </div>
        </div>
      </div>
    </div>
    <div v-if="loading" style="text-align: center;margin-top: 50px;">
      <van-loading/>
    </div>
  </div>
</template>
<script lang="ts" setup>
let router = useRouter()

const {study} = useApi()

const value = ref('')
const loading = ref(true)
const list = ref([]) as any
const copyList = ref([]) as any

study
    .dataDownload({
      lessons_id: ''
    })
    .then(({data}) => {
      list.value = data || []
      copyList.value = list.value
    })
    .finally(() => {
      loading.value = false
    })


const search = (item: any) => {
  let key = item.data
  copyList.value = list.value.filter((item: any) => {
    if (!key) {
      return true
    }
    return item.name.includes(key)
  })
}

const download = (item: any) => {
  window.open(item.link)
}
</script>
<style lang="scss" scoped>
.data-download {
  .van-search {
    padding: 12px 16px 0px;

    ::v-deep .van-search__content {
      border-radius: 23px;
      overflow: hidden;

      .van-field__control {
        font-weight: 400;
        font-size: 12px;
        color: #93969F;
        text-indent: 0.5em;
      }

      .van-field__right-icon {
        margin-right: 0px;
      }
    }
  }

  .file-list {
    padding: 23px 16px;

    .file-item {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      margin-bottom: 20px;
      padding-bottom: 10px;
      border-bottom: 1px solid #E8E9EA;

      .file-icon {
        img {
          width: 35px;
          height: 40px;
          margin-right: 17px;
        }
      }

      .file-message {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        align-content: space-between;

        .file-name {
          font-weight: 500;
          font-size: 15px;
          color: #262629;
        }

        .file-size-page {
          display: flex;
          align-items: center;
          justify-content: space-between;

          .file-info {
            display: flex;
            align-items: center;
            justify-content: flex-start;
            font-weight: 400;
            font-size: 12px;
            color: rgba(38, 38, 41, 0.6);

            .file-size {
              margin-right: 10px;
            }

            .file-page {

            }
          }

          .file-operate {
            .file-operate-item {
              width: 72px;
              height: 26px;
              border-radius: 14px;
              font-weight: 400;
              font-size: 12px;
              text-align: center;
              line-height: 24px;
            }

            .file-download {
              color: #018CFF;
              border: 1px solid rgba(1, 163, 99, 0.47);
            }

            .file-preview {
              background: #EAFFF7;
              border: 1px solid #0EA96D;
              color: #0EA96D;
            }

            .file-not {
              display: flex;
              justify-content: center;
              align-items: center;
              background: #EFEFEF;
              color: #969696;

              img {
                width: 14px;
                height: 14px;
                margin-right: 2px;
              }
            }
          }

        }


      }
    }
  }
}
</style>
