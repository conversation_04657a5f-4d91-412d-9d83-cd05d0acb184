<template>
  <view class="empty">
    <image
        class="empty-img"
        src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/4045173295663081752515_8b3592c2dcddcac66af8ddd46abbbf1b74efa19fac63-AlBs3V_fw1200%402x.png"
    />
    <view class="empty-message">未找到符合的学习内容</view>
    <!-- <view class="empty-button" @click="goSelectCourse">我要去选课</view> -->
  </view>
</template>

<script>
export default {
  name: 'ModuleStudyNotLearn',
  data() {
    return {}
  },
  methods: {
    goSelectCourse() {
      // const from = this.$store.state.jintiku.from;
      // if (from == 'm') {
        this.$xh.push('jintiku', 'pages/shop/index');
        return
      // }
    }
  }
}
</script>

<style scoped lang="less">
.empty {
  display: flex;
  justify-content: center;
  flex-direction: column;
  align-items: center;
  margin-top: 180px;

  .empty-img {
    width: 156px;
    height: 102px;
    margin-bottom: 4px;
  }

  .empty-message {
    font-weight: 400;
    font-size: 12px;
    color: rgba(3, 32, 61, 0.45);
    margin-bottom: 32px;
  }

  .empty-button {
    width: 122px;
    height: 34px;
    background: #018CFF;
    border-radius: 22px;
    font-weight: 400;
    font-size: 13px;
    color: #ffffff;
    text-align: center;
    line-height: 34px;
  }
}
</style>
