<template>
  <div class="course-list">
    <div class="course-list-content">
      <div v-if="learnCourseList.length" class="learn-course-list">
        <div v-for="item in learnCourseList" class="learn-course-list-item">
          <div class="learn-course-list-title">
            <div class="title">
              <div class="title-name">
                <span class="teaching-type">{{ item.teaching_type_name }}</span>
                <span>{{ item.name }}</span>
                <span class="label"
                  >（{{ item.lesson_attendance_num }}/{{
                    item.lesson_num
                  }}）</span
                >
              </div>
              <div class="close-open">
                <img
                  v-if="!item.isClose"
                  class="close"
                  src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/31c3173314287462623784_%E4%B8%8A.png"
                  @click="item.isClose = true"
                />
                <img
                  v-else
                  class="open"
                  src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/57dd17331428838889503_%E4%B8%8B.png"
                  @click="item.isClose = false"
                />
              </div>
            </div>
            <div class="address" v-if="item.address">
              <img
                class="address-img"
                src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/53a1173442499072733755_dizhi.png"
              />
              {{ item.address }}
            </div>
            <div class="class-progress">
              <span class="label">班级进度</span>
              <div class="progress">
                <van-progress
                  :percentage="
                    getProgress(item.lesson_attendance_num, item.lesson_num)
                  "
                  :show-pivot="false"
                  color="#018CFF"
                />
              </div>
              <span class="label"
                >{{ item.lesson_attendance_num }}/{{ item.lesson_num }}</span
              >
            </div>
          </div>
          <div v-if="!item.isClose" class="learn-course-list-content">
            <div
              v-for="(lessons, index) in item.lesson"
              class="learn-course-lessons"
            >
              <div
                class="learn-course-lessons-name"
                @click="goLookCourse(lessons, item?.teaching_type)"
              >
                {{ lessons.lesson_name }}
              </div>
              <div
                class="learn-course-lessons-message"
                @click="goLookCourse(lessons, item?.teaching_type)"
              >
                <div class="learn-course-lessons-message-left">
                  <div
                    v-if="item.teaching_type == '1'"
                    class="learn-course-lessons-type"
                  >
                    <img
                      v-if="lessons.lesson_status == '3'"
                      class="learn-course-lessons-type-img"
                      src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/2759173314312481144525_huifang.png"
                    />
                    <!--                  <img-->
                    <!--                      v-if="lessons.status == '3'"-->
                    <!--                      class="learn-course-lessons-type-img"-->
                    <!--                      src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/531817331432239781486_shengchenghuifang.png"-->
                    <!--                  />-->
                    <img
                      v-if="lessons.lesson_status != '3'"
                      class="learn-course-lessons-type-img"
                      src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/896f173314326200816744_zhibo.png"
                    />
                    <!--                  <img-->
                    <!--                    class="learn-course-lessons-type-img"-->
                    <!--                    src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/52fd173314329947918956_shipin.png-->
                    <!--"-->
                    <!--                  />-->
                    <text>{{
                      lessons.lesson_status == "3" ? "回放" : "直播"
                    }}</text>
                  </div>
                  <div v-if="item.teaching_type == '1'" class="line"></div>
                  <div class="learn-course-lessons-time">
                    {{ lessons.date }}
                  </div>
                  <div class="line"></div>
                  <div class="learn-course-lessons-teacher">
                    {{ lessons.teacher }}
                  </div>
                </div>
                <div class="learn-course-lessons-message-right">
                  <span v-if="lessons.status == '2'">已学完</span>
                  <!--                <span v-if="lessons.status=='2'" style="color: #018CFF">正在学</span>-->
                  <span v-if="lessons.status != '2'" style="color: #018CFF"
                    >未学习</span
                  >
                </div>
              </div>
              <!--              :class="{'no-border':!lessons?.independent?.length && (index==(item.lesson.length-1))}"-->
              <div
                v-if="
                  lessons?.evaluation_type_top &&
                  lessons?.evaluation_type_top?.length
                "
                class="operation-box"
              >
                <div
                  v-for="btn in lessons.evaluation_type_top"
                  class="operation-box-item preview"
                  @click="goAnswer(lessons, btn)"
                >
                  <img :src="completePath(btn.icon)" class="operation-img" />
                  <text class="operation-text">{{ btn.name }}</text>
                </div>
                <div
                  v-if="lessons.resource_document?.length"
                  class="operation-box-item preview"
                  @click="goDataDownload(lessons.resource_document[0].path)"
                >
                  <img
                    class="operation-img"
                    src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/6473173622834392761808_ziliao.png"
                  />
                  <text class="operation-text">资料</text>
                </div>
                <!--              <div v-if="lessons.pingjia == '1'" class="operation-box-item preview">-->
                <!--                <img-->
                <!--                    class="operation-img"-->
                <!--                    src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/9028173310453840513498_pingce.png"-->
                <!--                ></img>-->
                <!--                <text class="operation-text">评价</text>-->
                <!--              </div>-->
              </div>
              <div
                v-if="
                  lessons?.evaluation_type_bottom &&
                  lessons?.evaluation_type_bottom?.length
                "
                class="independent-list"
              >
                <div
                  v-for="independent in lessons.evaluation_type_bottom"
                  class="independent-item"
                >
                  <div class="independent-list-item-title">
                    <img
                      :src="completePath(independent.icon)"
                      alt=""
                      class="independent-list-item-title-img"
                    />
                    <span class="independent-list-item-title-name">{{
                      independent.name
                    }}</span>
                  </div>
                  <div
                    v-if="independent.is_evaluation == '1'"
                    class="independent-list-item-operate over"
                    @click="goAnswer(lessons, independent)"
                  >
                    <span>已完成</span>
                  </div>
                  <div
                    v-if="independent.is_evaluation == '2'"
                    class="independent-list-item-operate go-exam"
                    @click="goAnswer(lessons, independent)"
                  >
                    <span>去考试</span>
                  </div>
                  <!--                <div v-if="independent.status== '3'" class="independent-list-item-operate unlock">-->
                  <!--                  <img alt=""-->
                  <!--                       src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/795d173431941362452725_suo2.png">-->
                  <!--                  <span>解锁</span>-->
                  <!--                </div>-->
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div v-if="loading" style="text-align: center; margin-top: 20px">
      <van-loading />
    </div>
  </div>
</template>
<script lang="ts" setup>
const router = useRouter();
const route = useRoute();
const { study } = useApi();
const emit = defineEmits([
  "update",
  "update:goods_id",
  "update:goods_pid",
  "update:order_id",
]);

const loading = ref(true) as any;
const learnCourseList = ref([]) as any;

interface Props {
  goods_id: string;
  goods_pid: string;
  order_id: string;
}

const props = withDefaults(defineProps<Props>(), {
  goods_id: "",
  goods_pid: "",
  order_id: "",
});

const getCourseDetailLessons = () => {
  study
    .courseDetailLessons({
      goods_id: props.goods_id,
      goods_pid: props.goods_pid,
      order_id: props.order_id,
    })
    .then(({ data }: any) => {
      if (data && data.length) {
        learnCourseList.value = data;
        learnCourseList.value = learnCourseList.value
          .map((item: any) => {
            return {
              ...item,
              lesson: item?.lesson?.map((lesson: any) => {
                return {
                  ...lesson,
                  evaluation_type_top: lesson.evaluation_type.filter(
                    (btn: any) =>
                      btn.is_separate == "2" &&
                      btn.paper_version_id &&
                      btn.paper_version_id != "0"
                  ),
                  evaluation_type_bottom: lesson.evaluation_type.filter(
                    (btn: any) =>
                      btn.is_separate == "1" &&
                      btn.paper_version_id &&
                      btn.paper_version_id != "0"
                  ),
                };
              }),
            };
          })
          .filter((item: any) => {
            return item.id == route.query.filter_goods_id;
          });
      } else {
        learnCourseList.value = [];
      }
    })
    .catch((err) => {
      console.log(err);
      showFailToast(err.msg[0]);
      if (err.code == 100002) {
        showFailToast(err.msg[0]);
        studyLoginLose();
      }
    })
    .finally(() => {
      loading.value = false;
    });
};
getCourseDetailLessons();

const goLookCourse = (lesson: any, teaching_type: any) => {
  if (!teaching_type || teaching_type != "1") {
    return;
  }
  study
    .liveUrl({
      lesson_id: lesson.lesson_id,
    })
    .then(({ data }: any) => {
      if (data?.playback_url) {
        emit("update", {
          url: data.playback_url,
        });
        // emit('update:goods_id', lesson.goods_id)
        // emit('update:goods_pid', lesson.goods_pid)
        // emit('update:order_id', lesson.order_id)

        getCourseDetailLessons();
      } else {
        router.push({
          path: `/study/live`,
          query: {
            lesson_id: lesson?.lesson_id || "",
            url: data.live_url,
          },
        });
      }
    })
    .catch(() => {
      showFailToast("获取直播地址失败！");
    });
};
const goDataDownload = (url: any) => {
  if (!url) {
    showToast("暂无资料");
    return;
  }
  preview(completePath(url));
};

const goAnswer = (lessons: any, btn: any) => {
  router.push({
    path: `/answertest/answer`,
    query: {
      paper_version_id: btn?.paper_version_id || "",
      evaluation_type_id: btn?.id || "",
      professional_id: btn?.professional_id || "",
      goods_id: lessons?.paper_goods_id || "",
      order_id: lessons?.order_id || "",
      system_id: lessons?.system_id || "",
      order_detail_id: lessons?.order_goods_detail_id || "",
      lesson_id: lessons.lesson_id || "",
    },
  });
};
// 拼接完整路径
const env = useRuntimeConfig().public;

function completePath(path: string): string {
  return env.VITE_APP_BASEOSSURL + path;
}
function getProgress(overNum: any, totalNum: any) {
  if (!totalNum || !overNum || totalNum == "0" || overNum == "0") {
    return 0;
  }
  return Math.round(Number((overNum / totalNum) * 100));
}
</script>
<style lang="scss" scoped>
.course-list {
  padding: 0px 12px;

  .course-list-content {
    padding-bottom: 100px;

    .learn-course-list {
      margin-top: 11px;

      .learn-course-list-item {
        background: #fff;
        border-radius: 6px;
        overflow: hidden;

        .learn-course-list-title {
          background: linear-gradient(
            180deg,
            #e8fff2 0%,
            #f1fff8 50%,
            #f4fffb 100%
          );
          padding: 18px;

          .title {
            font-weight: 500;
            font-size: 15px;
            color: #262629;
            width: 100%;
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 14px;

            .title-name {
              display: flex;
              justify-content: flex-start;
              align-items: center;

              .teaching-type {
                width: 31px;
                height: 17px;
                background: #018CFF;
                border-radius: 2px;
                display: inline-flex;
                justify-content: center;
                align-items: center;
                font-weight: 400;
                font-size: 11px;
                color: #ffffff;
                margin-right: 6px;
              }
            }

            .close-open {
              display: flex;
              justify-content: flex-start;
              align-items: center;

              .close {
                width: 17px;
                height: 17px;
              }

              .open {
                width: 17px;
                height: 17px;
              }
            }
          }

          .address {
            .address-img {
              width: 14px;
              height: 14px;
              margin-right: 5px;
            }

            display: flex;
            justify-content: flex-start;
            align-items: flex-start;
            font-weight: 400;
            line-height: 18px;
            font-size: 12px;
            color: #424b57;
            margin-bottom: 16px;
          }

          .class-progress {
            display: flex;
            justify-content: flex-start;
            align-items: center;

            .label {
              font-weight: 400;
              font-size: 12px;
              color: #424b57;
            }

            .progress {
              margin: 0px 17px 0px 9px;

              flex: 1;
            }
          }
        }

        .learn-course-list-content {
          padding: 18px 16px 0px;

          .learn-course-lessons {
            //padding-bottom: 11px;
            margin-bottom: 19px;
            border-bottom: 1px solid rgba(232, 233, 234, 0.6);

            .learn-course-lessons-name {
              margin-bottom: 11px;
              font-weight: 600;
              font-size: 15px;
              color: #262629;
              line-height: 22px;
            }

            .learn-course-lessons-message {
              display: flex;
              justify-content: space-between;
              align-items: center;
              margin-bottom: 20px;
              font-weight: 400;
              font-size: 12px;
              color: #93969f;

              .learn-course-lessons-message-left {
                display: flex;
                justify-content: flex-start;
                align-items: center;

                .learn-course-lessons-type {
                  display: flex;
                  justify-content: flex-start;
                  align-items: center;
                  font-weight: 400;
                  font-size: 12px;
                  color: #424b57;

                  .learn-course-lessons-type-img {
                    width: 12px;
                    height: 12px;
                    margin-right: 7px;
                  }
                }

                .line {
                  width: 1px;
                  height: 10px;
                  background: #e1e5e8;
                  margin: 0 6px;
                }

                .learn-course-lessons-time {
                }

                .learn-course-lessons-teacher {
                }
              }

              .learn-course-lessons-message-right {
              }
            }

            .operation-box {
              display: flex;
              justify-content: flex-end;
              align-items: center;
              font-weight: 400;
              font-size: 12px;
              color: #424b57;
              padding-bottom: 22px;
              border-bottom: 1px solid #e8e9ea;

              .operation-box-item {
                display: flex;
                justify-content: flex-start;
                align-items: center;
              }

              .operation-box-item:not(:last-child) {
                position: relative;
                padding-right: 24px;
                margin-right: 24px;
              }

              .operation-box-item:not(:last-child)::after {
                content: "";
                width: 1px;
                height: 10px;
                background: #eceff1;
                position: absolute;
                right: 0;
                top: 2px;
              }

              .operation-img {
                width: 14px;
                height: 14px;
                margin-right: 3px;
              }

              .operation-text {
                color: #424b57;
              }
            }

            .no-border {
              //border-bottom: none;
            }

            .independent-list {
              padding-top: 10px;
              padding-bottom: 10px;
              .independent-item {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 10px 0;
                //border-bottom: 1px solid #E8E9EA;

                .independent-list-item-title {
                  display: flex;
                  justify-content: flex-start;
                  align-items: center;

                  .independent-list-item-title-img {
                    width: 24px;
                    height: 24px;
                    marign-right: 8px;
                  }

                  .independent-list-item-title-name {
                    font-weight: 600;
                    font-size: 14px;
                    color: #262629;
                  }
                }

                .independent-list-item-operate {
                  display: flex;
                  justify-content: center;
                  align-items: center;
                  font-weight: 400;
                  font-size: 12px;
                  color: #93969f;
                  margin-right: 18px;
                }

                .over {
                }

                .go-exam {
                  margin-right: 0px;
                  font-weight: 400;
                  font-size: 12px;
                  color: #018CFF;
                  width: 72px;
                  height: 28px;
                  border-radius: 14px;
                  border: 1px solid rgba(1, 163, 99, 0.56);
                  text-align: center;
                  line-height: 26px;
                }

                .over {
                  margin-right: 0px;
                  font-weight: 400;
                  font-size: 12px;
                  width: 72px;
                  height: 28px;
                  border-radius: 14px;
                  border: 1px solid #e2e2e2;
                  text-align: center;
                  line-height: 26px;
                }

                .unlock {
                  display: flex;
                  justify-content: center;
                  align-items: center;
                  margin-right: 0px;
                  font-weight: 400;
                  font-size: 12px;
                  color: #969696;
                  width: 72px;
                  height: 28px;
                  border-radius: 14px;
                  border: 1px solid transparent;
                  background: #efefef;

                  img {
                    width: 14px;
                    height: 14px;
                    margin-right: 2px;
                  }
                }
              }
            }
          }
        }

        .learn-course-lessons:last-child {
          margin-bottom: 0;
          border-bottom: none;

          .independent-list {
            .independent-item:last-child {
              border-bottom: 0px;
            }
          }
        }
      }

      .learn-course-list-item:not(:first-child) {
        margin-top: 12px;
      }
    }
  }
}
</style>
