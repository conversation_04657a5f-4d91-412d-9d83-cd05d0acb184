<template>
  <div class="my-detail">
    <div class="video-box" v-for="(item, index) in url">
      <iframe
        v-if="active == index + 1"
        :src="item"
        class="baijiayun-playback-iframe"
        allowfullscreen="true"
        webkitallowfullscreen="true"
        mozallowfullscreen="true"
      ></iframe>
    </div>
    <!--    <div class="tabs">-->
    <!--      <div v-for="item in options" :class="{active: active==item.value}" class="tabs-item"-->
    <!--           @click="changeSelected(item.value)">-->
    <!--        {{ item.text }}-->
    <!--      </div>-->
    <!--    </div>-->
    <!--    <div style="padding: 0px 12px;">-->
    <!--      <div v-if="recentlyData.lesson_id" class="go-on-learn">-->
    <!--        <div class="go-on-learn-describe">-->
    <!--          <img-->
    <!--              class="go-on-learn-img"-->
    <!--              src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/5022173314276286241077_%E6%92%AD%E6%94%BE.png"-->
    <!--          />-->
    <!--          <div class="go-on-learn-name"> {{ recentlyData.lesson_name }}</div>-->
    <!--        </div>-->
    <!--        <div class="go-on-learn-btn" @click="goLookCourse(recentlyData.lesson_id)"><span>继续学习</span></div>-->
    <!--      </div>-->
    <!--    </div>-->
    <!--    <ModuleStudyVideoCourseList ref="CourseList" v-model:goods_id="goods_id" v-model:goods_pid="goods_pid" v-model:order_id="order_id"-->
    <!--                                @update="update"/>-->
  </div>
</template>
<script lang="ts" setup>
const route = useRoute();
const router = useRouter();
const { study } = useApi();
const emit = defineEmits(["changeLesson_id"]);

const CourseList = ref(null);
const goods_id = ref("") as any;
const goods_pid = ref("") as any;
const order_id = ref("") as any;
let getID = () => {
  goods_id.value = route.query.goods_id;
  goods_pid.value = route.query.goods_pid;
  order_id.value = route.query.order_id;
};
getID();

let queryUrl = route.query.url || ("" as any);
let url = ref([]) as any;

const active = ref("1") as any;
const options = ref([]) as any;
const recentlyData = ref({
  lesson_id: "",
  lesson_name: "",
}) as any;

if (queryUrl) {
  if (queryUrl.indexOf(",") == -1) {
    url.value.push(route.query.url);
  } else {
    let arr = queryUrl?.split(",");
    arr.forEach((item: any, index: any) => {
      url.value.push(item);
      options.value.push({
        text: "回放" + (index + 1),
        value: index + 1,
      });
    });
  }
}
const changeSelected = (val: any) => {
  active.value = val;
};
const update = (val: any) => {
  queryUrl = val.url;
  url.value = [];
  options.value = [];
  if (queryUrl) {
    if (queryUrl.indexOf(",") == -1) {
      url.value.push(route.query.url);
    } else {
      let arr = queryUrl?.split(",");
      arr.forEach((item: any, index: any) => {
        url.value.push(item);
        options.value.push({
          text: "回放" + (index + 1),
          value: index + 1,
        });
      });
    }
  }
};
const getCourseDetailRecently = () => {
  study
    .courseDetailRecently({
      goods_id: goods_id.value || "",
      goods_pid: goods_pid.value || "",
      order_id: order_id.value || "",
    })
    .then(({ data }: any) => {
      if (data && data.lesson_id) {
        recentlyData.value = data;
      } else {
        recentlyData.value = {
          lesson_id: "",
          lesson_name: "",
        };
      }
    });
};
getCourseDetailRecently();

const goLookCourse = (lesson_id: any) => {
  study
    .liveUrl({
      lesson_id: lesson_id,
    })
    .then(({ data }: any) => {
      if (data?.playback_url) {
        // router.push({
        //   path: `/study/video`,
        //   query: {
        //     url: data.playback_url,
        //     filter_goods_id: data?.goods_id || '',
        //     goods_id: goods_id.value || '',
        //     goods_pid: goods_pid.value || '',
        //     order_id: order_id.value || ''
        //   }
        // })
        update({ url: data.playback_url });
        emit("changeLesson_id", lesson_id);
      } else {
        router.push({
          path: `/study/live`,
          query: {
            lesson_id: lesson_id,
            url: data.live_url,
          },
        });
      }
    })
    .catch(() => {
      showFailToast("获取直播地址失败！");
    });
};
</script>
<style lang="scss" scoped>
.my-detail {
  //position: fixed;
  //top: 0;
  //left: 0;
  //width: 100vw;
  //height: 100vh;
  //overflow-y: scroll;
  min-height: 100vh;
  height: 100vh;
  width: 100vw;
  background-color: #f2f5f7;

  .video-box {
    height: 100vh;
    width: 100vw;
  }
  .baijiayun-playback-iframe {
    height: 100%;
    width: 100%;
    //height: 100%;
  }

  .go-on-learn {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #fff;
    margin-top: 20px;
    padding: 12px 16px;
    border-radius: 6px;

    .go-on-learn-describe {
      display: flex;
      justify-content: flex-start;
      align-items: center;

      .go-on-learn-img {
        width: 20px;
        height: 20px;
        margin-right: 10px;
      }
    }

    .go-on-learn-btn {
      box-sizing: border-box;
      font-weight: 400;
      font-size: 12px;
      color: #018CFF;
      text-align: center;
      height: 28px;
      width: 72px;
      background: rgba(1, 163, 99, 0.07);
      border-radius: 14px;
      border: 1px solid rgba(1, 163, 99, 0.47);
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }

  .tabs {
    display: flex;
    justify-content: flex-start;
    align-items: flex-end;
    box-sizing: border-box;
    padding: 0px 20px;
    padding-top: 10px;
    overflow: hidden;

    .tabs-item {
      min-width: 50px;
      margin-right: 32px;
      font-weight: 500;
      font-size: 14px;
      color: rgba(51, 51, 51, 0.85);
    }

    .active {
      position: relative;
      font-weight: 600;
      font-size: 17px;
      color: #333333;
    }

    .active::before {
      position: absolute;
      left: calc(50% - 11px);
      bottom: -8px;
      content: "";
      width: 22px;
      height: 3px;
      background: #018CFF;
      border-radius: 1px;
    }
  }
}
</style>
