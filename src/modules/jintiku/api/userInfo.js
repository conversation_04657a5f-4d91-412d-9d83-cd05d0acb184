/**
 * @我的相关
 */
import http from './request'
// 收藏列表
export const getLearningData = function (data = {}) {
  return http({
    url: '/c/tiku/exam/learning/data',
    method: 'GET',
    data
  })
}
// 成绩报告列表
export const getScorereporting = function (data = {}) {
  return http({
    url: '/c/tiku/exam/scorereporting/list',
    method: 'GET',
    data
  })
}
// 获取考试时间
export const getExamTime = function (data = {}) {
  return http({
    url: '/c/tiku/configclient/getconfiginfo',
    method: 'GET',
    data
  })
}
// 设置考试时间
export const setTimeInfo = function (data = {}) {
  return http({
    url: '/c/tiku/configclient/setconfiginfo',
    method: 'POST',
    header: {
      'Content-Type': 'application/json'
    },
    data
  })
}

export const setconfiginfo = function (data = {}) {
  return http({
    url: '/c/tiku/configclient/setconfiginfo',
    method: 'POST',
    header: {
      'Content-Type': 'application/json'
    },
    data
  })
}
