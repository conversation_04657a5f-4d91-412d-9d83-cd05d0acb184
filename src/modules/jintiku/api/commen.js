/**
 * @公共接口
 */
import http from './request'
// 出题接口
export const getQuestionsList = data => {
  return http({
    url: '/c/tiku/question/getquestionlist',
    method: 'GET',
    data
  })
}
// 提交答案接口
export const setAnswer = data => {
  return http({
    url: '/c/tiku/question/answer',
    method: 'POST',
    data
  })
}
// 题收藏
export const collect = function (data = {}) {
  return http({
    url: '/c/tiku/question/practice/collect',
    method: 'GET',
    data
  })
}
// 题纠错
export const correction = function (data = {}) {
  return http({
    url: '/c/tiku/question/correction',
    method: 'POST',
    header: {
      'Content-Type': 'application/json'
    },
    data
  })
}
// 获取题型
export const getQuestionType = function (data = {}) {
  return http({
    url: '/c/tiku/question/type',
    method: 'GET',
    data
  })
}
// 获取品牌
export const getMerchantbrands = function (data = {}) {
  // return http({
  //   url: '/c/student/merchantbrands',
  //   method: 'GET',
  //   data
  // })
  return http({
    url: '/c/student/merchantbrands',
    method: 'POST',
    data
  })
}
