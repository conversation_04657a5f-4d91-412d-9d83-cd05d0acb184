/**
 * @错题本
 */
import http from './request'
// 获取章节
export const getWronganswerbook = function (data = {}) {
  return http({
    url: '/c/tiku/wronganswerbook',
    method: 'GET',
    data
  })
}

// 考点词条 标记
export const wronganswerbookMark = function (data = {}) {
  return http({
    url: '/c/tiku/wronganswerbook/mark',
    method: 'POST',
    header: {
      'Content-Type': 'application/json'
    },
    data
  })
}
// 考点词条 移出
export const wronganswerbookRemove = function (data = {}) {
  return http({
    url: '/c/tiku/wronganswerbook/remove',
    method: 'POST',
    header: {
      'Content-Type': 'application/json'
    },
    data
  })
}
