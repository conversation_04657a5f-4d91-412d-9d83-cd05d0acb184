/**
 * @真题闯关相关
 */
import http from './request'
// 获取章节列表
export const getChallengelist = function (data = {}) {
  return http({
    url: '/c/tiku/challenge/challengelist',
    method: 'GET',
    data
  })
}
// 获取闯关关卡
export const getCheckpoint = function (data = {}) {
  return http({
    url: '/c/tiku/challenge/checkpoint/list',
    method: 'GET',
    data
  })
}
// 跳过闯关关卡
export const skipChallenge = function (data = {}) {
  return http({
    url: '/c/tiku/challenge/skip',
    method: 'POST',
    header: {
      'Content-Type': 'application/json'
    },
    data
  })
}
// 报告
export const report = function (data = {}) {
  return http({
    url: '/c/tiku/challenge/checkpoint/report',
    method: 'GET',
    data
  })
}
// 闯关记录
export const getRecordList = function (data = {}) {
  return http({
    url: '/c/tiku/challenge/record',
    method: 'GET',
    data
  })
}
// 闯关数据
export const getRecordData = function (data = {}) {
  return http({
    url: '/c/tiku/challenge/data',
    method: 'GET',
    data
  })
}
