/**
 * @智能测评相关
 */
import http from './request'
// 获取章节
export const getevaluationlist = function (data = {}) {
  return http({
    url: '/c/tiku/evaluation/getevaluationlist',
    method: 'GET',
    data
  })
}
// 智能测评报告
export const getEvaluationsituation = function (data = {}) {
  return http({
    url: '/c/tiku/evaluation/getevaluationsituation',
    method: 'GET',
    data
  })
}
// 开启刷题计划
export const setPlan = function (data = {}) {
  return http({
    url: '/c/tiku/evaluation/plan/enable',
    method: 'POST',
    header: {
      'Content-Type': 'application/json'
    },
    data
  })
}
// 刷题计划列表
export const getPlanList = function (data = {}) {
  return http({
    url: '/c/tiku/evaluation/plan',
    method: 'GET',
    data
  })
}
// 排行榜
export const ranking = function (data = {}) {
  return http({
    url: '/c/tiku/evaluation/ranking',
    method: 'GET',
    data
  })
}
// 刷题计划的总报告
export const planReport = function (data = {}) {
  return http({
    url: '/c/tiku/evaluation/plan/report',
    method: 'GET',
    data
  })
}
// 今日刷题计划报告
export const todyReport = function (data = {}) {
  return http({
    url: '/c/tiku/evaluation/plan/today/report',
    method: 'GET',
    data
  })
}
