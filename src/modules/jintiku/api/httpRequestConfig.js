export const timeout = 10000
export const noToastUrl = [
  // '/b/base/sms/sendcode'
  '/c/tiku/session/delayeeventlogs',
  '/c/tiku/session/lockscreeneventlogs',
  '/c/tiku/session/pauseeventlogs',
  '/c/tiku/session/smseventlogs',
  '/c/tiku/session/submiteventlogs'
]
export const noCheckoutLogin = [
  '/b/base/sms/sendcode',
  '/c/student/openid',
  '/c/student/login',
  '/c/student/mobile',
  '/c/student/appoint/detail',
  '/MiniApps/getSectionQuestionList',
  '/c/base/sms/sendcode',
  '/c/student/smslogin',
  '/c/goods/v2/detail',
  '/c/tiku/homepage/chapterpackage/tree',
  '/c/goods/v2',
  '/c/tiku/homepage/recommend/chapterpackage',
  '/o/base/url/param/json',
  '/c/marketing/wechatshare/qrcode',
  '/c/marketing/wechatshare/qrcode/decodescene'

  // '/c/tiku/mockexam/allexam'
]

// 游客模式的接口
export const visitorMode = [
  '/c/teaching/mapping/tree',
  '/c/goods/v2',
  '/c/goods/v2/detail',
  '/c/goods/v2/chapter',
  '/c/goods/v2/type/tree',
  '/c/goods/v2/city',
  '/c/base/campus/city',
  '/c/org/campus',
  '/c/tiku/exchange/code/active',
  '/c/tiku/exchange/code/active/codelist',
  '/c/goods/v2/detail',
  '/c/tiku/homepage/chapterpackage/tree'
]
