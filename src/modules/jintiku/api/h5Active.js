/**
 * @h5活动相关接口
 */
import http from './request'
// 发送短信验证码
export const sendCode = data => {
  return http({
    url: '/c/base/sms/sendcode',
    method: 'post',
    data
  })
}
// 获取code详情
export const getCodeDetail = data => {
  return http({
    url: '/c/tiku/exchange/code/active',
    method: 'GET',
    data
  })
}
// 兑换码兑换
export const setCode = data => {
  return http({
    url: '/c/tiku/exchange/code/receive',
    method: 'POST',
    data
  })
}
// 分享
export const codeShare = data => {
  return http({
    url: '/c/tiku/exchange/code/share',
    method: 'POST',
    data
  })
}

// 获取code段
export const getCodes = data => {
  return http({
    url: '/c/tiku/exchange/code/active/codelist',
    method: 'GET',
    data
  })
}
