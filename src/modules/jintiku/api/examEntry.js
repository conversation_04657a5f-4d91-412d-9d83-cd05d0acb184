/**
 * @考点词条
 */
import http from './request'
// 获取章节
export const getTestpointknacklist = function (data = {}) {
  return http({
    url: '/c/tiku/testingcentre/gettestpointknacklist',
    method: 'GET',
    data
  })
}
// 考点词条 二级
export const getTestpointknackchildlist = function (data = {}) {
  return http({
    url: '/c/tiku/testingcentre/gettestpointknackchildlist',
    method: 'GET',
    data
  })
}
// 考点词条 词条信息
export const getTestpointknackquestion = function (data = {}) {
  return http({
    url: '/c/tiku/testingcentre/gettestpointknackquestion',
    method: 'GET',
    data
  })
}
// 考点词条 收藏考点目录
export const getTestpointcollectlist = function (data = {}) {
  return http({
    url: '/c/tiku/testingcentre/testingcentrecollectlist',
    method: 'GET',
    data
  })
}
// 考点词条
export const memorization = function (data = {}) {
  return http({
    url: '/c/tiku/testingcentre/memorization',
    method: 'POST',
    header: {
      'Content-Type': 'application/json'
    },
    data
  })
}
// 考点词条 收藏
export const testingcentreCollect = function (data = {}) {
  return http({
    url: '/c/tiku/testingcentre/collect',
    method: 'POST',
    header: {
      'Content-Type': 'application/json'
    },
    data
  })
}

// 人脸识别
export const visioncomparison = function (data = {}) {
  return http({
    url: '/c/tiku/configclient/visioncomparison',
    method: 'GET',
    data
  })
}
