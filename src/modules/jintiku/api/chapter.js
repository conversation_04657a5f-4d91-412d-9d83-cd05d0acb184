/**
 * @章节练习相关
 */
// {
//   "path": "pages/userInfo/authentication",
//   "style": {
//     "navigationBarTitleText": "认证中心",
//     "enablePullDownRefresh": false
//   }
// },
import http from './request'
// 获取章节
export const getChapterlist = function (data = {}) {
  return http({
    url: '/c/tiku/chapter/list',
    method: 'GET',
    data
  })
}
// 章节练习包商品 树接口
export const chapterpackageTree = function (data = {}) {
  return http({
    url: '/c/tiku/homepage/chapterpackage/tree',
    method: 'GET',
    data
  })
}
export const scorereporting = function (data = {}) {
  return http({
    url: '/c/tiku/servicehall/scorereporting',
    method: 'GET',
    data
  })
}
