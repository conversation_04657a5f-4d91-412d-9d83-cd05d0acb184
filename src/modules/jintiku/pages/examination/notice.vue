<template>
  <view class="notice">
    <view class="details">
      <!-- <view>1、本次考试考试时间共90分钟。</view> -->
      <view> 1、本次考试时间共{{ parseInt(this.totalTime / 60) }}分钟。 </view>
      <view>
        2、本系统将对考生的考试情况进行全程监控请诚信考试。若考试过程存在违规行为，如监拍范围内无影像光线过暗影像不清楚、一直低头不面向摄像头、抓拍照片与系统底片不符、影像不全、监拍范围内出现陌生人等，系统将审核不通过，考试成绩记为0分。在三次考试机会未用完的情况下考生还可重新考试。
      </view>
      <view>
        3、在阅读“考试说明”后，点击“开始答题”或在倒计时完毕后，系统自动进入考试界面，全程提示考试剩余时间，请考生在规定时间内完成考试。
      </view>
      <view>
        4、考试过程中，请考生随时查看答题导航，确保试题全部作答完毕后，点击“交卷”。
      </view>
      <view>5、提交试卷后，显示本次考试客观题成绩。</view>
    </view>
    <view
      class="start_btton button"
      @click="goDetail('pages/examination/examinationing')"
    >
      开始答题（倒计时：{{ transformTime(time) }}）
    </view>
  </view>
</template>
<script>
import { transformTime } from '../../utils/index.js'
export default {
  components: {},
  name: 'notice', // 考试须知
  props: {},
  data() {
    return {
      time: 120,
      transformTime,
      overtimeModelValue: false,
      suspendModelValue: false,
      enforcementModelValue: false,
      abnormalModelValue: false,
      id: '',
      eid: '',
      pid: '',
      pvid: '',
      session: '',
      session_name: '',
      examTime: '',
      timeOut: null,
      totalTime: 0,
      mock_name: '0',
      status: 0
    }
  },
  created() {
    this.setTime()
  },
  onLoad: function (e) {
    this.id = e.id
    this.eid = e.eid
    this.pid = e.pid
    this.pvid = e.pvid
    this.session = e.session
    this.session_name = e.session_name
    this.examTime = e.time
    this.totalTime = e.totalTime
    this.mock_name = e.mock_name
    this.status = e.status
  },
  methods: {
    goDetail(url) {
      // this.allTime = this.examTime / 60
      // this.$xh.push(
      //   'jintiku',
      //   `${url}?id=${this.id}&eid=${this.eid}&pid=${this.pid}&pvid=${this.pvid}&session=${this.session}&session_name=${this.session_name}&time=${this.examTime}`
      // )
      uni.redirectTo({
        url: `/modules/jintiku/${url}?id=${this.id}&eid=${this.eid}&pid=${this.pid}&pvid=${this.pvid}&session=${this.session}&session_name=${this.session_name}&time=${this.examTime}&mock_name=${this.mock_name}&status=${this.status}`
      })
      clearTimeout(this.timeOut)
    },
    setTime() {
      if (this.time) {
        this.timeOut = setTimeout(() => {
          this.time--
          this.setTime()
        }, 1000)
      } else {
        this.goDetail('pages/examination/examinationing')
      }
    }
  },
  onShareAppMessage() {
    return this.$xh.shareAppMessage()
  }
}
</script>
<style lang="scss" scoped>
.notice {
  min-height: 100vh;
  background: #ffffff;
  padding: 38rpx 24rpx;
  .details {
    background: #ebf1ff;
    border-radius: 12rpx;
    padding: 32rpx;
    font-size: 26rpx;
    color: #787e8f;
    letter-spacing: 1rpx;
    line-height: 52rpx;
    font-weight: 400;
  }
  .start_btton {
    width: 600rpx;
    height: 72rpx;
    background: #2e68ff;
    color: #ffffff;
    font-size: 26rpx;
    text-align: center;
    line-height: 72rpx;
    border-radius: 34rpx;
    margin: 230rpx auto;
  }
}
</style>
