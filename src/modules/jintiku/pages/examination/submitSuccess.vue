<template>
  <view class="submit-success">
    <head-height :statusBarHeight.sync="statusBarHeight" />
    <!-- 顶部功能预览 -->
    <view class="priview-time" :style="{ top: statusBarHeight + 'px' }">
      <image
        src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/169509193147644ed169509193147782088_back.png"
        mode="widthFix"
        class="back"
        @click="returnList"
      />
      <view class="nums"> 交卷成功 </view>
      <!-- <view class="time">{{ transformTime(time) }}</view> -->
    </view>
    <view class="success_img">
      <img
        src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/16951933733783d0c169519337337967803_%E7%BC%96%E7%BB%84%402x%20(2).png"
        alt=""
      />
    </view>
    <view class="success_txt">交卷成功!</view>
    <view class="user_data">
      <view class="data_list">
        <view class="title">考生姓名：</view>
        <view class="content">{{ userName }}</view>
      </view>
      <view class="data_list">
        <view class="title">ID：</view>
        <view class="content">{{ userID }}</view>
      </view>
      <view class="data_list">
        <view class="title">考试名称：</view>
        <view class="content">{{ mock_name }}</view>
      </view>
      <view class="data_list">
        <view class="title">考试轮次：</view>
        <view class="content">{{ sessionName }}</view>
      </view>
    </view>
    <view class="return_button button" @click="returnList">返回考试日程</view>
  </view>
</template>
<script>
export default {
  components: {},
  name: 'submit-success', // 考试须知
  props: {},
  data() {
    return {
      sessionName: '',
      userName: '',
      userID: '',
      mock_name: ''
    }
  },
  created() {
    this.getUserData()
  },
  methods: {
    getUserData() {
      let tmpData = wx.getStorageSync('__xingyun_userinfo__')
      this.userName = tmpData.nickname || tmpData.student_name
      this.userID = tmpData.student_id
    },
    returnList() {
      uni.navigateBack({
        delta: 1
      })
      // uni.switchTab({ url: '/modules/jintiku/pages/examination/index' })
    }
  },
  onLoad: function (e) {
    this.sessionName = e.session_name
    this.mock_name = e.mock_name
  },
  onShareAppMessage() {
    return this.$xh.shareAppMessage()
  }
}
</script>
<style lang="scss" scoped>
.submit-success {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  flex-direction: column;
  min-height: 100vh;
  background: #ffffff;
  .priview-time {
    position: fixed;
    left: 0;
    top: 75rpx;
    width: 100vw;
    height: 96rpx;
    background-color: #fff;
    border-bottom: 1px solid #eee;
    display: flex;
    align-items: center;
    // justify-content: space-between;
    justify-content: center;
    padding: 0 40rpx;
    .nums {
      text {
        font-size: 32rpx;
        color: #000000;
      }
      text:last-child {
        color: #949494;
      }
    }
    .time {
      text {
        font-size: 32rpx;
        color: #000000;
      }
    }
    .back {
      position: absolute;
      width: 19rpx;
      height: 32rpx;
      left: 30rpx;
      top: 0;
      bottom: 0;
      margin: auto 0;
    }
    .error {
      position: absolute;
      left: 80rpx;
      top: 0;
      bottom: 0;
      margin: auto 0;
      width: 120rpx;
      height: 44rpx;
      text-align: center;
      line-height: 44rpx;
      background: #f5f5f5;
      border-radius: 8rpx;
      font-size: 24rpx;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 800;
      color: #999999;
    }
  }
  .success_img {
    width: 200rpx;
    height: 200rpx;
    margin: 378rpx 0 0;
    img {
      width: 100%;
      height: 100%;
    }
  }
  .success_txt {
    text-align: center;
    margin-top: 60rpx;
    font-size: 32rpx;
    color: #000000;
  }
  .user_data {
    // margin: 20rpx auto;
    margin-top: 20rpx;
    .data_list {
      display: flex;
      justify-content: flex-start;
      font-size: 26rpx;
      margin-top: 22rpx;
      .title {
        width: 135rpx;
        color: #161f30;
      }
      .content {
        color: #787e8f;
      }
    }
  }
  .return_button {
    width: 400rpx;
    height: 72rpx;
    line-height: 72rpx;
    text-align: center;
    background: #2e68ff;
    color: #ffffff;
    border-radius: 34rpx;
    font-size: 26rpx;
    margin-top: 100rpx;
  }
}
</style>
