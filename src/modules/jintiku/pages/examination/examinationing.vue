<template>
  <view class="page-baidu">
    <!-- <view class="head-height"> -->
    <head-height
      :statusBarHeight.sync="statusBarHeight"
      style="background-color: #fff; position: relative; z-index: 11"
    />
    <!-- </view> -->
    <!-- 顶部功能预览 -->
    <view class="priview-time" :style="{ top: statusBarHeight + 'px' }">
      <view
        class="top-head"
        :style="{ height: statusBarHeight + 20 + 'px' }"
        style="background-color: #fff"
      ></view>
      <view class="button success" @click="showModelfn"> 交卷 </view>
      <view class="nums">
        <text>{{ current + 1 }}</text>
        <text>/{{ lists.length }}</text>
      </view>
    </view>
    <view style="height: auto; overflow: hidden">
      <view class="h96"></view>
      <!-- 第几轮 -->
      <view class="title"> {{ `第${session}轮：${session_name}` }}</view>
      <view class="time">
        剩余考试时间：{{ getResidueTime(transformTime(time)) }}
      </view>
      <view class="uni-margin-wrap">
        <examination-question-swiper
          :lists.sync="lists"
          :index.sync="current"
          ref="exercise"
          @last="last"
          :examination_id="id"
          :examKey="examKey"
          v-if="lists.length"
        />
      </view>
      <view class="utils">
        <view
          class="gjb dtk button"
          @click="sheetShow = true"
          style="width: 78rpx"
        >
          <image
            src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/16950896298723a451695089629872551_dtk.png"
            mode="widthFix"
          />
          <text>答题卡</text>
        </view>

        <view class="gjb dtk button" @click="onDoubtClick" style="width: 78rpx">
          <image
            :src="
              lists[current].doubt
                ? 'https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/16956222038717b4a169562220387182065_%E7%BC%96%E7%BB%84%205%E5%A4%87%E4%BB%BD%402x.png'
                : 'https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/16967375279005243169673752790025047_%E5%85%A8%E9%83%A8%E8%A7%A3%E6%9E%90%E5%A4%87%E4%BB%BD%205%402x.png'
            "
            mode="widthFix"
          />
          <text :class="{ doubt: lists[current].doubt }">
            {{ lists[current].doubt ? '已标疑' : '标疑' }}
          </text>
        </view>
        <view class="gjb pre">
          <view class="btn button flex-center" @click="prev">上一题</view>
        </view>
        <view class="gjb next">
          <view class="btn button flex-center" @click="next">下一题</view>
        </view>
      </view>
    </view>

    <model
      v-model="showModel"
      :title="title"
      :desc="desc"
      :sure="sure"
      :cancel="cancel"
      @success="success"
    />
    <!-- 答题卡 -->
    <answer-sheet
      v-model="sheetShow"
      @change="sheetchange"
      :questions="lists"
    />
    <test-reminder-overtime
      :modelValue="overtimeModelValue"
      @close="overtimeModelValue = false"
      :addTimeInfo="addTimeInfo"
      v-if="overtimeModelValue"
    />
    <test-reminder-suspend
      :modelValue="suspendModelValue"
      @close="suspendModelValue = false"
      v-if="suspendModelValue"
    />
    <test-reminder-enforcement
      :modelValue="enforcementModelValue"
      @close="enforcementModelValue = false"
      @submit="handInPapers"
      v-if="enforcementModelValue"
    />
    <test-reminder-abnormal
      :modelValue="abnormalModelValue"
      @close="abnormalModelValue = false"
      v-if="abnormalModelValue"
    />
  </view>
</template>
<script>
import md5 from 'md5'
import { transformTime, throttle } from '../../utils/index.js'
import questionAnswer from '../../components/makeQuestion/question-answer.vue'
import answerSheet from '../../components/makeQuestion/answer-sheet.vue'
import model from '../../components/commen/uni-model.vue'
import mixin from '../../mixin/index.js'
import examinationQuestionSwiper from '../../components/makeQuestion/examination-question-swiper-new.vue'
import headHeight from '../../components/commen/head-height.vue'
import { setQuestionLists } from '../../utils/index.js'
// 几个弹框
import testReminderOvertime from '../../components/makeQuestion/tipDialog/test-reminder-overtime.vue'
import testReminderSuspend from '../../components/makeQuestion/tipDialog/test-reminder-suspend'
import testReminderEnforcement from '../../components/makeQuestion/tipDialog/test-reminder-enforcement'
import testReminderAbnormal from '../../components/makeQuestion/tipDialog/test-reminder-abnormal'
import {
  getQuestionsDetail,
  getstudentexaminfo,
  doSecond,
  // getjinTiKuMockExamLists,
  submitAnswer,
  getquestionlist,
  examSubmitAnswer,
  getTimeOut,
  getLock,
  getPause,
  // getMessage,
  getIsSubmit,
  setLastId
} from '../../api'
export default {
  mixins: [mixin],
  components: {
    questionAnswer,
    model,
    answerSheet,
    examinationQuestionSwiper,
    headHeight,
    testReminderOvertime,
    testReminderEnforcement,
    testReminderSuspend,
    testReminderAbnormal
  },
  data() {
    return {
      overtimeModelValue: false,
      suspendModelValue: false,
      enforcementModelValue: false,
      abnormalModelValue: false,
      statusBarHeight: 0,
      lists: [],
      allLists: [], // 全部
      errorStatic: false,
      total: 0,
      indicatorDots: false,
      autoplay: false,
      // 当前选定值
      current: 0,
      // 当前时间
      time: 0,
      transformTime,
      // 显示答题卡
      sheetShow: false,
      // 是否交卷
      isHadnPaper: false,
      // 显示授权登录按钮
      // authorizationShow: false,
      examKey: '',
      id: '',
      // type=>6 我的错题, type => 29  每日一练交卷,  type => 1 章节练习
      // type: 1,
      // mokao: false
      eid: '',
      status: '',
      pid: '',
      session: '',
      session_name: '',
      // doubt: []
      timing: '',
      informTime: '',
      mock_name: '',
      addTimeInfo: {},
      pauseInfo: {},
      type: 7, // 不同场景
      isSubmit: false, // 是否交卷了
      stringParams: '',
      switching: false,
      idIndex: null
    }
  },
  methods: {
    getExamKey(obj) {
      return md5(`${obj.student_id}${obj.id}${obj.eid}${obj.pvid}${obj.type}`)
    },
    throttle,
    back() {
      uni.navigateBack({
        delta: 1
      })
    },
    setTime() {
      let that = this
      if (this.isSubmit) {
        return
      }
      if (this.time > 0) {
        this.timing = setTimeout(() => {
          this.time--
          this.setTime()
        }, 1000)
      } else {
        // this.handInPapers()
        // 直接交卷
        // this.$showModel(
        //   '温馨提示',
        //   `您的考试时间已到，请交卷`,
        //   '确认',
        //   '退出',
        //   () => {
        //     this.handInPapers()
        //   }
        // )
        uni.showModal({
          title: '温馨提示',
          content: '您的考试时间已到，请交卷',
          cancelText: '退出考试',
          confirmText: '确认交卷',
          success: function (res) {
            if (res.confirm) {
              // console.log('用户点击确定');
              that.handInPapers()
            } else if (res.cancel) {
              // console.log('用户点击取消');
              uni.navigateBack({
                delta: 1
              })
            }
          }
        })
      }
    },
    informTiming() {
      let data = { examination_session_id: this.eid }
      this.informTime = setTimeout(() => {
        if (this.isSubmit) {
          // 如果交卷了就停
          return
        }
        // 延时
        getTimeOut(data).then(data => {
          if (data.data.logs.length) {
            let res = data.data.logs[0]
            if (res.created_at == this.addTimeInfo.date) {
              return
            } else {
              this.overtimeModelValue = true
              this.time =
                (new Date(this.$xh.iosTime(data.data.end)).getTime() -
                  new Date().getTime()) /
                1000
            }
            this.addTimeInfo = {
              time: res.seconds,
              date: res.created_at,
              end: data.data.end
            }
            // 增加帅军接口
            setLastId({
              event_type: '2',
              examination_session_id: this.eid,
              id: res.id
            })
          }
        })
        // 锁屏
        getLock(data).then(data => {
          if (data.data.logs.length) {
            this.abnormalModelValue = true
          }
        })
        // 暂停
        getPause(data).then(data => {
          if (data.data.logs.length) {
            let res = data.data.logs[0]
            // if (res.operate_type == 4) {
            //   this.setTime()
            //   this.suspendModelValue = false
            // } else if (res.operate_type == 3) {
            //   clearTimeout(this.timing)
            //   this.suspendModelValue = true
            // }
            if (res.created_at == this.pauseInfo.date) {
              return
            } else {
              // this.suspendModelValue = !this.suspendModelValue
              // if (this.suspendModelValue) {
              //   clearTimeout(this.timing)
              // } else {
              //   this.setTime()
              // }
              if (res.operate_type == 4) {
                clearTimeout(this.timing)
                this.setTime()
                // startTimeout(this.timing)
                this.suspendModelValue = false
              } else if (res.operate_type == 3) {
                clearTimeout(this.timing)
                this.suspendModelValue = true
              }
            }
            this.pauseInfo = {
              msg: res.msg,
              date: res.created_at,
              show: true
            }
          }
        })
        // 强制交卷
        getIsSubmit(data).then(data => {
          if (data.data.logs.length) {
            this.enforcementModelValue = true
          }
        })
        this.informTiming()
      }, 2000)
    },
    prev() {
      if (this.switching) {
        this.$xh.Toast('不要点太快哦！')
        return
      }
      this.switching = true
      this.$refs.exercise.prev && this.$refs.exercise.prev()
      setTimeout(() => {
        this.switching = false
      }, 300)
    },
    next() {
      if (this.switching) {
        this.$xh.Toast('不要点太快哦！')
        return
      }
      this.switching = true
      this.$refs.exercise.next && this.$refs.exercise.next()
      setTimeout(() => {
        this.switching = false
      }, 300)
    },
    last() {
      let none = this.lists.reduce((cur, next) => {
        if (next.user_option == '') {
          return cur + 1
        }
        return cur
      }, 0)
      if (!none) {
        // 直接交卷
        this.$showModel(
          '确认交卷？',
          `已经是最后一道题了，是否确认交卷?`,
          '确认',
          '再看看',
          () => {
            this.handInPapers()
          }
        )
      } else {
        this.$showModel(
          '确认交卷？',
          `还有${none}道题未作答，确定要交卷吗?`,
          '确认',
          '继续做题',
          () => {}
        )
      }
    },
    // 交卷
    handInPapers() {
      clearTimeout(this.informTime)
      if (this.isSubmit) {
        return
      }
      // 交卷
      let obj = {}
      let tmpData = JSON.parse(JSON.stringify(this.lists))
      tmpData.forEach(item => {
        if (!obj[item.question_id]) {
          obj[item.question_id] = item
          return
        }
        obj[item.question_id].stem_list.push(item.stem_list[0])
      })
      let arr = []
      for (let key in obj) {
        arr.push(obj[key])
      }
      // let cost_time = parseInt((+new Date() - this.time * 1000) / 1000)
      let cost_time = parseInt(this.examTime - this.time)
      if (cost_time < 1) {
        cost_time = 1
      }
      let product_id = this.eid
      let professional_id = this.pid
      let type = this.type
      let question_info = arr.map(item => {
        return {
          question_id: item.question_id,
          user_option: item.stem_list.map(res => {
            return {
              sub_question_id: res.id,
              answer: res.selected.map(jtem => {
                return item.type == '8' || item.type == '9' || item.type == '10'
                  ? jtem.replace('\n', '<br/>')
                  : String(jtem)
              })
            }
          })
        }
      })
      // console.log(question_info)
      // return
      examSubmitAnswer({
        cost_time,
        product_id,
        professional_id,
        type,
        question_info: JSON.stringify(question_info)
      }).then(data => {
        // this.$xh.push(
        //   'jintiku',
        //   `pages/examination/submitSuccess?session=${this.session}&session_name=${this.session_name}`
        // )
        this.isSubmit = true
        uni.redirectTo({
          url: `/modules/jintiku/pages/examination/submitSuccess?session_name=${this.session_name}&mock_name=${this.mock_name}`
        })
      })
      let dataList = uni.getStorageSync('__anwers_list__')
      // dataList.splice(this.idIndex, 1)
      delete dataList[this.examKey]
      uni.setStorageSync('__anwers_list__', dataList)
    },
    showModelfn() {
      // 计算还有多少题没有答
      let none = this.lists.reduce((cur, next) => {
        if (next.user_option == '') {
          return cur + 1
        }
        return cur
      }, 0)
      if (!none) {
        // 直接交卷
        this.handInPapers()
      } else {
        this.$showModel(
          '确认交卷？',
          `还有${none}道题未作答，确定要交卷吗?`,
          '确认',
          '继续做题',
          () => {}
        )
      }
    },
    success() {
      this.$hideModel()
      this.handInPapers()
      // if (isLogin()) {
      //   // 登陆了 - 直接跳试卷解析页
      //   this.showModelfn()
      // } else {
      //   // 未登录
      //   this.authorizationShow = true
      //   this.$hideModel()
      // }
    },
    sheetchange(index) {
      this.current = index
    },
    getList() {
      let data = {
        examination_id: this.id,
        examination_session_id: this.eid,
        professional_id: this.pid,
        paper_version_id: this.pvid,
        type: '7'
      }
      // let data = {
      //   examination_id: '485624580702999846',
      //   examination_session_id: '485624580703065382',
      //   professional_id: '423168382750295155',
      //   paper_version_id: '485624007996020006',
      //   type: 7,
      //   user_id: '479679109031534871'
      // }

      getQuestionsDetail({
        user_id: this.$store.state.jintiku.user_id,
        section_id: this.id
      }).then(data => {
        this.setTime()
      })
      let anwersList = uni.getStorageSync('__anwers_list__') || {}
      if (anwersList[this.examKey]) {
        this.lists = anwersList[this.examKey].lists
      } else {
        getquestionlist(data).then(res => {
          this.lists = setQuestionLists(res.data.section_info)
          this.getstudentexaminfo()
        })
      }
    },
    // 标疑
    onDoubtClick() {
      // if (!this.lists[this.current].stem_list[0].selected.length) {
      let isDoubt = this.lists[this.current].doubt
      // if (isDoubt) {
      //   return
      //   this.lists[this.current].doubt = false
      // } else {
      // this.doubt.push(this.current)
      this.lists[this.current].doubt = !this.lists[this.current].doubt
      // setTimeout(() => {
      //   this.next()
      // }, 200)
      // }
      // } else {
      //   return false
      // }
    },
    getResidueTime(time) {
      if (time.includes('NaN')) {
        uni.showModal({
          title: '考试时间有误',
          content: '请您退出考试重新进入，或者联系相关老师协助！',
          success: function (res) {
            // if (res.confirm) {
            //   console.log('用户点击确定')
            // } else if (res.cancel) {
            //   console.log('用户点击取消')
            // }
            console.log(this.stringParams)
          }
        })
        return '00:00:00'
      }
      return time
    },
    getstudentexaminfo() {
      getstudentexaminfo({
        exam_id: this.id,
        exam_round_id: this.eid
      }).then(res => {
        let time = 0
        if (this.status != 5) {
          //正式考试时间 =结束时间-当前时间
          time =
            (new Date(res.data.end_time).getTime() -
              new Date(res.data.current_time).getTime()) /
            1000
        } else {
          //补考考试时间=开始考试时间+总时间-当前时间
          let totalTime =
            (new Date(res.data.end_time).getTime() -
              new Date(res.data.start_time).getTime()) /
            1000
          let end_time =
            new Date(res.data.entry_time).getTime() / 1000 + totalTime
          time = end_time - new Date(res.data.current_time).getTime() / 1000
        }
        if (!isNaN(time)) {
          this.time = Math.floor(time)
        } else {
          this.time = 2 * 60 * 60
        }
        this.examTime = this.time
      })
    }
  },
  onLoad(e) {
    clearTimeout(this.informTime)
    clearTimeout(this.timing)
    uni.enableAlertBeforeUnload({
      message: '确认退出吗,您还没有交卷？',
      success: function (res) {
        console.log('点击确认按钮了：', res)
        // 发送请求
      },
      fail: function (errMsg) {
        console.log('点击取消按钮了：', errMsg)
      }
    })
    this.eid = e.eid
    this.pid = e.pid
    this.id = e.id
    this.status = e.status
    this.pvid = e.pvid
    this.session = e.session
    this.session_name = e.session_name
    this.time = e.time * 1
    this.examTime = e.time
    this.mock_name = e.mock_name
    this.type = e.type ? e.type : 7
    this.examKey = this.getExamKey({
      id: e.id,
      eid: e.eid,
      pvid: e.pvid,
      student_id: uni.getStorageSync('__xingyun_userinfo__').student_id,
      type: e.status == 5 ? '2' : '1'
    })

    if (isNaN(this.time) || this.time * 1 < 3) {
      // 说明传过来的时间不对
      this.time = 1 * 60 * 60
    }
    this.getList()
    this.informTiming()
    try {
      this.stringParams = JSON.stringify(e)
    } catch (e) {}
  },
  onUnload() {
    clearTimeout(this.informTime)
    clearTimeout(this.timing)
    // this.handInPapers()
  },
  onShow() {
    this.getstudentexaminfo()
  },
  onShareAppMessage() {
    return this.$xh.shareAppMessage()
  }
}
</script>
<style scoped lang="less">
.page-baidu {
  // height: 100vh;
  // .head-height {
  //   position: relative;
  //   z-index: 10;
  //   background-color: #fff;
  // }
  .priview-time {
    position: fixed;
    left: 0;
    top: 0;
    width: 100vw;
    height: 96rpx;
    background-color: #fff;
    border-bottom: 1px solid #eee;
    display: flex;
    align-items: center;
    // justify-content: space-between;
    justify-content: center;
    padding: 0 40rpx;
    z-index: 10;
    .top-head {
      position: absolute;
      width: 100%;
      top: -116rpx;
      left: 0;
    }
    .nums {
      text {
        font-size: 32rpx;
        color: #000000;
      }
      text:last-child {
        color: #949494;
      }
    }
    .time {
      text {
        font-size: 32rpx;
        color: #000000;
      }
    }
    .back {
      position: absolute;
      width: 19rpx;
      height: 32rpx;
      left: 30rpx;
      top: 0;
      bottom: 0;
      margin: auto 0;
    }
    .success {
      position: absolute;
      left: 40rpx;
      top: 0;
      bottom: 0;
      margin: auto 0;
      width: 120rpx;
      height: 44rpx;
      text-align: center;
      line-height: 44rpx;
      background: #2e68ff;
      border-radius: 8rpx;
      font-size: 24rpx;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 800;
      color: #fff;
    }
  }
  .h96 {
    height: 96rpx;
    background-color: #fff;
    position: relative;
    z-index: 9;
  }
  .title {
    font-size: 32rpx;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 800;
    color: #000000;
    height: 94rpx;
    line-height: 94rpx;
    text-align: center;
  }
  .time {
    height: 64rpx;
    line-height: 64rpx;
    background: #f5f8ff;
    font-size: 24rpx;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #2e68ff;
    text-align: center;
  }
  .uni-margin-wrap {
    width: calc(100vw);
    height: calc(100vh - 330rpx);
    overflow-y: scroll;
    padding-bottom: calc(80rpx + 42rpx + 36rpx);
    // padding-top: 96rpx;
    box-sizing: border-box;
  }
  .utils {
    position: fixed;
    left: 0;
    bottom: 0;
    width: 100vw;
    height: 80rpx;
    padding-bottom: 42rpx;
    padding-top: 36rpx;
    background-color: #fff;
    display: flex;
    align-items: center;
    justify-content: space-around;
    height: 158rpx;
    .gjb {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      image {
        width: 34rpx;
        height: 40rpx;
        margin-bottom: 14rpx;
      }
      text {
        font-size: 24rpx;
        color: rgba(41, 65, 90, 0.75);
      }
      .btn {
        width: 192rpx;
        height: 80rpx;
        border-radius: 40rpx;
        border: 1px solid #2e68ff;
        color: #2e68ff;
        font-size: 26rpx;
      }
      .doubt {
        color: #fb9e0c;
      }
    }
    .next {
      .btn {
        border-radius: 40rpx;
        border: 1px solid #2e68ff;
        color: #fff;
        // background: linear-gradient(270deg, #6d7dff 0%, #b3a1ff 100%);
        background: #2e68ff;
      }
    }
  }
}
</style>
