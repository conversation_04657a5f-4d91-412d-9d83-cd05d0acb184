<template>
  <view class="examination">
    <view v-if="isLoding">
      <view class="title" @click="getAllExam" v-if="data">考试日程</view>
      <examination-list :data="data" />
    </view>
    <view class="not_data" v-else>
      <view class="img">
        <img
          src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/16954369620338446169543696203498545_%E7%BC%96%E7%BB%84%402x%20(4).png"
          alt=""
        />
      </view>
      <view class="button goLogin" @click="goDetail('pages/loginCenter/index')">
        去登录
      </view>
    </view>
  </view>
</template>
<script>
import examinationList from '../../components/makeQuestion/examination-list.vue'
import { getAllExam } from '../../api/index'
export default {
  components: {
    examinationList
  },
  name: 'examination', // 考试日程
  props: {},
  data() {
    return {
      data: [],
      isLoding: false
    }
  },
  onShow() {
    console.log('show')
    this.getAllExam()
    // uni.getStorageSync('__xingyun_userinfo__').is_real_name == '1'
    //   ? ''
    //   : (this.realNameModelValue = true)
  },
  methods: {
    getAllExam() {
      let token = this.$store.state.jintiku.token
      if (token) {
        this.isLoding = true
        let data = {
          professional_id: '',
          from_client: 'ios',
          source: '2'
        }
        getAllExam(data).then(res => {
          this.data = res.data.list
        })
      } else {
        this.isLoding = false
      }
    },
    goDetail(url) {
      this.$xh.push('jintiku', url)
    }
  },
  onShow: function () {
    this.getAllExam()
  },
  onShareAppMessage() {
    return this.$xh.shareAppMessage()
  }
}
</script>
<style lang="scss" scoped>
.examination {
  padding: 30rpx 24rpx;
  min-height: 100vh;
  background: #ffffff;
  .title {
    font-size: 30rpx;
    font-weight: 500;
    color: #161f30;
    line-height: 30rpx;
    margin-bottom: 24rpx;
  }
  .not_data {
    width: 100%;
    margin: 379rpx auto;

    .img {
      width: 229rpx;
      height: 180rpx;
      // margin-bottom: 60rpx;
      margin: 0 auto;
      img {
        width: 100%;
        height: 100%;
      }
    }
    .goLogin {
      width: 260rpx;
      height: 80rpx;
      background: #2e68ff;
      color: #ffffff;
      font-size: 28rpx;
      text-align: center;
      line-height: 80rpx;
      border-radius: 38rpx;
      margin: 60rpx auto;
    }
  }
}
</style>
