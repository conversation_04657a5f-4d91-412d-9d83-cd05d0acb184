<template>
  <!-- 考试人脸识别 -->
  <view class="facial_recognition">
    <view class="camera_img">
      <camera
        device-position="front"
        flash="off"
        @error="error"
        class="camera"
        v-if="!src"
      />
      <img :src="src" v-else class="camera" />
    </view>
    <view class="explain">
      <view class="explain_title">操作说明</view>
      <view class="explain_text">1.请先确保已开启前置摄像头和麦克风</view>
      <view class="explain_text">2.请确保光源充足，不要逆光操作</view>
      <view class="explain_text">
        3.请保证脸部正面面向前置摄像头，并适当调整姿势保证整个脸部能够进入识别画面
      </view>
      <view class="explain_text">4.系统识别通过后，将自动跳转进入考试界面</view>
    </view>
    <view class="button start_button" @click="camer">开始识别</view>
  </view>
</template>
<script>
import { visioncomparison } from '../../api/examEntry'
export default {
  name: 'facial-recognition',
  components: {},
  data() {
    return {
      listener: null, // 监听帧
      passFlag: false, // 录制通过状态
      src: null,
      id: '',
      eid: '',
      pid: '',
      pvid: '',
      session: '',
      session_name: '',
      examTime: ''
    }
  },
  onLoad: function (e) {
    this.id = e.id
    this.eid = e.eid
    this.pid = e.pid
    this.pvid = e.pvid
    this.session = e.session
    this.session_name = e.session_name
    this.examTime = e.time
  },
  methods: {
    goDetail(url) {
      // this.allTime = this.examTime / 60
      this.$xh.push(
        'jintiku',
        `${url}?id=${this.id}&eid=${this.eid}&pid=${this.pid}&pvid=${this.pvid}&session=${this.session}&session_name=${this.session_name}&time=${this.examTime}`
      )
    },
    camer() {
      const that = this
      const ctx = uni.createCameraContext()
      ctx.takePhoto({
        quality: 'high',
        success: res => {
          that.src = res.tempImagePath //获取到的画面信息
          that.$xh
            .upLoad(res.tempImagePath)
            .then(item => {
              let data = {
                face_url: item
              }
              visioncomparison(data)
                .then(citem => {
                  that.goDetail('pages/examination/notice')
                })
                .catch(() => {
                  that.$xh.Toast('识别失败')
                  that.src = ''
                })
            })
            .catch(() => {
              that.src = ''
            })
        },
        fail: res => {
          that.src = ''
        }
      })
    },
    error(e) {
      console.log(e.detail)
    }
  },
  onShow() {
    this.src = null
  },
  onShareAppMessage() {
    return this.$xh.shareAppMessage()
  }
}
</script>
<style lang="less" scoped>
.facial_recognition {
  background: #fff;
  min-height: 100vh;
  width: 100%;
  padding-top: 60rpx;

  .camera_img {
    width: 400rpx;
    margin: 0 auto;
    height: 400rpx;
    .camera {
      width: 400rpx;
      height: 400rpx;
      border-radius: 50%;
    }
  }
  .explain {
    width: 556rpx;
    margin: 134rpx auto 0;
    font-size: 30rpx;
    color: #03203d;
    .explain_title {
      margin-bottom: 24rpx;
    }
    .explain_text {
      font-size: 26rpx;
      color: #787e8f;
      line-height: 52rpx;
    }
  }
  .start_button {
    width: 600rpx;
    height: 72rpx;
    background: #2e68ff;
    border-radius: 34rpx;
    line-height: 72rpx;
    text-align: center;
    font-size: 26rpx;
    color: #fff;
    margin: 102rpx auto 0;
  }
}
</style>
