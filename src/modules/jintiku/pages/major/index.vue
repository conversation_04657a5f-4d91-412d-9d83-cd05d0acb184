<template>
  <view class="box">
    <view class="top">你的专业是？</view>
    <view class="tips">填写后定制你的个性化课程中心</view>
    <view class="listBox" v-if="!checkId">
      <view
        v-for="(item, index) in dataList"
        :key="index"
        @click="choiceFun(item)"
        :class="{ viewOne: checkId == item.id }"
      >
        {{ item.data_name }}
      </view>
    </view>
    <view v-else>
      <view class="checkBox" @click="returnFun">
        <view>
          <img
            src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/1669641564098755c166964156409840548_left.png"
          />
          {{ checkName }}
        </view>
      </view>
      <view class="multipleBox" v-for="(item, index) in subsList" :key="index">
        <view class="titleBox">{{ item.data_name }}</view>
        <view class="listBox" v-if="item.subs && item.subs.length">
          <view
            v-for="(data, key) in item.subs"
            :key="key"
            @click="checkFun(data)"
            :class="{ viewOne: checkObj.major_id == data.id }"
            >{{ data.data_name }}</view
          >
        </view>
        <view class="listBox" v-else>
          <view
            @click="checkFun(item)"
            :class="{ viewOne: checkObj.major_id == item.id }"
          >
            {{ item.data_name }}
          </view>
        </view>
      </view>
      <view class="footer">
        <text>该信息可随时在首页更改</text>
      </view>
      <view class="btnBox">
        <view @click="submitFun">提交</view>
      </view>
    </view>
  </view>
</template>
<script>
import { getMajor, checkMajor } from '../../api/index'
import { transformDataId } from '../../utils/index'
export default {
  data() {
    return {
      dataList: [],
      checkId: '',
      checkName: '',
      subsList: [],
      checkObj: {
        id: '',
        major_id: ''
      },
      majorName: '',
      goback: false
    }
  },
  onLoad(e) {
    let { student_id = '' } = uni.getStorageSync('__xingyun_userinfo__')
    this.checkObj.id = student_id
    this.startFun()
    if (e.goback) {
      this.goback = true
    }
  },
  methods: {
    startFun() {
      let obj = {
        code: 'professional',
        is_auth: 2,
        is_usable: 1,
        professional_ids: ''
      }
      getMajor(obj).then(res => {
        this.dataList = transformDataId(res.data)
      })
    },
    // 选择专业
    choiceFun(data) {
      this.checkId = data.id
      this.subsList = data.subs
      this.checkName = data.data_name
    },
    returnFun() {
      this.checkName = ''
      this.checkId = ''
      // this.checkObj.id = ''
      this.checkObj.major_id = ''
    },
    checkFun(data) {
      this.checkObj.major_id = data.id
      this.majorName = data.data_name
    },
    submitFun() {
      if (!this.checkObj.major_id) {
        this.$xh.Toast('请先选择专业')
        return
      }
      let token = uni.getStorageSync('__xingyun_token__')
      if (token) {
        checkMajor(this.checkObj).then(res => {
          let obj = {
            major_id: this.checkObj.major_id,
            major_name: this.majorName
          }
          uni.setStorageSync('__xingyun_major__', obj)
          if (this.goback) {
            uni.navigateBack({
              delta: 1
            })
          } else {
            wx.switchTab({
              url: '/modules/jintiku/pages/index/index'
            })
          }
        })
        return
      }
      // 游客模式
      let obj = {
        major_id: this.checkObj.major_id,
        major_name: this.majorName
      }
      uni.setStorageSync('__xingyun_major__', obj)
      wx.switchTab({
        url: '/modules/jintiku/pages/index/index'
      })
    }
  },
  onShareAppMessage() {
    return this.$xh.shareAppMessage()
  }
}
</script>

<style scoped lang="less">
.box {
  width: 100%;
  min-height: 100vh;
  background: url(https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/16696400618188716166964006181894592_backThree.png);
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center 0;
  padding: 24rpx 6rpx 24rpx 24rpx;
  display: flex;
  flex-direction: column;
  position: relative;
  padding-bottom: 150rpx;
  .top {
    margin-top: 200rpx;
    text-align: center;
    font-size: 40rpx;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #03203d;
    line-height: 56rpx;
  }
  .tips {
    font-size: 22rpx;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: rgba(3, 32, 61, 0.45);
    margin-top: 4rpx;
    text-align: center;
    margin-bottom: 64rpx;
  }
  .listBox {
    display: flex;
    flex-wrap: wrap;
    view {
      min-width: 214rpx;
      padding: 0 30rpx;
      height: 68rpx;
      background: #ffffff;
      box-shadow: 0rpx 4rpx 24rpx 0rpx rgba(228, 242, 255, 0.5);
      border-radius: 32rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 24rpx;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #03203d;
      line-height: 34rpx;
      text-shadow: 0rpx 4rpx 24rpx rgba(228, 242, 255, 0.5);
      margin: 0 24rpx 32rpx 0;
    }
    .viewOne {
      color: #2e68ff;
      text-shadow: 0 4rpx 24rpx rgba(228, 242, 255, 0.5);
    }
  }
  .checkBox {
    min-width: 214rpx;
    height: 68rpx;
    display: inline-block;
    background: #ffffff;
    box-shadow: 0rpx 4rpx 24rpx 0rpx rgba(228, 242, 255, 0.5);
    border-radius: 32rpx;
    // display: flex;
    // align-items: center;
    // justify-content: center;
    font-size: 24rpx;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #2e68ff;
    line-height: 68rpx;
    text-shadow: 0rpx 4rpx 24rpx rgba(228, 242, 255, 0.5);
    // position: relative;
    margin-bottom: 64rpx;

    view {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 0 10rpx;
    }
    img {
      // position: absolute;
      width: 32rpx;
      height: 32rpx;
      // left: 24rpx;
    }
  }
  .multipleBox {
    margin-bottom: 48rpx;
    .titleBox {
      font-size: 24rpx;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: rgba(3, 32, 61, 0.45);
      margin-bottom: 32rpx;
    }
  }
  .footer {
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 208rpx;
    background: linear-gradient(
      180deg,
      rgba(255, 255, 255, 0) 0%,
      #ffffff 100%
    );
    text {
      position: absolute;
      left: 0;
      bottom: 24rpx;
      width: 100%;
      text-align: center;
      font-size: 22rpx;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: rgba(3, 32, 61, 0.45);
    }
  }
  .btnBox {
    width: 100%;
    position: fixed;
    left: 0;
    bottom: 100rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    view {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 496rpx;
      height: 88rpx;
      background: #2e68ff;
      box-shadow: 0rpx 8rpx 32rpx 0rpx rgba(46, 104, 255, 0.25);
      border-radius: 44rpx;
      font-size: 28rpx;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #ffffff;
    }
  }
}
</style>
