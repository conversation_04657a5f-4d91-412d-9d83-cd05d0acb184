<template>
  <view class="scheduleReport">
    <view class="day-text"> 坚持天数 </view>
    <view class="day">
      <text class="num">{{ total_day_number }}</text>
      <text>天</text>
    </view>
    <view class="rate">
      <view class="rateinfo left">
        <image
          src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/16968232033468762169682320334617642_edit.png"
        />
        <text> 连续刷题天数：{{ continuous_day_number }}天 </text>
      </view>
      <view class="rateinfo right">
        <image
          src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/169682329433224e0169682329433256420_damuge.png"
        />
        <text> 正确率：{{ correct_rate }}% </text>
      </view>
    </view>
    <view class="echarts">
      <view class="title">
        <view class="xie blod"></view>
        <view class="xie"></view>
        <view class="center">一周刷题正确率</view>
        <view class="xie blod"></view>
        <view class="xie"></view>
      </view>
      <view class="successRate">
        <view class="rate-lenged">
          <view class="left"> 正确率：% </view>
          <view class="left"> 日期：天 </view>
        </view>
        <qiun-data-charts type="line" :opts="opts" :chartData="chartData" />
      </view>
    </view>
    <view class="echarts">
      <view class="title">
        <view class="xie blod"></view>
        <view class="xie"></view>
        <view class="center">各题型正确率</view>
        <view class="xie blod"></view>
        <view class="xie"></view>
      </view>
      <view class="questionTypeRate">
        <view class="rate-lenged">
          <view class="left"> 正确率：% </view>
          <view class="left"> 日期：天 </view>
        </view>
        <qiun-data-charts
          type="column"
          :opts="opts2"
          :chartData="questionTypeData"
        />
      </view>
    </view>
  </view>
</template>
<script>
import { planReport } from '../../api/intelligentEvaluation'
export default {
  data() {
    return {
      chartData: {},
      questionTypeData: {},
      opts: {
        fontColor: '#2E68FF',
        legend: {
          show: false
        },
        xAxis: {
          rotateLabel: true,
          disableGrid: true,
          marginTop: 6
        },
        yAxis: {
          data: [
            {
              min: 0,
              max: 100
            }
          ]
        },
        extra: {
          line: {
            type: 'straight'
          }
        }
      },
      opts2: {
        fontColor: '#2E68FF',
        legend: {
          show: false
        },
        xAxis: {
          rotateLabel: true,
          disableGrid: true,
          marginTop: 6
        },
        yAxis: {
          data: [
            {
              min: 0,
              max: 100
            }
          ]
        },
        extra: {
          column: {
            type: 'group',
            width: 30,
            activeBgColor: '#000000',
            activeBgOpacity: 0.08
          }
        }
      },
      evaluation_id: '',
      // 统计过
      continuous_day_number: '9', // 连续刷题天数
      correct_rate: '9', // 正确率
      total_day_number: '3' // 已刷题天数
    }
  },
  onLoad(e) {
    this.evaluation_id = e.evaluation_id
    this.getServerData()
  },
  methods: {
    getServerData() {
      planReport({
        evaluation_id: this.evaluation_id
      }).then(data => {
        data = data.data
        let res = {
          all_section_data: [
            [
              {
                child: ['string'],
                correct_number: '答题正确数量',
                correct_rate: '正确率',
                error_number: '答题错误数量',
                name: '章节或者知识点名称'
              }
            ]
          ],
          continuous_day_number: '30',
          correct_rate: '8',
          question_type_correct_rate: [
            {
              correct_rate: '20', // 正确率
              type_name: 'A1'
            },
            {
              correct_rate: '30', // 正确率
              type_name: 'A2'
            },
            {
              correct_rate: '10', // 正确率
              type_name: 'A3'
            },
            {
              correct_rate: '80', // 正确率
              type_name: 'A4'
            },
            {
              correct_rate: '0', // 正确率
              type_name: 'A7'
            }
          ],
          total_day_number: '9',
          week_correct_rate: [
            {
              correct_rate: '10.5', // 正确率
              date: '2018-02-10'
            },
            {
              correct_rate: '30', // 正确率
              date: '2018-02-11'
            },
            {
              correct_rate: '40', // 正确率
              date: '2018-02-12'
            },
            {
              correct_rate: '50', // 正确率
              date: '2018-02-13'
            }
          ]
        }
        if (data.continuous_day_number) {
          res.continuous_day_number = data.continuous_day_number
        }
        if (data.all_section_data) {
          res.all_section_data = data.all_section_data
        }
        if (data.correct_rate) {
          res.correct_rate = data.correct_rate
        }
        if (data.question_type_correct_rate) {
          res.question_type_correct_rate = data.question_type_correct_rate
        }
        if (data.total_day_number) {
          res.total_day_number = data.total_day_number
        }
        if (data.week_correct_rate) {
          res.week_correct_rate = data.week_correct_rate
        }
        this.continuous_day_number = res.continuous_day_number
        this.correct_rate = res.correct_rate
        this.total_day_number = res.total_day_number
        let _asyncdata = {
          categories: res.week_correct_rate.map(item => item.date),
          series: [
            {
              name: '正确率',
              data: res.week_correct_rate.map(item => item.correct_rate * 1)
            }
          ]
        }
        this.chartData = JSON.parse(JSON.stringify(_asyncdata))
        let res2 = {
          categories: res.question_type_correct_rate.map(
            item => item.type_name
          ),
          series: [
            {
              name: '正确率',
              data: res.question_type_correct_rate.map(
                item => item.correct_rate * 1
              )
            }
          ]
        }
        this.questionTypeData = JSON.parse(JSON.stringify(res2))
      })
      setTimeout(() => {
        //模拟服务器返回数据，如果数据格式和标准格式不同，需自行按下面的格式拼接
        // let res = {
        //   categories: ['10-09', '10-08', '10-09', '10-10', '10-11', '10-12'],
        //   series: [
        //     {
        //       name: '一周刷题正确率',
        //       data: [35, 36, 31, 33, 13, 34]
        //     }
        //   ]
        // }
        // this.chartData = JSON.parse(JSON.stringify(res))
        // let res2 = {
        //   categories: ['10-09', '10-08', '10-09', '10-10', '10-11', '10-12'],
        //   series: [
        //     {
        //       name: '各题型正确率',
        //       data: [35, 36, 31, 33, 13, 34]
        //     }
        //   ]
        // }
        // this.questionTypeData = JSON.parse(JSON.stringify(res2))
      }, 500)
    }
  },
  onShareAppMessage() {
    return this.$xh.shareAppMessage()
  }
}
</script>
<style scoped lang="less">
.scheduleReport {
  background: url('https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/16968229101347ba2169682291013460276_nav.png')
    no-repeat 0 -100rpx;
  background-size: contain;
  min-height: 100vh;
  padding-top: 40rpx;
  padding: 0 30rpx;
  .day-text {
    text-align: center;
    font-size: 32rpx;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #ffffff;
    line-height: 36rpx;
    margin-bottom: 10rpx;
  }
  .day {
    display: flex;
    align-items: flex-end;
    justify-content: center;
    text-align: center;
    margin-top: 20rpx;
    margin-bottom: 40rpx;
    text {
      font-size: 24rpx;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #ffffff;
      // line-height: 316rpx;
      position: relative;
      top: -10rpx;
    }
    .num {
      top: 0rpx;
      font-size: 100rpx;
      font-family: PingFangSC-Semibold, PingFang SC;
      font-weight: 800;
      color: #f7d61d;
      line-height: 100rpx;
      transform: scaleY(1.1);
      margin-right: 2rpx;
      margin-right: 5rpx;
    }
  }
  .rate {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 40rpx;
    .rateinfo {
      display: flex;
      align-items: center;
      image {
        width: 26rpx;
        height: 26rpx;
        margin-right: 20rpx;
      }
      text {
        font-size: 24rpx;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #ffffff;
        line-height: 36rpx;
      }
    }
  }
  .echarts {
    background: #ffffff;
    box-shadow: 0px 6rpx 30rpx 0px rgba(37, 76, 144, 0.15);
    border-radius: 20rpx;
    padding: 40rpx 30rpx;
    margin-bottom: 20rpx;
    .title {
      display: flex;
      align-items: center;
      justify-content: center;
      .xie {
        height: 18rpx;
        border-left: 1px solid #2e68ff;
        margin-right: 10rpx;
        transform: rotateZ(25deg);
        transform-origin: left bottom;
      }
      .blod {
        border-left: 2px solid #2e68ff;
      }
      .center {
        font-size: 32rpx;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 800;
        color: #161f30;
        line-height: 36rpx;
        margin: 0 20rpx;
      }
    }
    .rate-lenged {
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-size: 22rpx;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #787e8f;
      line-height: 28rpx;
    }
  }
}
</style>
