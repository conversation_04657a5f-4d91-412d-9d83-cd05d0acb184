<template>
  <view class="report">
    <view class="cicre-box">
      <view class="rate">
        <view class="l-circular-progress-class">
          <view class="circular-progress">
            <l-circular-progress
              :percent="accuracy"
              :lineWidth="10"
              bgColor="#EDF5FF"
              gradualColor="#95C0FA"
              :boxWidth="150"
              :boxHeight="150"
              :fontShow="false"
            ></l-circular-progress>
          </view>
          <view class="cicle-rate">
            <text class="u-progress-text">正确率</text>
            <text class="u-progress-info">{{ accuracy }}%</text>
          </view>
        </view>

        <view class="info">
          <view class="title"> 考试通过预测： </view>
          <view class="desc"> {{ result }} </view>
          <view class="button reset-button flex-center" @click="reset">
            重新测评
          </view>
        </view>
      </view>
      <view class="line"></view>
      <view class="knows">
        <view class="text"> 掌握知识点数： </view>
        <view class="nums">
          <text>{{ grasp_knowledge_num }}/{{ knowledge_num }}</text>
          <text>个</text>
        </view>
      </view>
    </view>
    <view class="konws-no">
      <view class="title"> 薄弱知识点 </view>
      <view class="lists">
        <view class="list active">
          <view class="name">知识点</view>
          <view class="rate">正确率</view>
        </view>
        <view
          class="list"
          :class="{ active: i % 2 != 0 }"
          v-for="(item, i) in knows"
          :key="i"
        >
          <view class="name">{{ item.name ? item.name : '未知知识点' }}</view>
          <view class="rate">{{ item.accuracy }}%</view>
        </view>
      </view>
    </view>
    <view class="konws-test konws-no">
      <view class="title"> 测试分析 </view>
      <view class="desc">
        {{ analyse }}
      </view>
      <view class="buttons flex">
        <view
          class="button flex-center"
          @click="
            goDetail(
              `pages/makeQuestion/lookAnalysisQuestion?type=3&evaluation_id=${evaluation_id}`
            )
          "
        >
          查看测评错题
        </view>
        <!-- gray -->
        <view class="button flex-center" @click="next" v-if="is_passed == '1'">
          下一等级测评
        </view>
        <!-- 没开启专属刷题计划的时候现实 -->
        <view
          class="button flex-center active"
          @click="openPlan"
          v-if="is_passed != '1' && isHasDoQuestionPlan != '1'"
        >
          开启专属刷题计划
        </view>
      </view>
    </view>
  </view>
</template>
<script>
import lCircularProgress from '../../../../components/l-circular-progress.vue'
import {
  getEvaluationsituation,
  setPlan
} from '../../api/intelligentEvaluation'
export default {
  components: {
    lCircularProgress
  },
  data() {
    return {
      knows: [],
      evaluation_id: '',
      report_id: '',
      analyse: `暂无`, // ""测试分析""
      grasp_knowledge_num: 0, // ""掌握知识点的数量""
      accuracy: 0, // ""正确率""
      knowledge_num: 0, // ""知识点的数量""
      result: '暂无', // "测试评语"
      score: 0, // "得分"
      is_passed: '2', // 是否过关
      isHasDoQuestionPlan: '2' // 是否开启了专属刷题计划
    }
  },
  onLoad(e) {
    this.evaluation_id = e.evaluation_id
    this.report_id = e.report_id
    if (uni.getStorageSync('__ai_test__')) {
      let res = uni.getStorageSync('__ai_test__')
      this.isHasDoQuestionPlan = res.isHasDoQuestionPlan
    }
    this.getInfo()
  },
  methods: {
    openPlan() {
      setPlan({
        evaluation_situation_id: this.report_id
      }).then(res => {
        this.$xh.Toast('开启成功，赶快去刷题吧！')
        uni.navigateBack({
          delta: 1
        })
      })
    },
    next() {
      uni.navigateBack({
        delta: 1
      })
    },
    reset() {
      uni.redirectTo({
        url:
          '/modules/jintiku/pages/intelligentEvaluation/practise?evaluation_id=' +
          this.evaluation_id,
        fail(err) {
          console.log(err)
        }
      })
    },
    getInfo() {
      getEvaluationsituation({
        evaluation_situations_id: this.report_id
      }).then(data => {
        let res = data.data
        this.analyse = res.analyse
        this.grasp_knowledge_num = res.grasp_knowledge_num
        this.accuracy = res.accuracy
        this.knowledge_num = res.knowledge_num
        this.result = res.result
        this.score = res.score
        this.knows = res.section_list ? res.section_list : []
        this.is_passed = res.is_passed
      })
    },
    goDetail(url) {
      this.$xh.push('jintiku', url)
    }
  },
  onShareAppMessage() {
    return this.$xh.shareAppMessage()
  }
}
</script>

<style scoped lang="less">
.report {
  height: 100vh;
  overflow-y: scroll;
  background: url('https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/16968229101347ba2169682291013460276_nav.png')
    no-repeat;
  background-size: contain;
  padding: 0 30rpx;
  padding-top: 120rpx;
  .cicre-box {
    margin-bottom: 30rpx;
    padding: 42rpx;
    border-radius: 20rpx;
    box-shadow: 0px 6px 30px 0px rgba(37, 76, 144, 0.15);
    background-color: #fff;
    .rate {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .l-circular-progress-class {
        position: relative;
        .circular-progress {
          transform: rotateZ(-90deg);
        }
        .cicle-rate {
          width: 236rpx;
          height: 236rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          flex-direction: column;
          position: absolute;
          left: 0;
          right: 0;
          top: 0;
          bottom: 0;
          margin: auto;
          .u-progress-text {
            font-size: 24rpx;
            font-family: PingFangSC-Medium, PingFang SC;
            font-weight: 500;
            color: #999999;
            line-height: 24rpx;
            margin-bottom: 16rpx;
          }
          .u-progress-info {
            font-size: 60rpx;
            font-family: PingFangSC-Medium, PingFang SC;
            font-weight: 900;
            color: #666666;
            line-height: 60rpx;
          }
        }
      }

      .info {
        // padding-right: 20rpx;
        margin-left: 70rpx;
        flex: 1;
        .title {
          font-size: 28rpx;
          font-family: PingFangSC-Medium, PingFang SC;
          // font-weight: 800;
          color: #999999;
          line-height: 28rpx;
          margin-bottom: 20rpx;
        }
        .desc {
          font-size: 48rpx;
          font-family: STSongti-SC-Black, STSongti-SC;
          font-weight: 900;
          color: #387dfc;
          line-height: 48rpx;
          margin-bottom: 30rpx;
        }
        .reset-button {
          width: 170rpx;
          height: 60rpx;
          background-color: #387dfc;
          color: #fff;
          border-radius: 30rpx;
        }
      }
    }
    .line {
      border-bottom: 2rpx dashed #387dfc;
      margin: 40rpx 0;
    }
    .knows {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .text {
        font-size: 28rpx;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #999999;
        line-height: 28rpx;
      }
      .nums {
        display: flex;
        align-items: center;
        text {
          font-size: 40rpx;
          font-family: PingFangSC-Medium, PingFang SC;
          font-weight: 500;
          color: #387dfc;
          line-height: 28rpx;
        }
        text:last-child {
          font-size: 28rpx;
          font-weight: 400;
          color: #999999;
        }
      }
    }
  }
  .konws-no {
    margin-bottom: 30rpx;
    padding: 42rpx;
    border-radius: 20rpx;
    box-shadow: 0px 6px 30px 0px rgba(37, 76, 144, 0.15);
    background-color: #fff;
    .title {
      text-align: center;
      font-size: 32rpx;
      font-family: AppleSystemUIFont;
      color: #333333;
      line-height: 32rpx;
      margin-bottom: 40rpx;
    }
    .lists {
      .list {
        display: flex;
        .name {
          width: 400rpx;
          border-right: 1px solid #fff;
        }
        .rate {
          flex: 1;
        }
        .name,
        .rate {
          background: rgba(56, 125, 252, 0.15);
          height: 70rpx;
          text-align: center;
          font-size: 26rpx;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #333333;
          line-height: 70rpx;
          padding: 0 36rpx;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
      .active {
        .name,
        .rate {
          background: rgba(56, 125, 252, 0.05);
        }
      }
    }
    .desc {
      font-size: 28rpx;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #333333;
      line-height: 42rpx;
    }
    .buttons {
      margin-top: 40rpx;
      .button {
        width: 280rpx;
        height: 90rpx;
        border-radius: 45rpx;
        border: 1px solid #387dfc;
        color: #387dfc;
        margin-right: 30rpx;
      }
      .gray {
        background-color: #fff;
        color: #ccc;
        border: 1px solid #ccc;
      }
      .active {
        background-color: #387dfc;
        color: #fff;
        border: 1px solid #387dfc;
      }
    }
  }
}
</style>
