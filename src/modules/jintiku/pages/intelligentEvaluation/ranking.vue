<template>
  <view class="scheduleReport">
    <view class="container">
      <view class="tabs">
        <view class="tab" :class="{ active: tab == 0 }" @click="tab = 0">
          今日榜单
        </view>
        <view class="tab" :class="{ active: tab == 1 }" @click="tab = 1">
          本周榜单
        </view>
      </view>
      <view class="static">
        <view class="info">
          <view class="title">今日做题</view>
          <view class="num">{{ do_number }}</view>
        </view>
        <view class="line"></view>
        <view class="info">
          <view class="title">正确</view>
          <view class="num">{{ correct_num }}</view>
        </view>
        <view class="line"></view>
        <view class="info">
          <view class="title">当前排名</view>
          <view class="num">{{ rank }}</view>
        </view>
      </view>
      <view class="tab-row"></view>
      <view class="lists">
        <view class="line-title">
          <view class="pm">排名</view>
          <view class="nickname">昵称</view>
          <view class="questionsnums">做题数量</view>
          <view class="successnums">正确数量</view>
          <view class="tb">同比</view>
        </view>
        <view class="line-title" v-for="(item, i) in list" :key="i">
          <view class="pm">
            <image
              src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/16968447770986f61169684477709878453_1.png"
              mode=""
              v-if="i == 0"
            />
            <image
              src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/16968447977014fce169684479770170899_2.png"
              mode=""
              v-else-if="i == 1"
            />
            <image
              src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/16968448107682458169684481076897988_3.png"
              mode=""
              v-else-if="i == 2"
            />
            <text v-else> {{ i + 1 }}</text>
          </view>
          <view class="nickname">{{ item.user_name }}</view>
          <view class="questionsnums">{{ item.do_question_num }}</view>
          <view class="successnums">{{ item.do_right_num }}</view>
          <view class="tb">
            <image
              :class="{ up: item.compare_status == '3' }"
              src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/16968448277963f4c169684482779635069_4.png"
              mode="widthFix"
            />
          </view>
        </view>
      </view>
    </view>
  </view>
</template>
<script>
import { ranking } from '../../api/intelligentEvaluation'
export default {
  data() {
    return {
      tab: 0,
      today_info: {
        correct_num: '30', // 答对试题数量
        do_number: '100', // 答题数量
        rank: '10' // 排名
      },
      today_list: [
        // {
        //   compare_status: '2',
        //   do_question_num: '10',
        //   do_right_num: '9',
        //   rank: '1',
        //   user_id: '用户id',
        //   user_name: '小明'
        // }
      ],
      week_info: {
        correct_num: '20', // 答对试题数量
        do_number: '20', // 答题数量
        rank: '39' // 排名
      },
      week_list: [
        // {
        //   compare_status: '刷题量环比(1:相等; 2:上升; 3:下降)',
        //   do_question_num: '答题数量',
        //   do_right_num: '答对试题数量',
        //   rank: '排名',
        //   user_id: '用户id',
        //   user_name: '用户名称'
        // }
      ]
    }
  },
  onLoad() {
    this.getList()
  },
  methods: {
    getList() {
      ranking({}).then(data => {
        let res = data.data
        this.today_info = res.today_info
        this.today_list = res.today_list ? res.today_list : []
        this.week_info = res.week_info
        this.week_list = res.week_list ? res.week_list : []
      })
    }
  },
  computed: {
    do_number() {
      if (this.tab == 0) {
        return this.today_info.do_number
      } else {
        return this.week_info.do_number
      }
    },
    correct_num() {
      if (this.tab == 0) {
        return this.today_info.correct_num
      } else {
        return this.week_info.correct_num
      }
    },
    rank() {
      if (this.tab == 0) {
        return this.today_info.rank
      } else {
        return this.week_info.rank
      }
    },
    list() {
      if (this.tab == 0) {
        return this.today_list
      } else {
        return this.week_list
      }
    }
  },
  onShareAppMessage() {
    return this.$xh.shareAppMessage()
  }
}
</script>
<style scoped lang="less">
.scheduleReport {
  background: url('https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/16968433431793e1216968433431801577_phb.png')
    no-repeat 0 -50rpx;
  background-size: contain;
  min-height: 100vh;
  padding-top: 240rpx;
  .container {
    background-color: #fff;
    border-radius: 30rpx 30rpx 0px 0px;
    .tabs {
      height: 150rpx;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 100rpx;
      .tab {
        font-size: 32rpx;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #9a9fae;
        line-height: 28rpx;
      }
      .active {
        font-size: 32rpx;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 800;
        color: #387dfc;
        line-height: 28px;
        position: relative;
        &::before {
          content: '';
          position: absolute;
          left: 0;
          right: 0;
          margin: 0 auto;
          bottom: -6rpx;
          width: 30rpx;
          height: 4rpx;
          border-radius: 3px;
          background: #387dfc;
        }
      }
    }
    .static {
      display: flex;
      align-items: center;
      justify-content: space-around;
      margin-bottom: 50rpx;
      .info {
        .title {
          text-align: center;
          font-size: 28rpx;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #666666;
          line-height: 40rpx;
          margin-bottom: 10rpx;
        }
        .num {
          font-size: 36rpx;
          font-family: AppleSystemUIFont;
          color: #387dfc;
          line-height: 43rpx;
          text-align: center;
        }
      }
      .line {
        width: 2rpx;
        height: 36rpx;
        background: #d2d2d2;
      }
    }
    .tab-row {
      height: 16rpx;
      background: #f5f5f5;
      margin-bottom: 50rpx;
    }
    .lists {
      padding: 0 30rpx;
      .line-title {
        display: flex;
        align-items: center;
        margin-bottom: 50rpx;
        border-bottom: 1px solid #eee;
        padding-bottom: 16rpx;
        view {
          flex: 1;
          text-align: center;
          font-size: 28rpx;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #333333;
          line-height: 28rpx;
        }
        .pm {
          image {
            width: 32rpx;
            height: 40rpx;
          }
        }
        .tb {
          image {
            width: 22rpx;
            height: 28rpx;
          }
          .up {
            transform: rotateZ(180deg);
          }
        }
      }
    }
  }
}
</style>
