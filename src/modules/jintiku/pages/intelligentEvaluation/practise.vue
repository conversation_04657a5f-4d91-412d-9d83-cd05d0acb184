<template>
  <view class="page-baidu">
    <!-- 顶部功能预览 -->
    <view class="priview-time">
      <view class="static-text">
        <image
          src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/16952026981143f8f169520269811490483_pause.png"
          mode="widthFix"
          @click="pauseFn"
        />
        <view class="nums">
          <text>{{ current + 1 }}</text>
          <text>/{{ total }}</text>
        </view>
      </view>
    </view>
    <view class="h96"></view>
    <!-- 倒计时组件 -->
    <countdown @end="tiemEnd" ref="countdown" />
    <view class="uni-margin-wrap">
      <!-- 做题组件 -->
      <practise-question-swiper
        :lists.sync="lists"
        :index.sync="current"
        :disableTouch="true"
        :skipHiddenItemLayout="true"
        ref="exercise"
        @success="submit"
        @next="next"
      ></practise-question-swiper>
    </view>
    <!-- 操作 -->
    <view class="options" v-if="pause">
      <view class="xx">休息中...</view>
      <view class="option" @click="startFn">
        <image
          src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/16952814828718ea3169528148287133396_pl.png"
          mode="widthFix"
        />
        <text>继续练习</text>
      </view>
      <view class="option" @click="resetFn">
        <image
          src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/17018597400085f20170185974000877515_reset.png"
          mode="widthFix"
        />
        <text>重新开始</text>
      </view>
      <view class="option" @click="back">
        <image
          src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/16952815292322121169528152923283917_log.png"
          mode="widthFix"
        />
        <text>退出练习</text>
      </view>
    </view>
  </view>
</template>
<script>
import questionAnswer from '../../components/makeQuestion/question-answer.vue'
import answerSheet from '../../components/makeQuestion/answer-sheet.vue'
import practiseQuestionSwiper from '../../components/makeQuestion/practise-question-swiper.vue'
import headHeight from '../../components/commen/head-height.vue'
import countdown from '../../components/commen/countdown.vue'
import { setQuestionLists, setSubmitPageData } from '../../utils/index'
import { getQuestionsList, setAnswer } from '../../api/commen'
export default {
  components: {
    questionAnswer,
    answerSheet,
    practiseQuestionSwiper,
    headHeight,
    countdown
  },
  data() {
    return {
      lists: [], // 小题的数据列
      total: 0, // 统计的小题数量
      maxList: [], // 大题的数据列 - 把每道题的答题时间挂到这上面
      startDate: +new Date(), // 整体的开始时间
      maxStart: +new Date(), // 每道题的开始时间
      // 当前小题序号
      current: 0,
      // 当前时间
      time: 0, // 没有用
      evaluation_id: '',
      pause: false, // 是否暂停
      isSubmit: false,
      type: '3',
      evaluation_plan_id: ''
    }
  },
  computed: {
    // 计算用户答题到哪个答题了
    maxCurrent() {
      if (this.lists[this.current]) {
        let id = this.lists[this.current].id
        return this.maxList.findIndex(res => res.id == id) + 1
      } else {
        return 1
      }
    }
  },
  watch: {
    maxCurrent(val) {
      let time = +new Date()
      let cost_time = (time - this.maxStart) / 1000
      this.maxStart = +new Date()
      if (this.maxList[val - 2]) {
        this.maxList[val - 2].cost_time = cost_time
      }
    }
  },
  methods: {
    next() {
      this.$refs.countdown.reset && this.$refs.countdown.reset()
    },
    reset() {
      uni.navigateBack({
        delta: 1
      })
    },
    resetFn() {
      this.reload()
    },
    back() {
      uni.navigateBack({
        delta: 2
      })
    },
    pauseFn() {
      this.pause = true
      this.$refs.countdown.pause && this.$refs.countdown.pause()
    },
    startFn() {
      this.pause = false
      this.$refs.countdown.play && this.$refs.countdown.play()
    },
    tiemEnd() {
      // 一题的时间到
      // 时间到 就下一题
      if (this.current + 1 >= this.total) {
        // 已经完事了 -执行交卷逻辑
        this.submit()
      } else {
        this.current += 1
        // 重置时间
        this.$refs.countdown.reset && this.$refs.countdown.reset(true)
      }
    },
    selecte(info) {
      this.lists = this.lists.map(res => {
        if (res.questionid == info.questionid) {
          return {
            ...res,
            ...info
          }
        }
        return res
      })
      this.next()
    },
    // 交卷
    submit() {
      if (this.isSubmit) {
        return
      }
      this.maxList[this.maxList.length - 1].cost_time =
        (+new Date() - this.maxStart) / 1000

      let resultQuestion = setSubmitPageData(this.lists).map((item, i) => {
        return {
          ...item,
          cost_time: Math.ceil(this.maxList[i].cost_time)
        }
      })
      let obj = {
        type: this.type,
        question_info: JSON.stringify(resultQuestion),
        cost_time: Math.floor((+new Date() - this.startDate) / 1000),
        product_id: this.evaluation_id
          ? this.evaluation_id
          : this.evaluation_plan_id, // id跟进传入的type去做匹配
        start_time: this.$xh.parseFullTime(this.startDate)
      }
      setAnswer({
        ...obj
      }).then(data => {
        // 提交成功
        this.isSubmit = true
        if (this.type == '3') {
          // 智能测评跳转
          uni.redirectTo({
            url: `/modules/jintiku/pages/intelligentEvaluation/report?evaluation_id=${this.evaluation_id}&report_id=${data.data.practice_record_id}`,
            fail(err) {
              console.log(err)
            }
          })
        } else {
          // 刷题计划任务
          uni.redirectTo({
            url: `/modules/jintiku/pages/intelligentEvaluation/planQuestionReport?type=4&evaluation_plan_id=${this.evaluation_plan_id}`,
            fail(err) {
              console.log(err)
            }
          })
        }
      })
    },
    sheetchange(index) {
      this.current = index
    },
    reload() {
      // 页面重载
      const pages = getCurrentPages()
      const curPage = pages[pages.length - 1]
      // 声明一个当前页面
      // curPage.onLoad(curPage.options) // 传入参数
      // curPage.onShow()
      // curPage.onReady()
      let options = curPage.options
      let params = []
      for (let key in options) {
        params.push(`${key}=${options[key]}`)
      }
      uni.redirectTo({
        url: `/${curPage.route}?${params.join('&')}`,
        fail(err) {
          console.log(err)
        }
      })
    },
    getList() {
      getQuestionsList({
        evaluation_id: this.evaluation_id,
        evaluation_plan_id: this.evaluation_plan_id,
        type: this.type
      }).then(data => {
        this.lists = setQuestionLists(data.data.section_info)
        this.maxList = data.data.section_info
        this.total = this.lists.length
      })
    }
  },
  onLoad(e) {
    console.log(e)
    uni.enableAlertBeforeUnload({
      message: '学贵有恒，不要轻言放弃，确认退出吗？',
      success: function (res) {
        console.log('点击确认按钮了：', res)
      },
      fail: function (errMsg) {
        console.log('点击取消按钮了：', errMsg)
      }
    })
    if (e.evaluation_id) {
      this.evaluation_id = e.evaluation_id
    }
    if (e.type) {
      this.type = e.type
    }
    if (e.evaluation_plan_id) {
      this.evaluation_plan_id = e.evaluation_plan_id
    }
    this.getList()
  },
  onShareAppMessage() {
    return this.$xh.shareAppMessage()
  }
}
</script>
<style scoped lang="less">
.page-baidu {
  position: relative;
  .head-height {
    position: relative;
    z-index: 10;
  }
  .priview-time {
    position: fixed;
    left: 0;
    top: 0rpx;
    width: 100vw;
    height: 96rpx;
    background-color: #fff;
    border-bottom: 1px solid #eee;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 40rpx;
    z-index: 10;
    .nums {
      text {
        font-size: 32rpx;
        color: #000000;
      }
      text:last-child {
        color: #949494;
      }
    }
    .time {
      text {
        font-size: 32rpx;
        color: #000000;
      }
    }
    .static-text {
      display: flex;
      align-items: center;
      image {
        width: 40rpx;
        height: 40rpx;
        margin-right: 37rpx;
      }
    }
  }
  .h96 {
    height: 96rpx;
    background-color: #fff;
    position: relative;
    z-index: 9;
  }
  .title {
    font-size: 32rpx;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 800;
    color: #000000;
    height: 94rpx;
    line-height: 94rpx;
    text-align: center;
  }
  .time {
    height: 64rpx;
    line-height: 64rpx;
    background: #f5f8ff;
    font-size: 24rpx;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #2e68ff;
    text-align: center;
  }
  .uni-margin-wrap {
    width: calc(100vw);
    height: calc(100vh - 100rpx);
    overflow-y: scroll;
    // padding-bottom: calc(80rpx + 42rpx + 36rpx);
    // padding-top: 96rpx;
    box-sizing: border-box;
  }
  .options {
    position: fixed;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: #fff;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 11;
    .xx {
      color: #2e68ff;
      font-size: 28rpx;
      margin-top: -400rpx;
    }
    .option {
      margin-top: 40rpx;
      display: flex;
      align-items: center;
      image {
        width: 32rpx;
        height: 32rpx;
        margin-right: 20rpx;
      }
      text {
        color: #333;
        font-size: 28rpx;
      }
    }
  }
}
</style>
