<template>
  <view class="page-baidu">
    <head-height :statusBarHeight.sync="statusBarHeight" />
    <!-- 顶部功能预览 -->
    <view class="priview-time" :style="{ top: statusBarHeight + 'px' }">
      <image
        src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/169509193147644ed169509193147782088_back.png"
        mode="widthFix"
        class="back"
        @click="back"
      />
      <view class="nums">
        <text>{{ current + 1 }}</text>
        <text>/{{ lists.length }}</text>
      </view>
    </view>
    <view class="uni-margin-wrap">
      <chapter-exercise-question-swiper
        :showAnswer="true"
        :lists.sync="lists"
        :index.sync="current"
        ref="exercise"
      ></chapter-exercise-question-swiper>
    </view>
    <bottom-utils
      :current.sync="current"
      :lists="lists"
      :currentData="currentData"
      :utils="['errorAnswerSheet', 'collect', 'errorCorrection']"
      :isnextChapter="isnextChapter == 1"
      @nextChapter="getNextChapter"
    >
      <view class="gjb end button" @click="lookError">
        <image
          src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/169508959924626a6169508959924677188_look.png"
          mode="widthFix"
        />
        <text>
          {{ errorStatic ? '全部' : '只看错题' }}
        </text>
      </view>
    </bottom-utils>
  </view>
</template>
<script>
import {
  transformTime,
  getNextChapter,
  shareAppMessage2
} from '../../utils/index.js'
import questionAnswer from '../../components/makeQuestion/question-answer.vue'
import resolveAuthorizationSheet from '../../components/makeQuestion/resolve-authorization-sheet.vue'
import mixin from '../../mixin/index.js'
import chapterExerciseQuestionSwiper from '../../components/makeQuestion/chapter-exercise-question-swiper.vue'
import headHeight from '../../components/commen/head-height.vue'
import { setQuestionLists } from '../../utils/index'
import { questionHelper, isSelectedType } from '../../utils/index'
import { getQuestionsList } from '../../api/commen'
import bottomUtils from '../../components/makeQuestion/bottom-utils.vue'
export default {
  mixins: [mixin],
  components: {
    questionAnswer,
    resolveAuthorizationSheet,
    chapterExerciseQuestionSwiper,
    headHeight,
    bottomUtils
  },
  data() {
    return {
      statusBarHeight: 0,
      lists: [],
      allLists: [], // 全部
      errorStatic: false,
      total: 0,
      indicatorDots: false,
      autoplay: false,
      // 当前选定值
      current: 0,
      // 当前时间
      time: 0,
      transformTime,
      // 是否交卷
      isHadnPaper: false,
      id: '',
      isnextChapter: '1',
      params: {} // 通用参数
    }
  },
  methods: {
    back() {
      uni.navigateBack({
        delta: 1
      })
    },
    selecte(info) {
      this.lists = this.lists.map(res => {
        if (res.questionid == info.questionid) {
          return {
            ...res,
            ...info
          }
        }
        return res
      })
      this.next()
    },
    prev() {
      this.$refs.exercise.prev && this.$refs.exercise.prev()
    },
    next() {
      this.$refs.exercise.next && this.$refs.exercise.next()
    },
    lookError() {
      // 只看错题
      if (!this.errorStatic) {
        // 打开看错题状态
        let res = this.lists.find(item => {
          return (
            questionHelper.isSelected(item) && !questionHelper.diffAnswer(item)
          )
        })
        if (res) {
          this.errorStatic = true
          // 说明有做错的题目
          this.allLists = this.lists // 先存一下
          // 只取错题
          this.lists = this.lists.filter(item => {
            return (
              questionHelper.isSelected(item) &&
              !questionHelper.diffAnswer(item)
            )
          })
          this.current = 0
        } else {
          this.$xh.Toast('暂时还没有错题哦！')
        }
      } else {
        // 关闭错题状态
        this.errorStatic = false
        this.lists = this.allLists // 恢复
        this.current = 0
      }
    },
    getList() {
      getQuestionsList({
        ...this.params,
        is_look_back: '1'
      }).then(data => {
        let res = data.data.section_info
        res = res?.map(item => {
          try {
            item.user_option = JSON.parse(item.user_option)
          } catch (error) {
            console.log(error)
            // this.$xh.Toast('数据返回错误！')
            item.user_option = []
          }
          if (isSelectedType(item.type)) {
            // 选择题处理
            let map = {}
            item.user_option.forEach(u_p => {
              map[u_p.sub_question_id] = u_p.answer.map(an => an * 1)
            })
            item.stem_list = item.stem_list.map(stem => {
              stem.selected = map[stem.id] ? map[stem.id] : []
              return stem
            })
          } else {
            // 填空题处理
            let map = {}
            item.user_option.forEach(u_p => {
              map[u_p.sub_question_id] = u_p.answer.map(an => an)
            })
            item.stem_list = item.stem_list.map(stem => {
              stem.selected = map[stem.id] ? map[stem.id] : []
              return stem
            })
          }
          return item
        })
        this.lists = setQuestionLists(res)
        this.allLists = this.lists
        this.total = this.lists?.length
      })
    },
    getNextChapter() {
      getNextChapter(this.params.knowledge_id)
    }
  },
  onLoad(e) {
    this.params = e
    this.isnextChapter = e.isnextChapter || 1
    this.getList()
  },
  onShareAppMessage() {
    return shareAppMessage2()
  }
}
</script>
<style scoped lang="less">
.page-baidu {
  .priview-time {
    position: fixed;
    left: 0;
    top: 140rpx;
    width: 100vw;
    height: 96rpx;
    background-color: #fff;
    border-bottom: 1px solid #eee;
    display: flex;
    align-items: center;
    // justify-content: space-between;
    justify-content: center;
    padding: 0 40rpx;
    .nums {
      text {
        font-size: 32rpx;
        color: #000000;
      }
      text:last-child {
        color: #949494;
      }
    }
    .time {
      text {
        font-size: 32rpx;
        color: #000000;
      }
    }
    .back {
      position: absolute;
      width: 19rpx;
      height: 32rpx;
      left: 30rpx;
      top: 0;
      bottom: 0;
      margin: auto 0;
    }
    .error {
      position: absolute;
      left: 80rpx;
      top: 0;
      bottom: 0;
      margin: auto 0;
      width: 120rpx;
      height: 44rpx;
      text-align: center;
      line-height: 44rpx;
      background: #f5f5f5;
      border-radius: 8rpx;
      font-size: 24rpx;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 800;
      color: #999999;
    }
  }
  .uni-margin-wrap {
    width: calc(100vw);
    height: calc(100vh - 100rpx);
    overflow-y: scroll;
    padding-bottom: calc(80rpx + 42rpx + 36rpx);
    padding-top: 96rpx;
    box-sizing: border-box;
  }
  .utils {
    position: fixed;
    left: 0;
    bottom: 0;
    width: 100vw;
    height: 80rpx;
    padding-bottom: 42rpx;
    padding-top: 36rpx;
    background-color: #fff;
    display: flex;
    align-items: center;
    justify-content: space-around;
    height: 158rpx;
    z-index: 100;
    .gjb {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      image {
        width: 40rpx;
        height: 40rpx;
        margin-bottom: 14rpx;
      }
      text {
        font-size: 24rpx;
        color: rgba(41, 65, 90, 0.75);
      }
      .btn {
        width: 192rpx;
        height: 80rpx;
        border-radius: 40rpx;
        border: 1px solid #847cf7;
        color: #847cf7;
        font-size: 26rpx;
      }
    }
    .next {
      .btn {
        border-radius: 40rpx;
        border: 1px solid transparent;
        color: #fff;
        background: linear-gradient(270deg, #6d7dff 0%, #b3a1ff 100%);
      }
    }
  }
}
</style>
