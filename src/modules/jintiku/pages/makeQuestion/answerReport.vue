<template>
  <view class="index page-baidu">
    <view class="h">占位-请勿删除</view>
    <view class="header">答题报告</view>
    <!-- 报告区 -->
    <view class="report-content">
      <view class="report-time">
        <text>生成报告时间：</text>
        <text class="date">{{ practiceInfo.examination_time }}</text>
        <!-- <text>12:00:00</text> -->
      </view>
      <view class="report-time relaly-time">
        <text>实际用时：{{ attr.sum_time }}</text>
      </view>
      <view class="echarts">
        <view class="score">
          <canvas
            :style="{
              width: canvas.width + 'px',
              height: canvas.height + 'px'
            }"
            canvas-id="score"
            id="score"
          ></canvas>
          <image
            :src="scoreImg"
            mode="widthFix"
            :style="{
              width: canvas.width + 'px',
              height: canvas.height + 'px'
            }"
          />
        </view>
        <view class="yes-lv">
          <canvas
            :style="{
              width: canvas.width + 'px',
              height: canvas.height + 'px'
            }"
            canvas-id="rate"
            id="rate"
          ></canvas>
          <image
            :src="rateImg"
            mode="widthFix"
            :style="{
              width: canvas.width + 'px',
              height: canvas.height + 'px'
            }"
          />
        </view>
      </view>
      <view class="static">
        <view class="static-box">
          <view class="cicre q"></view>
          <view class="text">共{{ attr.totalNum }}题</view>
        </view>
        <view class="static-box">
          <view class="cicre d"></view>
          <view class="text">正确{{ attr.trueNum }}道</view>
        </view>
        <view class="static-box">
          <view class="cicre c"></view>
          <view class="text">错误{{ attr.error }}道</view>
        </view>
        <view class="static-box">
          <view class="cicre w"></view>
          <view class="text">未作答</view>
        </view>
      </view>
      <view class="line"> </view>
      <view class="title">答题卡</view>
      <view class="questions">
        <view
          class="question flex-center"
          :class="{
            done: item.user_option == '',
            success: item.user_option && item.is_error == '0',
            error: item.user_option && item.is_error == '1'
          }"
          v-for="(item, index) in list"
          :key="index"
        >
          {{ index + 1 }}
        </view>
      </view>
    </view>
    <!-- 我的 -->
    <home />
    <!-- 分享海报 -->
    <view class="share flex-center" @click="share"> 分享海报 </view>
    <!-- 按钮 -->
    <view class="btns">
      <c-button @click="back">返回列表</c-button>
      <c-button plain @click="goJX">错题解析</c-button>
    </view>
    <!-- 分享 -->
    <question-share
      v-model="questionShareShow"
      @download="download"
      @friend="friend"
      @wchat="friend"
      @baidu="friend"
    ></question-share>
    <!-- 分享图 -->
    <view class="mask" v-if="questionShareShow"></view>
    <view class="canvas-share" v-if="questionShareShow">
      <canvas
        :style="{
          width: shareinfo.width + 'px',
          height: shareinfo.height + 'px'
        }"
        canvas-id="share"
        id="share"
      ></canvas>
    </view>
  </view>
</template>
<script>
import home from '../../components/commen/home.vue'
import cButton from '../../components/commen/c-button.vue'
import questionShare from '../../components/makeQuestion/question-share.vue'
import { getnewExamineAnalysis, getnewExamineAnalysis2 } from '../../api'
export default {
  components: {
    home,
    cButton,
    questionShare
  },
  data() {
    return {
      list: [],
      // 进度条的 canvas 的宽高
      canvas: {
        width: 152,
        height: 152,
        r: 65
      },
      // 分享canvas的宽高
      shareinfo: {
        width: 335,
        // height: 540
        height: 620
      },
      scoreImg: '',
      rateImg: '',
      questionShareShow: false,
      shareCtx: null, // 分享图的上下文
      practiceInfo: {},
      attr: {},
      sharimg: ''
    }
  },
  computed: {},
  methods: {
    drawCircle(ctx, process = 0, canvas) {
      // 画圆
      // ctx.restore()
      ctx.beginPath()
      ctx.setStrokeStyle('#E5EFFF')
      ctx.setLineWidth(10)
      ctx.arc(canvas.x, canvas.y, canvas.r, 0, Math.PI * 2)
      ctx.stroke()
      // ctx.restore()

      ctx.beginPath()
      ctx.setLineWidth(10)
      ctx.setLineCap('round')
      // const grd = ctx.createLinearGradient(0, 0, 200, 0)
      // grd.addColorStop(1, '#6191FE')
      // grd.addColorStop(0, '#947BF7')
      ctx.setStrokeStyle('#6191FE')
      ctx.arc(
        canvas.x,
        canvas.y,
        canvas.r,
        -Math.PI * 0.5,
        Math.PI * ((2 / 100) * process) - Math.PI * 0.5
      )
      ctx.stroke()

      // ctx.restore()
      ctx.beginPath()
      ctx.setStrokeStyle('#E5EFFF')
      ctx.setLineWidth(2)
      ctx.arc(canvas.x, canvas.y, canvas.r - 15, 0, Math.PI * 2)
      ctx.stroke()
      // ctx.restore()
    },
    // 画分
    drawText(ctx, title, total, score, canvas, top = 20) {
      // ctx.restore()
      ctx.setTextAlign('center')
      ctx.setFontSize(18)
      ctx.setFillStyle('#787878')
      ctx.fillText(title, canvas.x, canvas.y - top)
      // ctx.restore()
      // 具体的分数值
      ctx.setTextAlign('right')
      ctx.font = 'normal bold 24px Oswald-Regular'
      ctx.setFillStyle('#6191FE')
      ctx.fillText(`${score}`, canvas.x - 10, canvas.y + 18)
      // ctx.restore()

      ctx.setTextAlign('left')
      ctx.font = 'normal bold 24px Oswald-Regular'
      ctx.setFillStyle('#23272D')
      ctx.fillText(total, canvas.x - 10, canvas.y + 18)
    },
    // 画正确率
    drawTextRate(ctx, title, score, canvas, top = 18) {
      ctx.setTextAlign('center')
      ctx.setFontSize(18)
      ctx.setFillStyle('#787878')
      ctx.font = 'normal 400 18px Oswald-Regular'
      ctx.fillText(title, canvas.x, canvas.y - 20)
      // ctx.restore()
      // 具体的分数值
      ctx.setTextAlign('center')
      ctx.font = 'normal bold 24px Oswald-Regular'
      ctx.setFillStyle('#6191FE')
      ctx.fillText(`${score}`, canvas.x - 10, canvas.y + top)
      // ctx.restore()
      ctx.setTextAlign('left')
      ctx.font = 'normal 400 16px Oswald'
      ctx.setFillStyle('#23272D')
      ctx.fillText('%', canvas.x + (score >= 100 ? 15 : 5), canvas.y + 18)
      // ctx.restore()
    },
    drawTextCommen(ctx, title, color, canvas) {
      // 画圆
      // canvas 是原点的 x 和 y
      ctx.beginPath()
      ctx.setFillStyle(color)
      ctx.arc(canvas.x, canvas.y, canvas.r, 0, Math.PI * 2)
      ctx.fill()
      ctx.setTextAlign('left')
      ctx.setTextBaseline('middle')
      ctx.setFontSize(14)
      ctx.setFillStyle('#333')
      ctx.fillText(title, canvas.x + 8, canvas.y)
    },
    setScore(score = 58) {
      const ctx = uni.createCanvasContext('score', this)
      // 100 总分 100进度
      let targetProcess = score / (100 / 100)
      // let currentProcess = 0
      // let timer = setInterval(() => {
      //   if (currentProcess >= targetProcess) {
      //     clearInterval(timer)
      //     timer = null
      //     return
      //   }
      //   this.drawCircle(ctx, currentProcess)
      //   // 最后绘制文字
      //   this.drawText(
      //     ctx,
      //     '得分',
      //     100,
      //     (100 / 100) * currentProcess,
      //     this.canvas
      //   )
      //   ctx.draw()
      //   currentProcess += 1.1
      // }, 1)
      this.drawCircle(ctx, targetProcess, {
        ...this.canvas,
        x: this.canvas.width / 2,
        y: this.canvas.height / 2
      })
      // // 最后绘制文字
      this.drawText(ctx, '得分', `/100`, score, {
        ...this.canvas,
        x: this.canvas.width / 2,
        y: this.canvas.height / 2
      })
      ctx.draw(
        setTimeout(() => {
          uni.canvasToTempFilePath({
            x: 0,
            y: 0,
            width: this.canvas.width,
            height: this.canvas.height,
            destWidth: this.canvas.width,
            destHeight: this.canvas.height,
            fileType: 'png',
            canvasId: 'score',
            success: res => {
              this.scoreImg = res.tempFilePath
            },
            fail: function (err) {
              console.log(err, '图片生成失败')
            }
          })
        }, 100)
      )
    },
    setRate(score = 8) {
      const ctx = uni.createCanvasContext('rate')
      // 100 总分 100进度
      let targetProcess = score
      // let currentProcess = 0
      // let timer = setInterval(() => {
      //   if (currentProcess >= targetProcess) {
      //     clearInterval(timer)
      //     timer = null
      //     return
      //   }
      //   this.drawCircle(ctx, currentProcess)
      //   // 最后绘制文字
      //   this.drawText(
      //     ctx,
      //     '得分',
      //     100,
      //     (100 / 100) * currentProcess,
      //     this.canvas
      //   )
      //   ctx.draw()
      //   currentProcess += 1.1
      // }, 1)
      this.drawCircle(ctx, targetProcess, {
        ...this.canvas,
        x: this.canvas.width / 2,
        y: this.canvas.height / 2
      })
      // // 最后绘制文字
      this.drawTextRate(ctx, '正确率', score, {
        ...this.canvas,
        x: this.canvas.width / 2,
        y: this.canvas.height / 2
      })
      ctx.draw(
        setTimeout(() => {
          uni.canvasToTempFilePath({
            x: 0,
            y: 0,
            width: this.canvas.width,
            height: this.canvas.height,
            destWidth: this.canvas.width,
            destHeight: this.canvas.height,
            fileType: 'png',
            canvasId: 'rate',
            success: res => {
              this.rateImg = res.tempFilePath
            },
            fail: function (err) {
              console.log(err, '图片生成失败')
            }
          })
        }, 500)
      )
    },
    // 画答题卡
    setCard(ctx, title, textColor, criColor, position = { x: 0, y: 0 }) {
      ctx.beginPath()
      ctx.setFillStyle(criColor)
      ctx.arc(position.x, position.y, 15, 0, Math.PI * 2)
      ctx.fill()
      ctx.beginPath()
      ctx.setTextAlign('center')
      ctx.setTextBaseline('middle')
      ctx.setFontSize(12)
      ctx.setFillStyle(textColor)
      ctx.fillText(`${title}`, position.x, position.y)
    },
    getShareImgUrl() {
      return new Promise((resolve, reject) => {
        uni.canvasToTempFilePath({
          x: 0,
          y: 0,
          width: this.shareinfo.width,
          height: this.shareinfo.height,
          destWidth: this.shareinfo.width,
          destHeight: this.shareinfo.height,
          fileType: 'jpg',
          canvasId: 'share',
          success: res => {
            resolve(res.tempFilePath)
          },
          fail: function (err) {
            console.log(err, '图片生成失败')
            reject(err)
          }
        })
      })
    },
    download() {
      uni.showLoading({
        title: '下载中，请稍等'
      })
      let that = this
      this.getShareImgUrl().then(url => {
        uni.saveImageToPhotosAlbum({
          filePath: url,
          success() {
            that.$xh.Toast('保存成功！')
            uni.hideLoading()
          },
          fail() {
            that.$xh.Toast('保存失败！')
            uni.hideLoading()
          }
        })
      })
    },
    friend() {
      // 分享
      this.getShareImgUrl().then(url => {
        swan.openShare({
          title: '模拟试题',
          content: this.practiceInfo.exercise_name,
          imageUrl: url,
          success() {
            console.log('分享成功')
          },
          fail() {
            console.log('分享失败')
          }
        })
      })
    },
    wchat() {},
    baidu() {},
    /** 将网络图片变成临时图片 */
    handleNetworkImgaeTransferTempImage(url) {
      return new Promise(resolve => {
        if (url.indexOf('http') === 0) {
          uni.downloadFile({
            url,
            success: res => {
              resolve(res.tempFilePath)
            }
          })
        } else {
          resolve(url)
        }
      })
    },
    async setShare() {
      // 设置分享
      const ctx = uni.createCanvasContext('share')
      ctx.beginPath()
      ctx.setFillStyle('#fff')
      ctx.fillRect(0, 0, this.shareinfo.width, this.shareinfo.height)
      let padding = 20 // canvas的padding
      function setText(
        ctx,
        date = '2022.06.08',
        // time = '12:00:00',
        relalyTime = '1小时1分钟',
        score = 60,
        rate = 0
      ) {
        ctx.setTextAlign('left')
        ctx.setTextBaseline('top')
        ctx.setFontSize(16)
        ctx.setFillStyle('#000')
        // ctx.font = 'normal bold 20px cursive'
        let t1 = ctx.measureText('生成报告时间:').width
        ctx.fillText('生成报告时间:', padding, padding)
        ctx.fillText(date, padding + 102 + 8, padding)
        let t11 = ctx.measureText(date).width
        // ctx.fillText(time, padding + 112 + 80 + 12, padding)
        ctx.fillText('实际用时:', padding, padding + 30)
        let t2 = ctx.measureText('实际用时:').width
        ctx.fillText(`${relalyTime}`, padding + 62 + 8, padding + 30)
        // 画圆 - 左侧
        let targetProcess = score / (100 / 100)
        let cx = this.shareinfo.width / 4
        let cy = this.shareinfo.height / 4 + 20
        this.drawCircle(ctx, targetProcess, {
          x: cx,
          y: cy,
          r: 60
        })
        // 写字
        this.drawText(
          ctx,
          '得分',
          `/100`,
          score,
          {
            x: cx,
            y: cy - 20,
            r: 60
          },
          10
        )
        // 画圆 - 右侧
        this.drawCircle(ctx, targetProcess, {
          x: cx * 3,
          y: cy,
          r: 60
        })
        this.drawTextRate(
          ctx,
          '正确率',
          rate,
          {
            x: cx * 3,
            y: cy - 10
          },
          12
        )
      }
      // 设置题目统计题数
      function setQuestionStatic(ctx, total, success, error, not) {
        let y = 250
        let d = 85
        this.drawTextCommen(ctx, `共${total}道`, '#FFB41A', {
          x: padding,
          y: y,
          r: 5
        })
        this.drawTextCommen(ctx, `正确${success}道`, '#847CF7', {
          x: padding + d,
          y: y,
          r: 5
        })
        this.drawTextCommen(ctx, `错误${total}道`, '#F76C5D', {
          x: padding + d * 2,
          y: y,
          r: 5
        })
        this.drawTextCommen(ctx, `未作答`, '#E4E4E4', {
          x: padding + d * 3,
          y: y,
          r: 5
        })
      }
      // 画横线 和 标题
      function setLine(ctx) {
        ctx.beginPath()
        let y = 280
        ctx.moveTo(padding, y)
        ctx.lineTo(this.shareinfo.width - padding, y)
        ctx.setStrokeStyle('#E4E4E4')
        ctx.stroke()
        ctx.beginPath()
        ctx.setTextAlign('left')
        ctx.setTextBaseline('middle')
        ctx.font = 'normal bold 20px Oswald-Regular'
        ctx.setFillStyle('#000')
        ctx.fillText('答题卡', padding, y + 30)
      }
      // 画答题卡
      function setQuestionCard(ctx) {
        let sx = padding + 10
        let sy = 350
        // let ques = [
        //   {
        //     type: 'success'
        //   },
        //   {
        //     type: 'error'
        //   },
        //   {
        //     type: 'not'
        //   }
        // ]
        let ques = this.list.map(item => {
          if (!item.user_option) {
            return {
              type: 'not'
            }
          }
          if (item.user_option && item.is_error == '0') {
            return {
              type: 'success'
            }
          }
          if (item.user_option && item.is_error == '1') {
            return {
              type: 'error'
            }
          }
        })
        if (ques.length > 20) {
          ques = ques.slice(0, 20)
        }
        for (let i = 0; i < ques.length; i++) {
          let textcolor = ''
          let bgcolor = ''
          if (ques[i].type == 'error') {
            textcolor = '#F7695A'
            bgcolor = '#FFF3F2'
          }
          if (ques[i].type == 'success') {
            textcolor = '#8179F7'
            bgcolor = '#E9E8FD'
          }
          if (ques[i].type == 'not') {
            textcolor = '#000'
            bgcolor = '#F6F6F6'
          }
          this.setCard(ctx, i + 1, textcolor, bgcolor, {
            x: sx + 67 * (i % 5),
            y: sy + 50 * parseInt(i / 5)
          })
        }
      }
      setText.call(
        this,
        ctx,
        this.practiceInfo.examination_time,
        this.attr.sum_time,
        Math.ceil((100 / this.attr.totalNum) * this.attr.trueNum),
        this.attr.correct_rate
      )
      setQuestionStatic.call(
        this,
        ctx,
        this.attr.totalNum,
        this.attr.trueNum,
        this.attr.error,
        '未作答'
      )
      setLine.call(this, ctx)
      setQuestionCard.call(this, ctx)
      // 绘制二维码
      ctx.drawImage(
        await this.handleNetworkImgaeTransferTempImage(
          'https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/16606441201641e20166064412016443231_ER.png'
        ),
        this.shareinfo.width / 2 - 40,
        this.shareinfo.height - 95,
        80,
        80
      )
      ctx.draw()
      return ctx
    },
    share() {
      this.questionShareShow = true
      setTimeout(() => {
        this.shareCtx = this.setShare()
        this.getShareImgUrl().then(url => {
          this.sharimg = url
        })
      }, 0)
    },
    back() {
      uni.navigateBack({
        delta: 1
      })
    },
    goJX() {
      // 去解析页
      this.$xh.push('jintiku_baidu', 'pages/makeQuestion/errorQuestion')
    }
  },
  onShow() {},
  onLoad(e) {
    if (e.mokao) {
      getnewExamineAnalysis2({
        user_id: this.$store.state.jintiku_baidu.user_id,
        id: e.practice_record_id,
        // id: 31076094,
        is_mini_apps: 1,
        dailyId: 0
      }).then(data => {
        this.practiceInfo = data.entity.practiceInfo
        this.attr = data.entity.attr
        this.list = data.entity.dos
        this.setScore(Math.ceil((100 / this.attr.totalNum) * this.attr.trueNum))
        this.setRate(this.attr.correct_rate)
      })
    } else {
      getnewExamineAnalysis({
        user_id: this.$store.state.jintiku_baidu.user_id,
        id: e.practice_record_id,
        // id: 31076094,
        is_mini_apps: 1,
        dailyId: 0
      }).then(data => {
        this.practiceInfo = data.entity.practiceInfo
        this.attr = data.entity.attr
        this.list = data.entity.dos
        this.setScore(Math.ceil((100 / this.attr.totalNum) * this.attr.trueNum))
        this.setRate(this.attr.correct_rate)
      })
    }
  },
  onShareAppMessage() {
    return this.$xh.shareAppMessage()
  }
}
</script>
<style scoped lang="less">
.index {
  height: 100vh;
  background: url('../../static/imgs/report-bg.png') no-repeat;
  background-size: contain;
  overflow-y: hidden;
  display: flex;
  flex-direction: column;
  .h {
    height: --status-bar-height;
    opacity: 0;
  }
  .header {
    height: 66rpx;
    line-height: 66rpx;
    margin-top: 26rpx;
    color: #fff;
    font-weight: 800;
    text-align: center;
    margin-bottom: 30rpx;
  }
  .report-content {
    background-color: #fff;
    background: #ffffff;
    border-top-left-radius: 36rpx;
    border-top-right-radius: 36rpx;
    flex: 1;
    overflow-y: auto;
    padding: 50rpx 40rpx 184rpx;
    .report-time {
      font-size: 30rpx;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #23272d;
      line-height: 42rpx;
      margin-bottom: 30rpx;
      .date {
        margin-right: 10rpx;
      }
    }
    .echarts {
      display: flex;
      justify-content: space-between;
      canvas {
        position: fixed;
        left: -100%;
        top: -100%;
      }
    }
    .static {
      display: flex;
      margin-bottom: 40rpx;
      margin-top: 60rpx;
      width: 100%;
      align-items: center;
      justify-content: space-between;
      .static-box {
        display: flex;
        align-items: center;

        .cicre {
          width: 16rpx;
          height: 16rpx;
          background-color: #ffb41a;
          border-radius: 50%;
          margin-right: 12rpx;
        }
        .d {
          background-color: #847cf7;
        }
        .c {
          background-color: #f76c5d;
        }
        .w {
          background-color: #e4e4e4;
        }
        .text {
          font-size: 28rpx;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #333333;
        }
      }
    }
    .line {
      width: 100%;
      height: 1px;
      width: 100%;
      background: #e9e9e9;
      margin-bottom: 44rpx;
    }
    .title {
      height: 50rpx;
      font-size: 36rpx;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #23272d;
      line-height: 50rpx;
      margin-bottom: 44rpx;
    }
    .questions {
      // padding: 60rpx 40rpx 50rpx;
      padding-bottom: 0;
      display: flex;
      flex-wrap: wrap;
      align-items: center;
      .question {
        width: 80rpx;
        height: 80rpx;
        font-size: 32rpx;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 600;
        color: #000000;
        border-radius: 50%;
        background-color: #f6f6f6;
        margin-right: 64rpx;
        margin-bottom: 44rpx;
        &:nth-child(5n) {
          margin-right: 0;
        }
      }
      .question.success {
        background: rgba(132, 124, 247, 0.18);
        color: #847cf7;
      }
      .question.error {
        background: #fff3f2;
        color: #f76c5d;
      }
    }
  }
  .btns {
    position: fixed;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 184rpx;
    display: flex;
    background-color: #fff;
    justify-content: space-between;
    padding: 0 40rpx;
    padding-top: 36rpx;
  }
  .share {
    width: 198rpx;
    height: 76rpx;
    background-color: #b57cf5;
    color: #fff;
    font-size: 34rpx;
    border-top-left-radius: 50rpx;
    border-bottom-left-radius: 50rpx;
    position: fixed;
    right: 0;
    top: 250rpx;
  }
}
.canvas-share {
  position: fixed;
  width: 335px;
  height: 540px;
  background-color: #fff;
  left: 0;
  right: 0;
  top: 92rpx;
  margin: 0 auto;
  z-index: 600;
  pointer-events: none;
  transition: all 0.25s;
  padding: 0;
  canvas {
    background-color: #fff;
  }
}
.mask {
  position: fixed;
  left: 0;
  top: 0;
  margin: auto;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
}
.op0 {
  opacity: 0;
}
</style>
