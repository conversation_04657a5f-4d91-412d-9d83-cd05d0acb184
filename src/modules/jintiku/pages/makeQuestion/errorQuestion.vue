<template>
  <view class="page-baidu">
    <!-- 顶部功能预览 -->
    <view class="priview-time">
      <view class="nums">
        <text>{{ current + 1 }}</text>
        <text>/{{ total }}</text>
      </view>
      <view class="time" v-if="false">{{ transformTime(time) }}</view>
    </view>
    <view class="uni-margin-wrap">
      <swiper
        class="swiper"
        :indicator-dots="indicatorDots"
        :autoplay="autoplay"
        :current="current"
        :duration="300"
        :circular="false"
        @change="swiperChange"
      >
        <swiper-item v-for="(item, index) in lists" :key="index">
          <view
            class="swiper-item"
            v-if="
              index == current || index == current - 1 || index == current + 1
            "
          >
            <view class="select-question-box">
              <select-question
                :info="item"
                :answer="true"
                @selecte="selecte"
              ></select-question>
            </view>
            <view class="question-answer-box">
              <!-- 名师解析 -->
              <question-answer :info="item"></question-answer>
            </view>
          </view>
        </swiper-item>
      </swiper>
    </view>
    <view class="utils">
      <view class="gjb dtk" @click="sheetShow = true">
        <image
          src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/16950157902962fab169501579029776102_dtk.png"
          mode="widthFix"
        />
        <text>答题卡</text>
      </view>
      <view class="gjb end" @click="showModelfn">
        <template v-if="isAll">
          <image
            src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/169501588410064fa169501588410065556_look-error.png"
            mode="widthFix"
          />
          <text>只看错题</text>
        </template>
        <template v-else>
          <image
            :src="require('../../static/imgs/look-all.png')"
            mode="widthFix"
          />
          <text>查看全部</text>
        </template>
      </view>
      <view class="gjb pre">
        <view class="btn button flex-center" @click="prev">上一题</view>
      </view>
      <view class="gjb next">
        <view class="btn button flex-center" @click="next">下一题</view>
      </view>
    </view>
    <answer-sheet v-model="sheetShow" :lists="lists" @change="change" />
  </view>
</template>
<script>
import { transformTime } from '../../utils/index.js'
import selectQuestion from '../../components/makeQuestion/select-question.vue'
import questionAnswer from '../../components/makeQuestion/question-answer.vue'
import answerSheet from '../../components/makeQuestion/answer-sheet-analysis.vue'
// import { isLogin } from '../../utils/index.js'
export default {
  components: {
    answerSheet,
    selectQuestion,
    questionAnswer
  },
  data() {
    return {
      lists: [],
      total: 0,
      indicatorDots: false,
      autoplay: false,
      // 当前选定值
      current: 0,
      // 当前时间
      time: 0,
      transformTime,
      // 显示答题卡
      sheetShow: false,
      // 是否是全部的题目
      isAll: true,
      backlists: []
    }
  },
  methods: {
    change(e) {
      this.current = e
    },
    setTime() {
      setTimeout(() => {
        this.time++
        this.setTime()
      }, 1000)
    },
    swiperChange(e) {
      this.current = e.detail.current
    },
    prev() {
      if (this.current <= 0) {
        this.$xh.Toast('已经是第一题了哦！')
        return
      }
      this.current--
    },
    next() {
      if (this.current >= this.lists.length - 1) {
        this.$xh.Toast('已经是最后一题了哦！')
        return
      }
      this.current++
    },
    showModelfn() {
      // 计算还有多少题没有答
      // 只看错题
      this.isAll = !this.isAll
      if (this.isAll) {
        // 全部
        this.lists = JSON.parse(JSON.stringify(this.backlists))
        this.current = 0
        this.total = this.lists.length
      } else {
        // 只看错题
        let errorList = this.backlists.filter(item => {
          return item.user_option != item.questionanswer
        })
        if (errorList.length == 0) {
          this.$xh.Toast('恭喜您，全部答对了！')
          return
        }
        this.lists = errorList
        this.total = errorList.length
        this.current = 0
      }
    }
  },
  onLoad(e) {
    try {
      this.lists = JSON.parse(uni.getStorageSync('_q_lists_'))
      this.backlists = JSON.parse(uni.getStorageSync('_q_lists_'))
      this.total = this.lists.length
    } catch (e) {}
  },
  onUnload() {},
  onShow() {},
  onShareAppMessage() {
    return this.$xh.shareAppMessage()
  }
}
</script>
<style scoped lang="less">
.page-baidu {
  .uni-margin-wrap {
    width: 100vw;
    height: 100vh;
    padding-bottom: calc(80rpx + 42rpx + 36rpx);
    padding-top: 96rpx;
    box-sizing: border-box;

    .swiper {
      height: 100%;
      .swiper-item {
        overflow-y: auto;
        padding: 48rpx 38rpx;
        height: 100%;
        background-color: #fff;
        .question-answer-box {
          margin-top: 86rpx;
        }
      }
    }
  }
  .priview-time {
    position: fixed;
    left: 0;
    top: 0;
    width: 100vw;
    height: 96rpx;
    background-color: #f2f5f7;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 40rpx;
    .nums {
      text {
        font-size: 32rpx;
        color: #000000;
      }
      text:last-child {
        color: #949494;
      }
    }
    .time {
      text {
        font-size: 32rpx;
        color: #000000;
      }
    }
  }
  .utils {
    position: fixed;
    left: 0;
    bottom: 0;
    width: 100vw;
    height: 80rpx;
    padding-bottom: 42rpx;
    padding-top: 36rpx;
    background-color: #fff;
    display: flex;
    align-items: center;
    justify-content: space-around;
    height: 158rpx;
    .gjb {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      image {
        width: 40rpx;
        height: 40rpx;
        margin-bottom: 14rpx;
      }
      text {
        font-size: 24rpx;
        color: rgba(41, 65, 90, 0.75);
      }
      .btn {
        width: 192rpx;
        height: 80rpx;
        border-radius: 40rpx;
        border: 1px solid #847cf7;
        color: #847cf7;
        font-size: 26rpx;
      }
    }
    .next {
      .btn {
        border-radius: 40rpx;
        border: 1px solid transparent;
        color: #fff;
        background: linear-gradient(270deg, #6d7dff 0%, #b3a1ff 100%);
      }
    }
  }
}
</style>
