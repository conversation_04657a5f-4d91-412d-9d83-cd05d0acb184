<template>
  <view class="chapterExercise">
    <view class="title"> 本题库共有{{ total }}道题</view>
    <view class="container">
      <view class="lists" v-for="(jtem, x) in lists" :key="x">
        <view class="big-title" v-if="jtem.child && jtem.child.length">
          {{ jtem.sectionname }}
        </view>
        <view
          class="list"
          v-for="(item, i) in jtem.child"
          :key="i"
          :class="{ active: item.isExtend }"
        >
          <view class="head" @click="extend(item)">
            <view class="left">
              <view class="image">
                <image
                  src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/16950043427848e3c169500434278459705_select.png"
                  mode="widthFix"
                />
              </view>
              <text class="tit">{{ item.sectionname }}</text>
              <!-- <text class="cont">口腔组织病理学</text> -->
            </view>
            <view class="right">
              <text>{{ item.do_question_num }}</text>
              <text>/{{ item.question_number }}</text>
            </view>
          </view>
          <view class="contents">
            <view
              class="content"
              @click="goDetail(res)"
              v-for="(res, j) in item.child"
              :key="j"
            >
              <view class="content-head">
                <view class="content-left">
                  <view class="circle"></view>
                  <text class="unit">{{ res.sectionname }}</text>
                  <!-- <text class="desc-title">牙体解剖生理</text> -->
                </view>
                <image
                  src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/16950046792662774169500467926615379_edit.png"
                  mode="widthFix"
                  class="edit"
                  v-if="!res.isPreCurrent"
                />
                <text class="pre-picter" v-else> 上次练习 </text>
              </view>
              <view class="content-static">
                <view class="static">
                  做对 {{ res.correct_question_num }}
                </view>
                <view class="static"> 做错 {{ res.error_question_num }} </view>
                <view class="static">
                  未答 {{ res.not_answered_question_num }}
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
    <view class="preData box-show" v-if="preData.id">
      <view class="leftinfo"> 上次练习：{{ preData.sectionname }} </view>
      <view class="button flex-center" @click="goContinue(preData)">
        继续练习
      </view>
    </view>
  </view>
</template>
<script>
import { getChapterlist } from '../../api/chapter'
export default {
  data() {
    return {
      lists: [],
      total: 0,
      preData: {} // 上次练习位置
    }
  },
  onShow() {
    this.getList()
  },
  onLoad(e) {
    this.total = e.total
  },
  methods: {
    setDataInfo(list) {
      return list.map(item => {
        item.child &&
          item.child.map(res => {
            res.child &&
              res.child.map(result => {
                result.isPreCurrent = false
                return result
              })
            return res
          })
        return item
      })
    },
    getList() {
      getChapterlist().then(data => {
        if (this.lists.length) {
          return
        }
        this.lists = data.data.section_info.map(item => {
          item.child &&
            item.child.map(res => {
              res.isExtend = false
              res.child &&
                res.child.map(result => {
                  result.isPreCurrent = result.is_checked == '1' ? true : false
                  if (result.isPreCurrent) {
                    // 如果是上次练习
                    res.isExtend = true
                    this.preData = result
                  }
                  return result
                })
              return res
            })
          return item
        })
      })
    },
    extend(item) {
      item.isExtend = !item.isExtend
    },
    goDetail(item) {
      if (item.not_answered_question_num == '0') {
        // 跳转看题
        this.$xh.push(
          'jintiku',
          `pages/makeQuestion/lookAnalysisQuestion?knowledge_id=${item.id}&type=1&chapter_id=${item.sectionprent}`
        )
        return
      }
      // 先把其他消除
      this.setDataInfo(this.lists)
      item.isPreCurrent = true
      this.$xh.push(
        'jintiku',
        `pages/makeQuestion/makeQuestion?knowledge_id=${item.id}&type=1&chapter_id=${item.sectionprent}`
      )
    },
    goContinue(item) {
      this.$xh.push(
        'jintiku',
        `pages/makeQuestion/makeQuestion?knowledge_id=${item.id}&type=1&chapter_id=${item.sectionprent}`
      )
    }
  },
  onShareAppMessage() {
    return this.$xh.shareAppMessage()
  }
}
</script>
<style scoped lang="less">
.chapterExercise {
  height: 100vh;
  overflow-y: auto;
  background-color: #fff;
  padding-bottom: 100rpx;
  .big-title {
    color: #ccc;
    font-size: 28rpx;
    margin-top: 20rpx;
  }
  .title {
    height: 80rpx;
    line-height: 80rpx;
    background: #f5f5f5;
    font-size: 28rpx;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #666666;
    padding-left: 30rpx;
  }
  .container {
    padding: 0 32rpx;
    .lists {
      .list {
        .head {
          height: 124rpx;
          display: flex;
          justify-content: space-between;
          align-items: center;
          border-bottom: 1rpx solid #f0f0f0;
          .left {
            display: flex;
            align-items: center;
            image {
              width: 32rpx;
              height: 32rpx;
              margin-right: 24rpx;
              transition: all 0.25s;
            }
            text {
              font-size: 28rpx;
              font-family: PingFangSC-Medium, PingFang SC;
              font-weight: 800;
              color: #333333;
            }
            .tit {
              margin-right: 4rpx;
            }
          }
          .right {
            font-size: 24rpx;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #999999;
            text:first-child {
              color: #387dfc;
            }
          }
        }
        .contents {
          // padding: 48rpx 0;
          height: 0;
          overflow: hidden;
          padding: 0;
          transition: all 0.25s;
          .content {
            margin: 0 16rpx;
            .content-head {
              display: flex;
              align-items: center;
              justify-content: space-between;
              .content-left {
                display: flex;
                align-items: center;
                .circle {
                  width: 20rpx;
                  height: 20rpx;
                  margin-right: 30rpx;
                  background: #387dfc;
                  border-radius: 50%;
                  margin-left: -8rpx;
                }
                .unit {
                  font-size: 26rpx;
                  font-family: PingFangSC-Regular, PingFang SC;
                  font-weight: 400;
                  color: #333333;
                  margin-right: 4rpx;
                }
                .desc-title {
                  font-size: 26rpx;
                  font-family: PingFangSC-Regular, PingFang SC;
                  font-weight: 400;
                  color: #333333;
                }
              }
              .edit {
                width: 24rpx;
                height: 24rpx;
              }
              .pre-picter {
                color: #387dfc;
                font-size: 24rpx;
              }
            }
            .content-static {
              height: 94rpx;
              border-left: 2rpx dashed #387dfc;
              padding-top: 20rpx;
              padding-left: 40rpx;
              display: flex;

              .static {
                margin-right: 80rpx;
                font-size: 24rpx;
                font-family: PingFangSC-Regular, PingFang SC;
                font-weight: 400;
                color: #999999;
              }
            }
          }
        }
      }
      .list:last-child {
        border-bottom: 0 none;
      }
      .active {
        .head {
          .left {
            image {
              transform: rotateZ(180deg);
            }
          }
        }
        .contents {
          padding: 48rpx 0;
          overflow: visible;
          height: auto;
          border-bottom: 1rpx solid #f0f0f0;
        }
      }
    }
  }
  .preData {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 40rpx;
    margin: 0 auto;
    height: 70rpx;
    width: 90%;
    border-radius: 10rpx;
    background-color: #fff;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20rpx;
    .leftinfo {
      font-size: 24rpx;
      color: #333;
    }
    .button {
      width: 120rpx;
      height: 40rpx;
      background-color: #387dfc;
      color: #fff;
      font-size: 20rpx;
      border-radius: 10rpx;
    }
  }
}
</style>
