<template>
  <view class="detail-page">
    <view class="header">
      <view class="header-b">
        <image class="img"></image>
        <view class="info">
          <view class="name">2025临床执业技能全阶2025临床执业技能全阶</view>
          <view class="tag">精准押题</view>
        </view>
      </view>
    </view>
    <view class="list">
      <view class="item" v-for="item of 10">
        <view>
          <view class="name">临床技能一站病例分析</view>
          <view class="pro">正确率:0%</view>
        </view>
        <view class="push">
          <view>296题</view>
          <image
            src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/1669357686810b313166935768681084385_right.png"
          />
        </view>
      </view>
      <view style="height: 40px"></view>
    </view>
  </view>
</template>

<script>
export default {
  onShareAppMessage() {
    return this.$xh.shareAppMessage()
  }
}
</script>

<style lang="scss" scoped>
.list {
  padding-top: 40rpx;
  .item {
    background-color: #fff;
    padding: 24rpx;
    margin: 24rpx;
    margin-bottom: 20rpx;
    border-radius: 16rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .name {
      font-size: 32rpx;
      font-weight: 400;
      color: #03203d;
      margin-bottom: 16rpx;
    }
    .pro {
      font-size: 24rpx;
      font-weight: 400;
      color: rgba($color: #03203d, $alpha: 0.65);
    }
    .push {
      display: flex;

      view {
        font-size: 32rpx;
        font-weight: 400;
        color: rgba($color: #03203d, $alpha: 0.85);
      }
      image {
        width: 40rpx;
        height: 40rpx;
      }
    }
  }
}
.detail-page {
  height: 100vh;
  width: 100vw;
  background-color: #f2f6f7;
}
.header {
  // align-items: flex-end;
  background-color: #4a3927;
  padding-top: 60px;
  height: 328rpx;
  .header-b {
    display: flex;
    position: relative;
    bottom: -40rpx;
  }
  .img {
    width: 200rpx;
    height: 200rpx;
    background-color: #fff;
    margin-left: 30px;
    margin-right: 24rpx;
    flex-shrink: 0;
  }
  .info {
    .name {
      font-size: 38rpx;
      color: #fff;
    }
    .tag {
      margin-top: 20rpx;
      font-size: 24rpx;
      color: #fff;
    }
  }
}
</style>
