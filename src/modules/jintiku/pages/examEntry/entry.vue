<template>
  <view class="page-baidu">
    <head-height :statusBarHeight.sync="statusBarHeight" />
    <!-- 顶部功能预览 -->
    <view
      class="priview-time"
      :style="{ top: statusBarHeight + 'px' }"
      style="z-index: 11"
    >
      <img
        src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/169509193147644ed169509193147782088_back.png"
        mode="widthFix"
        class="back"
        @click="back"
      />
      <view class="collect" @click="onCollectClick">
        <img
          src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/16975981429418820169759814294171084_login_sch%E5%A4%87%E4%BB%BD%402x.png"
          alt=""
          v-if="lists[current].collect_status == 2"
        />
        <img
          src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/16952764211895063169527642118928281_login_sch%E5%A4%87%E4%BB%BD%402x.png"
          alt=""
          v-else
        />
      </view>
      <view class="nums">
        <text>{{ lists.length ? current + 1 : 0 }}</text>
        <text>/{{ lists.length }}</text>
      </view>
    </view>
    <swiper
      class="swiper"
      :indicator-dots="false"
      :autoplay="false"
      :current="current"
      :duration="300"
      :circular="false"
      @change="swiperChange"
    >
      <swiper-item v-for="(item, index) in lists" :key="index">
        <view class="topic"> 考点：{{ item.name }} </view>
        <view class="explain">
          <view
            class="explain_content"
            v-html="formatting(item.content, item.memoryType)"
          />
        </view>
        <view class="my_data" v-if="item.memoryType == 2">
          <view class="content">
            <view class="title">我的默写</view>
            <view
              class="my_content"
              :class="{ no_data: item.myData ? false : true }"
              >{{ item.myData ? item.myData : '您未作答' }}</view
            >
          </view>
        </view>
        <view
          class="foot_button"
          :class="{ active: item.isShow || item.memoryType == 2 }"
          v-if="item.memoryType != 1"
        >
          <view
            class="button show_button"
            @click="handleMemoryClick(index)"
            v-if="item.memoryType == 0"
          >
            默写
          </view>
          <view
            class="button show_button not_remember"
            @click="handleNoMemoryClick(item, index)"
            v-else
          >
            没记住
          </view>
          <view
            class="button"
            @click="showButton(item)"
            style="margin: 0 30rpx"
            v-if="item.memoryType == 0"
          >
            默记
          </view>
          <view
            class="button"
            @click="handleMemoryClick(index)"
            style="margin: 0 30rpx"
            v-else
          >
            默写
          </view>
          <view class="button show_button" @click="next(item)">记住了</view>
        </view>
        <view class="foot_button waive" style="justify-content: start" v-else>
          <view class="waive_button" @click="handelWaiveClick(item)">放弃</view>
          <view class="memory_button" @click="handleMemoryClick(index)">
            默写
          </view>
          <view class="hint">请默写标红关键字</view>
        </view>
      </swiper-item>
    </swiper>
    <write-from-memory
      v-show="isMemoryShow"
      @close="handleCloseClick($event)"
    />
  </view>
</template>
<script>
import headHeight from '../../components/commen/head-height.vue'
import writeFromMemory from '../../components/makeQuestion/write-from-memory'
import {
  getTestpointknackquestion,
  memorization,
  testingcentreCollect
} from '../../api/examEntry'
export default {
  components: {
    headHeight,
    writeFromMemory
  },
  data() {
    return {
      statusBarHeight: 0,
      current: 0,
      lists: [],
      isMemoryShow: false,
      itemIndex: '',
      label: '',
      pid: ''
    }
  },
  onLoad(e) {
    this.label = e.label
    this.pid = e.pid
    this.init()
  },

  methods: {
    init() {
      this.searchFn()
    },
    searchFn() {
      getTestpointknackquestion({ pid: this.pid, label: this.label }).then(
        res => {
          this.lists = res.data.question.map(item => {
            return {
              ...item,
              memoryType: '0',
              isShow: false
            }
          })
        }
      )
    },
    swiperChange(e) {
      this.current = e.detail.current
    },
    showButton(item) {
      item.isShow = true
      this.memoryType = 0
    },
    handleCloseClick(e) {
      this.isMemoryShow = false
      this.lists[this.itemIndex].memoryType = e.type
      this.lists[this.itemIndex].myData = e.data
    },
    handleMemoryClick(index) {
      this.isMemoryShow = true
      this.itemIndex = index
    },
    handleNoMemoryClick(item, index) {
      let data = {
        action_type: '2',
        testingcentre_id: item.id,
        testingcentre_pid: item.pid
      }
      memorization(data).then(res => {
        item.memoryType = 0
        this.$xh.Toast('操作成功')
      })
    },
    handelWaiveClick(item) {
      item.memoryType = 0
    },
    formatting(data, type) {
      if (data) {
        if (type == 0 || type == 2) {
          return data
            .replace(/\[/gi, '<span style="color:red">')
            .replace(/\]/gi, '</span> ')
            .replace(/\&/gi, '<br/>')
        } else {
          return data
            .replace(/ *\[[^)]*\] */g, '______')
            .replace(/\&/gi, '<br/>')
        }
      }
    },
    back() {
      uni.navigateBack({
        delta: 1
      })
    },
    next(item) {
      let data = {
        action_type: '1',
        testingcentre_id: item.id,
        testingcentre_pid: item.pid
      }
      memorization(data).then(res => {
        this.$xh.Toast('操作成功')
        // if (this.lists.length > this.current + 1) {
        //   this.current++
        // } else {
        //   this.$xh.Toast('已经是最后一题了哦！')
        // }
      })
    },
    onCollectClick() {
      let data = {
        action_type: this.lists[this.current].collect_status == '1' ? '2' : '1',
        testingcentre_id: this.lists[this.current].id,
        testingcentre_pid: this.lists[this.current].pid
      }
      testingcentreCollect(data).then(res => {
        this.$xh.Toast('操作成功')
        this.lists[this.current].collect_status =
          this.lists[this.current].collect_status == '1' ? '2' : '1'
      })
    }
  },
  onShareAppMessage() {
    return this.$xh.shareAppMessage()
  }
}
</script>
<style scoped lang="less">
.page-baidu {
  .priview-time {
    position: fixed;
    left: 0;
    // top: 140rpx;
    width: 100vw;
    height: 96rpx;
    background-color: #fff;
    border-bottom: 1px solid #eee;
    display: flex;
    align-items: center;
    // justify-content: space-between;
    justify-content: center;
    padding: 0 40rpx;
    .nums {
      text {
        font-size: 32rpx;
        color: #000000;
      }
      text:last-child {
        color: #949494;
      }
    }
    .time {
      text {
        font-size: 32rpx;
        color: #000000;
      }
    }
    .back {
      position: absolute;
      width: 19rpx;
      height: 32rpx;
      left: 30rpx;
      top: 0;
      bottom: 0;
      margin: auto 0;
    }
    .collect {
      width: 40rpx;
      height: 40rpx;
      position: absolute;
      left: 80rpx;
      top: 25rpx;
      bottom: 0;
      img {
        width: 100%;
        height: 100%;
      }
    }
  }
  .swiper {
    height: calc(100% - 127rpx);
    .topic {
      width: 100%;
      padding: 26rpx 30rpx;
      background: #f5f5f5;
      color: #666666;
      font-size: 28rpx;
      margin-top: calc(96rpx);
      overflow-y: auto;
    }
    .explain {
      padding: 20rpx 30rpx;
      // margin: 20rpx 30rpx;
      // width: 100%;
      // min-height: 620rpx;
      // background: #f5f5f5;
      .explain_content {
        background: #f5f5f5;
        width: 100%;
        height: 620rpx;
        border-radius: 16rpx;
        padding: 32rpx 16rpx;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #333333;
        line-height: 28rpx;
        font-size: 28rpx;
        overflow: hidden;
        overflow-y: visible;
      }
      .redText {
        span {
          color: red;
        }
      }
    }
    .my_data {
      padding: 20rpx 32rpx;
      .content {
        font-size: 28rpx;
        color: #333333;
        width: auto;
        min-height: 168rpx;
        border-radius: 16rpx;
        background: #f5f5f5;
        padding: 32rpx 16rpx;
      }
      .my_content {
        margin-top: 20rpx;
      }
      .no_data {
        color: #e02020;
      }
    }
    .foot_button {
      position: fixed;
      bottom: 40rpx;
      width: 100%;
      padding: 0 24rpx;
      display: flex;
      justify-content: center;
      align-items: center;
      .show_button {
        display: none;
        transition: all 0.25s;
      }
      .button {
        width: 214rpx;
        height: 68rpx;
        text-align: center;
        border-radius: 32rpx;
        line-height: 68rpx;
        font-size: 24rpx;
        border: 1rpx solid #2e68ff;
        background: #ebf1ff;
        color: #2e68ff;
      }
      .not_remember {
        border: 1rpx solid #e02020 !important;
        color: #e02020 !important;
      }
    }
    .waive {
      .waive_button {
        width: 214rpx;
        height: 68rpx;
        background: #2e68ff;
        text-align: center;
        line-height: 68rpx;
        color: #ffffff;
        font-size: 24rpx;
        border-radius: 32rpx;
      }
      .memory_button {
        width: 76rpx;
        height: 36rpx;
        background: #fbe8e8;
        font-size: 20rpx;
        border-radius: 18rpx;
        text-align: center;
        line-height: 36rpx;
        color: #e02020;
        margin-left: 16rpx;
      }
      .hint {
        color: #666666;
        font-size: 28rpx;
        margin-left: 32rpx;
      }
    }
    .active {
      .show_button {
        display: inline-block;
        background: #f6f7f8;
        color: #03203d;
        border: 0;
      }
    }
  }
}
</style>
