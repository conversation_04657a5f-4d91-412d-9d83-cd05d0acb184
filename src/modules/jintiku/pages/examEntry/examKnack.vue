<template>
  <view class="exam_knack">
    <view class="header_statistics">
      <view class="statistics">
        <view
          class="details"
          :class="{ init: current == 0 }"
          @click="handleTabClick(0)"
        >
          <view class="title">未记住</view>
          <view class="number"
            >{{ learn_exam_site_count.not_learn_exam_site_count }}条</view
          >
        </view>
        <view class="division"></view>
        <view
          class="details"
          :class="{ init: current == 1 }"
          @click="handleTabClick(1)"
        >
          <view class="title">已记住</view>
          <view class="number"
            >{{ learn_exam_site_count.learn_exam_site_count }}条</view
          >
        </view>
        <view class="division"></view>
        <view
          class="details"
          :class="{ init: current == 2 }"
          @click="handleTabClick(2)"
        >
          <view class="title">全部</view>
          <view class="number"
            >{{ learn_exam_site_count.all_exam_site_count }}条</view
          >
        </view>
      </view>
    </view>
    <swiper
      class="swiper"
      :indicator-dots="false"
      :autoplay="false"
      :current="current"
      :duration="300"
      :circular="false"
      :disable-touch="false"
      :skip-hidden-item-layout="false"
      @change="swiperChange"
      :style="{ height: swiperHeight * 2 + 'rpx' }"
    >
      <swiper-item v-for="(item, index) in lists" :key="index">
        <view :id="'height_calculate' + index" style="padding-bottom: 130rpx">
          <view class="headline" v-if="current == 0"> 未记住 </view>
          <view class="headline" v-if="current == 1"> 已记住 </view>
          <view class="headline" v-if="current == 2"> 全部 </view>
          <view class="content_box" v-if="item[0].id">
            <view
              class="list"
              v-for="(citem, cindex) in item"
              :key="cindex"
              :class="{ active: citem.isExtend }"
            >
              <view class="head" @click="extend(citem)">
                <view class="left">
                  <view class="image">
                    <image
                      src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/16950043427848e3c169500434278459705_select.png"
                      mode="widthFix"
                    />
                  </view>
                  <!-- <text class="tit">第一章</text> -->
                  <text class="cont">{{ citem.name }}</text>
                </view>
                <view class="right">
                  <text>共{{ citem.number }}条</text>
                </view>
              </view>
              <view class="contents">
                <view
                  class="content"
                  @click="goDetail(citem)"
                  v-for="(jitem, jindex) in citem.child"
                  :key="jindex"
                >
                  <view class="content-head">
                    <view class="content-left">
                      <view class="circle"></view>
                      <text class="desc-title">{{ jitem.details_name }}</text>
                    </view>
                    <image
                      src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/16950046792662774169500467926615379_edit.png"
                      mode="widthFix"
                      class="edit"
                    />
                  </view>
                  <!-- <view class="content-static">
                  <view class="static"> 做对 0 </view>
                  <view class="static"> 做错 0 </view>
                  <view class="static"> 未答 147 </view>
                </view> -->
                </view>
              </view>
            </view>
          </view>
          <view v-else class="placeholder_box">
            <view class="cnt">
              <img
                src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/16975967239088c80169759672390853792_%E7%BC%96%E7%BB%84%205%402x.png"
                alt=""
              />
              <p>暂无数据~</p>
            </view>
          </view>
        </view>
      </swiper-item>
    </swiper>
  </view>
</template>
<script>
import { getTestpointknackchildlist } from '../../api/examEntry'
export default {
  name: '',
  components: {},
  data() {
    return {
      pid: '',
      current: 0,
      urlLink: '',
      lists: [],
      learn_exam_site_count: {
        not_learn_exam_site_count: '', //未记住
        learn_exam_site_count: '', //已记住
        all_exam_site_count: '' //全部
      },
      swiperHeight: 0
    }
  },
  onLoad(e) {
    this.pid = e.pid
    this.init()
  },
  onShow() {
    this.init()
  },
  methods: {
    init() {
      this.searchFn()
    },
    searchFn() {
      getTestpointknackchildlist({ pid: this.pid }).then(res => {
        this.learn_exam_site_count = {
          not_learn_exam_site_count: res.data.not_learn_exam_site_count, //未记住
          learn_exam_site_count: res.data.learn_exam_site_count, //未记住
          all_exam_site_count: res.data.all_exam_site_count //未记住
        }
        this.lists = [
          {
            ...res.data.not_learn_exam_site?.map(item => {
              return {
                ...item,
                isExtend: false
              }
            })
          },
          {
            ...res.data.learn_exam_site?.map(item => {
              return {
                ...item,
                isExtend: false
              }
            })
          },
          {
            ...res.data.all_exam_site?.map(item => {
              return {
                ...item,
                isExtend: false
              }
            })
          }
        ]
        this.$nextTick(() => {
          this.setSwiperHeight()
        })
      })
    },
    extend(item) {
      item.isExtend = !item.isExtend
      console.log(item.isExtend)
    },
    goDetail(data) {
      this.$xh.push(
        'jintiku',
        `pages/examEntry/entry?pid=${data.id}&label=${this.current + 1}`
      )
    },
    handleTabClick(index) {
      this.current = index
    },
    swiperChange(e) {
      this.current = e.detail.current
      this.$nextTick(() => {
        this.setSwiperHeight()
      })
    },
    setSwiperHeight() {
      let element = '#height_calculate' + this.current
      let query = uni.createSelectorQuery().in(this)
      console.log(query)
      query.select(element).boundingClientRect()
      query.exec(res => {
        console.log(res)
        if (res && res[0]) {
          console.log(res)
          this.swiperHeight = res[0].height
          console.log(this.swiperHeight)
        }
      })
    }
  },
  onShareAppMessage() {
    return this.$xh.shareAppMessage()
  }
}
</script>
<style lang="less" scoped>
.exam_knack {
  background: #ffffff;
  min-height: 100vh;
  .header_statistics {
    padding: 32rpx 32rpx 0 32rpx;
    .statistics {
      width: 100%;
      padding: 32rpx 0;
      background: #f8f8f8;
      border-radius: 16rpx;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      .division {
        width: 1rpx;
        height: 100rpx;
        background: #cbcbcb;
      }
      .details {
        flex: 1;
        height: 100rpx;
        text-align: center;
        display: flex;
        flex-direction: column;
        align-content: space-between;
        justify-content: space-between;
        .title {
          font-size: 28rpx;
          color: #666666;
        }
        .number {
          color: #333333;
          font-size: 32rpx;
        }
      }
      .init {
        .title,
        .number {
          color: #387dfc;
        }
      }
    }
  }
  .swiper {
    .headline {
      font-size: 32rpx;
      color: #000000;
      font-weight: 500;
      padding: 48rpx 32rpx 0;
    }
    .content_box {
      padding: 0rpx 32rpx 48rpx;
      .list {
        .head {
          height: 124rpx;
          display: flex;
          justify-content: space-between;
          align-items: center;
          border-bottom: 1rpx solid #f0f0f0;
          // border-top: 1rpx solid #f0f0f0;
          .left {
            display: flex;
            align-items: center;
            image {
              transform: rotateZ(180deg);
              width: 32rpx;
              height: 32rpx;
              margin-right: 24rpx;
              margin-top: 5px;
              transition: all 0.25s;
            }
            text {
              font-size: 28rpx;
              font-family: PingFangSC-Medium, PingFang SC;
              font-weight: 800;
              color: #333333;
            }
            .tit {
              margin-right: 4rpx;
            }
          }
          .right {
            font-size: 24rpx;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #999999;
            text:first-child {
              color: #387dfc;
            }
          }
        }
        .contents {
          // padding: 48rpx 0;
          height: 0;
          overflow: hidden;
          padding: 0;
          transition: all 0.25s;
          .content {
            margin: 0 16rpx 20px;
            .content-head {
              display: flex;
              align-items: center;
              justify-content: space-between;
              .content-left {
                display: flex;
                align-items: center;
                .circle {
                  width: 20rpx;
                  height: 20rpx;
                  margin-right: 30rpx;
                  background: #387dfc;
                  border-radius: 50%;
                  margin-left: -8rpx;
                }
                .unit {
                  font-size: 26rpx;
                  font-family: PingFangSC-Regular, PingFang SC;
                  font-weight: 400;
                  color: #333333;
                  margin-right: 4rpx;
                }
                .desc-title {
                  font-size: 26rpx;
                  font-family: PingFangSC-Regular, PingFang SC;
                  font-weight: 400;
                  color: #333333;
                }
              }
              .edit {
                width: 24rpx;
                height: 24rpx;
              }
            }
            // .content-static {
            //   height: 94rpx;
            //   border-left: 2rpx dashed #387dfc;
            //   padding-top: 20rpx;
            //   padding-left: 40rpx;
            //   display: flex;

            //   .static {
            //     margin-right: 80rpx;
            //     font-size: 24rpx;
            //     font-family: PingFangSC-Regular, PingFang SC;
            //     font-weight: 400;
            //     color: #999999;
            //   }
            // }
            // .content-static:last-child {
            //   border-left: 0 none;
            // }
          }
          .content:last-child .content-static {
            border-left: 0 none;
          }
        }
      }
      .list:last-child {
        border-bottom: 0 none;
      }
      .active {
        .head {
          .left {
            image {
              transform: rotateZ(0deg);
            }
          }
        }
        .contents {
          padding: 48rpx 0 0;
          overflow: visible;
          height: auto;
          border-bottom: 1rpx solid #f0f0f0;
        }
      }
    }
    .placeholder_box {
      margin-top: 180rpx;
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      .cnt {
        img {
          width: 300rpx;
          height: 220rpx;
        }
        p {
          text-align: center;
          color: #787e8f;
          font-size: 32rpxs;
          margin-top: 40rpx;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
        }
      }
    }
  }
}
</style>
