<template>
  <view class="test_entry">
    <view class="content_box" v-if="contentList.length">
      <view
        class="list"
        v-for="(citem, cindex) in contentList"
        :key="cindex"
        :class="{ active: citem.isExtend }"
      >
        <view class="head" @click="extend(citem)">
          <view class="left">
            <view class="image">
              <image
                src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/16950043427848e3c169500434278459705_select.png"
                mode="widthFix"
              />
            </view>
            <!-- <text class="tit">第一章</text> -->
            <text class="cont">{{ citem.name }}</text>
          </view>
          <view class="right">
            <text>共{{ citem.number }}条</text>
          </view>
        </view>
        <view class="contents">
          <view
            class="content"
            @click="goDetail(citem)"
            v-for="(jitem, jindex) in citem.child"
            :key="jindex"
          >
            <view class="content-head">
              <view class="content-left">
                <view class="circle"></view>
                <text class="desc-title">{{ jitem.details_name }}</text>
              </view>
              <image
                src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/16950046792662774169500467926615379_edit.png"
                mode="widthFix"
                class="edit"
              />
            </view>
            <!-- <view class="content-static">
                  <view class="static"> 做对 0 </view>
                  <view class="static"> 做错 0 </view>
                  <view class="static"> 未答 147 </view>
                </view> -->
          </view>
        </view>
      </view>
    </view>
    <view v-else class="placeholder_box">
      <view class="cnt">
        <img
          src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/16975967239088c80169759672390853792_%E7%BC%96%E7%BB%84%205%402x.png"
          alt=""
        />
        <p>暂无收藏，快去记考点吧~</p>
      </view>
    </view>
  </view>
</template>
<script>
import { getTestpointcollectlist } from '../../api/examEntry'
export default {
  data() {
    return {
      contentList: [],
      staticInfo: {}
    }
  },
  onShow() {
    this.getList()
  },
  onLoad() {
    this.getList()
  },
  methods: {
    goDetail(data) {
      this.$xh.push('jintiku', `pages/examEntry/entry?pid=${data.id}&label=4`)
    },
    onDetails(citem) {
      this.goDetail(`pages/examEntry/examKnack?pid=${citem.id}`)
    },
    getList() {
      this.contentList = []
      getTestpointcollectlist().then(data => {
        this.contentList = data.data.exam_site.map(res => {
          return {
            ...res,
            isExtend: false
          }
        })
      })
    },
    extend(item) {
      item.isExtend = !item.isExtend
      console.log(item.isExtend)
    }
  },
  onShareAppMessage() {
    return this.$xh.shareAppMessage()
  }
}
</script>
<style lang="less" scoped>
.test_entry {
  background: #ffffff;
  min-height: 100vh;
  .content_box {
    padding: 0rpx 32rpx 48rpx;
    .list {
      .head {
        height: 124rpx;
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-bottom: 1rpx solid #f0f0f0;
        // border-top: 1rpx solid #f0f0f0;
        .left {
          display: flex;
          align-items: center;
          image {
            transform: rotateZ(180deg);
            width: 32rpx;
            height: 32rpx;
            margin-right: 24rpx;
            margin-top: 5px;
            transition: all 0.25s;
          }
          text {
            font-size: 28rpx;
            font-family: PingFangSC-Medium, PingFang SC;
            font-weight: 800;
            color: #333333;
          }
          .tit {
            margin-right: 4rpx;
          }
        }
        .right {
          font-size: 24rpx;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #999999;
          text:first-child {
            color: #387dfc;
          }
        }
      }
      .contents {
        // padding: 48rpx 0;
        height: 0;
        overflow: hidden;
        padding: 0;
        transition: all 0.25s;
        .content {
          margin: 0 16rpx 20px;
          .content-head {
            display: flex;
            align-items: center;
            justify-content: space-between;
            .content-left {
              display: flex;
              align-items: center;
              .circle {
                width: 20rpx;
                height: 20rpx;
                margin-right: 30rpx;
                background: #387dfc;
                border-radius: 50%;
                margin-left: -8rpx;
              }
              .unit {
                font-size: 26rpx;
                font-family: PingFangSC-Regular, PingFang SC;
                font-weight: 400;
                color: #333333;
                margin-right: 4rpx;
              }
              .desc-title {
                font-size: 26rpx;
                font-family: PingFangSC-Regular, PingFang SC;
                font-weight: 400;
                color: #333333;
              }
            }
            .edit {
              width: 24rpx;
              height: 24rpx;
            }
          }
          // .content-static {
          //   height: 94rpx;
          //   border-left: 2rpx dashed #387dfc;
          //   padding-top: 20rpx;
          //   padding-left: 40rpx;
          //   display: flex;

          //   .static {
          //     margin-right: 80rpx;
          //     font-size: 24rpx;
          //     font-family: PingFangSC-Regular, PingFang SC;
          //     font-weight: 400;
          //     color: #999999;
          //   }
          // }
          // .content-static:last-child {
          //   border-left: 0 none;
          // }
        }
        .content:last-child .content-static {
          border-left: 0 none;
        }
      }
    }
    .list:last-child {
      border-bottom: 0 none;
    }
    .active {
      .head {
        .left {
          image {
            transform: rotateZ(0deg);
          }
        }
      }
      .contents {
        padding: 48rpx 0 0;
        overflow: visible;
        height: auto;
        border-bottom: 1rpx solid #f0f0f0;
      }
    }
  }
  .placeholder_box {
    padding-top: 180rpx;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    .cnt {
      img {
        width: 300rpx;
        height: 220rpx;
      }
      p {
        text-align: center;
        color: #787e8f;
        font-size: 32rpxs;
        margin-top: 40rpx;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
      }
    }
  }
}
</style>
