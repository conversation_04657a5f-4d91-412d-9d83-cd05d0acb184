<template>
  <view class="test_entry">
    <view class="header_statistics">
      <view class="statistics">
        <view class="details">
          <view class="title">我已记住</view>
          <view class="number">{{ staticInfo.learn_number }}条</view>
        </view>
        <view class="division"></view>
        <view class="details" @click="onCollectClick">
          <view class="title">我已收藏</view>
          <view class="number">{{ staticInfo.collect_number }}条</view>
        </view>
      </view>
    </view>
    <view class="content_box" v-if="contentList.length">
      <view
        class="content"
        v-for="(item, cindex) in contentList"
        @click="onDetails(item)"
        :key="cindex"
      >
        <view class="left_img">
          <img
            src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/169511338762776c2169511338762759752_%E7%BC%96%E7%BB%84%403x.png"
            alt=""
          />
        </view>
        <view class="content_details">
          <view class="content_details_title">{{ item.name }}</view>
          <view class="content_details_number">
            已记住
            <view style="margin-left: 10rpx" class="main-color">
              {{ item.learn_number }}
            </view>
            /
            <view>{{ item.number }}</view>
            （{{ item.progress_rate }}%）
          </view>
        </view>
        <view class="right_img">
          <!-- @click="goDetail('pages/examEntry/examKnack')" -->
          <img
            src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/169511461243550e1169511461243524472_%E8%B7%AF%E5%BE%84%202%402x.png"
            alt=""
          />
        </view>
        <view class="pre-look" v-if="item.is_checked == '1'">上次练习</view>
      </view>
    </view>
    <view v-else class="placeholder_box">
      <view class="cnt">
        <img
          src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/16975967239088c80169759672390853792_%E7%BC%96%E7%BB%84%205%402x.png"
          alt=""
        />
        <p>暂无词条~</p>
      </view>
    </view>
  </view>
</template>
<script>
import { getTestpointknacklist } from '../../api/examEntry'
export default {
  data() {
    return {
      contentList: [],
      staticInfo: {
        collect_number: 0, // 已收藏数量
        learn_number: 0 // 记住数量
      }
    }
  },
  onShow() {
    this.getList()
  },
  methods: {
    goDetail(url) {
      this.$xh.push('jintiku', url)
    },
    onDetails(citem) {
      this.goDetail(`pages/examEntry/examKnack?pid=${citem.id}`)
    },
    getList() {
      getTestpointknacklist().then(data => {
        this.contentList = data.data.exam_site
        this.staticInfo.collect_number = data.data.collect_number
        this.staticInfo.learn_number = data.data.learn_number
        console.log(data)
      })
    },
    onCollectClick() {
      this.goDetail(`pages/examEntry/examEntryCollect`)
    }
  },
  onShareAppMessage() {
    return this.$xh.shareAppMessage()
  }
}
</script>
<style lang="less" scoped>
.test_entry {
  background: #ffffff;
  min-height: 100vh;
  .header_statistics {
    padding: 32rpx 32rpx 0 32rpx;
    .statistics {
      width: 100%;
      padding: 32rpx 0;
      background: #f8f8f8;
      border-radius: 16rpx;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      .division {
        width: 1rpx;
        height: 100rpx;
        background: #cbcbcb;
      }
      .details {
        flex: 1;
        height: 100rpx;
        text-align: center;
        display: flex;
        flex-direction: column;
        align-content: space-between;
        justify-content: space-between;
        .title {
          font-size: 28rpx;
          color: #666666;
        }
        .number {
          color: #333333;
          font-size: 32rpx;
        }
      }
    }
  }
  .content_box {
    padding: 0 32rpx;
    .content {
      width: 100%;
      border-bottom: 1rpx solid #f0f0f0;
      display: flex;
      justify-content: flex-start;
      align-items: flex-start;
      padding: 48rpx 0;
      position: relative;
      .left_img {
        width: 40rpx;
        height: 32rpx;
        margin-right: 16rpx;
        img {
          width: 100%;
          height: 100%;
          margin-top: 6rpx;
        }
      }
      .content_details {
        .content_details_title {
          margin-bottom: 32rpx;
          font-size: 28rpx;
          color: #333333;
        }
        .content_details_number {
          font-size: 24rpx;
          color: #666666;
          display: flex;
          justify-content: flex-start;
        }
      }
      .right_img {
        width: 22rpx;
        height: 32rpx;
        margin-left: auto;
        margin-top: 38rpx;
        img {
          width: 100%;
          height: 100%;
        }
      }
      .pre-look {
        position: absolute;
        right: -20rpx;
        top: 20rpx;
        width: 110rpx;
        height: 35rpx;
        line-height: 35rpx;
        background-color: #ffa4d6;
        color: #fff;
        font-size: 20rpx;
        text-align: center;
        border-top-right-radius: 15rpx;
        border-bottom-left-radius: 15rpx;
      }
    }
  }
  .placeholder_box {
    padding-top: 180rpx;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    .cnt {
      img {
        width: 300rpx;
        height: 220rpx;
      }
      p {
        text-align: center;
        color: #787e8f;
        font-size: 32rpxs;
        margin-top: 40rpx;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
      }
    }
  }
}
</style>
