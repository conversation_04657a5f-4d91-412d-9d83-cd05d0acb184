<template>
  <!--  #ifdef H5  -->
  <view class="code-receive">
    <image
      src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/17012279436164661170122794361674007_App.png"
      class="App"
    ></image>
    <view class="text">
      医考金题甄选，免费刷题听课！ 刷金题听讲解，记口诀战医考！
    </view>
    <view class="android btn flex-center"> Android下载 </view>
    <view class="ios btn flex-center"> ios下载 </view>
  </view>
  <!--  #endif -->
</template>
<script>
//#ifdef H5
export default {
  data() {
    return {}
  }
}
//#endif
</script>
<style scoped lang="less">
/* #ifdef H5 */
.code-receive {
  height: 100vh;
  background-color: #fff;
  display: flex;
  align-items: center;
  flex-direction: column;
  padding-top: 148rpx;
  .App {
    width: 120rpx;
    height: 120rpx;
  }
  .text {
    width: 504rpx;
    height: 120rpx;
    font-size: 36rpx;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    color: #1469ff;
    line-height: 60rpx;
    margin: 0 auto;
    margin-top: 120rpx;
    margin-bottom: 100rpx;
  }
  .btn {
    width: 670rpx;
    height: 92rpx;
    background: #1469ff;
    border-radius: 46rpx;
    color: #fff;
    margin-bottom: 60rpx;
  }
  .btn:last-child {
    border: 2rpx solid #1469ff;
    background-color: #fff;
    color: #1469ff;
  }
}
/* #endif */
</style>
