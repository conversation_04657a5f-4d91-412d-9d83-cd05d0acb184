<template>
  <!--  #ifdef H5  -->
  <view class="open-app">
    <image
      src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/17012279436164661170122794361674007_App.png"
      class="App"
    ></image>
    <image
      src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/170122798414991c4170122798414918796_person.png"
      class="person"
    ></image>
    <view class="btns">
      <view class="btn flex-center" @click="openApp">打开App</view>
      <view class="btn flex-center" @click="downLoad">去下载</view>
    </view>
  </view>
  <!--  #endif -->
</template>
<script>
//#ifdef H5
export default {
  data() {
    return {}
  },
  methods: {
    openApp() {},
    downLoad() {
      this.$xh.push('jintiku', 'pages/h5Active/app-upload')
    }
  }
}
//#endif
</script>
<style scoped lang="less">
/* #ifdef H5 */
.open-app {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  padding-top: 148rpx;
  .App {
    width: 120rpx;
    height: 120rpx;
  }
  .person {
    width: 612rpx;
    height: 716rpx;
  }
  .btns {
    display: flex;
    align-items: center;
    margin-top: 84rpx;
    justify-content: center;
    .btn {
      width: 300rpx;
      height: 92rpx;
      background: #1469ff;
      border-radius: 20rpx;
      color: #fff;
    }
    .btn:last-child {
      background: #2cc28c;
      margin-left: 40rpx;
    }
  }
}

/* #endif */
</style>
