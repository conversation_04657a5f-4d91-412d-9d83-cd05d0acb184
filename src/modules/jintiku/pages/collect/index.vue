<template>
  <view class="collect_question">
    <view class="header_select">
      <view class="select_button" @click="showType = true">
        {{ question_type_name ? question_type_name : '题型' }}
        <image
          src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/169693015786889b916969301578688446_%E7%BC%96%E7%BB%84%402x.png"
        />
      </view>
      <view class="select_button" @click="showTime = true">
        {{ time_range_name ? time_range_name : '时间' }}
        <image
          src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/169693015786889b916969301578688446_%E7%BC%96%E7%BB%84%402x.png"
        />
      </view>
    </view>
    <view style="padding: 24rpx 24rpx; margin-top: 85rpx">
      <view class="content" v-for="(obj, cindex) in listInfo" :key="cindex">
        <view class="headline">{{ obj.type_name }}题型</view>
        <view
          class="question_content"
          v-for="(jitem, jindex) in obj.arr"
          :key="jindex"
          @click="goDetail(jitem)"
        >
          <view class="title">
            <view v-if="jitem.thematic_stem">
              病例： {{ jitem.thematic_stem }}
            </view>
            <view v-else>
              <view
                class="info flex"
                v-for="(res, oi) in jitem.titleInfo"
                :key="oi"
                style="align-items: start"
              >
                <view>{{ isB1(jitem.type) ? eng[oi] : jindex + 1 }}、</view>
                <view
                  v-html="res"
                  style="word-wrap: break-word; width: auto; overflow: hidden"
                ></view>
              </view>
            </view>
          </view>
          <view class="synopsis">
            <view class="time">{{ jitem.created_at.substring(0, 16) }}</view>
            <!-- <view class="label">易错</view>
            <view class="label">思路错误</view> -->
            <view class="star">
              <view v-for="item in 5" :key="item" style="margin-right: 9rpx">
                <image
                  src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/16953700953269c48169537009532642625_%E7%BC%96%E7%BB%84%E5%A4%87%E4%BB%BD%205%402x.png"
                  alt=""
                  v-if="item < jitem.level"
                />
                <image
                  src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/16953700763015ff616953700763011336_Fill%202%402x.png"
                  alt=""
                  v-else
                />
              </view>
            </view>
          </view>
        </view>
      </view>
      <view class="finsh" v-if="finsh && question.length"> 没有更多啦～ </view>
      <view class="finsh" v-if="!question.length"> 没有任何收藏呢～ </view>
    </view>
    <select-question-type
      v-model="showType"
      :type.sync="search.question_type"
      :name.sync="question_type_name"
      @success="success"
    />
    <select-time-range
      v-model="showTime"
      :type.sync="search.time_range"
      :name.sync="time_range_name"
      :start_date.sync="search.start_date"
      :end_date.sync="search.end_date"
      @success="success"
      :selectList="selectList"
    ></select-time-range>
  </view>
</template>
<script>
import selectQuestionType from '../../components/collect/select-question-type.vue'
import selectTimeRange from '../../components/collect/select-time-range.vue'
import { getCollectlist } from '../../api/collect'
import { isB1 } from '../../utils/index'
export default {
  components: { selectQuestionType, selectTimeRange },
  data() {
    return {
      showType: false,
      showTime: false,
      question: [],
      listInfo: {},
      time_range_name: '时间',
      question_type_name: '',
      total: 0,
      finsh: false,
      eng: ['A', 'B', 'C', 'D', 'E', 'F', 'G'],
      search: {
        page: 1,
        size: 10,
        start_date: '',
        end_date: '',
        time_range: '0',
        question_type: ''
      },
      selectList: [
        {
          id: '0',
          label: '全部'
        },
        {
          id: '1',
          label: '近3天'
        },
        {
          id: '2',
          label: '一周内'
        },
        {
          id: '3',
          label: '一月内'
        },
        {
          id: '4',
          label: '自定义'
        }
      ]
    }
  },
  onShow() {
    this.getList()
  },
  methods: {
    isB1,
    success() {
      this.init()
      this.getList(true)
    },
    getList(refresh = false) {
      // this.listInfo = {}
      getCollectlist({
        ...this.search
      }).then(data => {
        let res = data.data
        if (!res.list) {
          this.finsh = true
          return
        }
        this.total = res.total
        let map = {}
        let list = res.list.map(item => {
          return {
            ...item,
            titleInfo: (() => {
              if (item.thematic_stem) {
                return item.thematic_stem
              } else if (isB1(item.type)) {
                let options = JSON.parse(item.stem_list[0].option)
                return options
              } else {
                let options = [item.stem_list[0].content]
                return options
              }
            })()
          }
        })
        list
          .sort((a, b) => a.type - b.type)
          .forEach(item => {
            if (!map[item.type]) {
              map[item.type] = {
                type_name: item.type_name,
                arr: []
              }
            }
            map[item.type].arr.push({ ...item, is_collect: '1' })
          })
        this.listInfo = map
        uni.stopPullDownRefresh()
        this.$forceUpdate()
      })
    },
    init() {
      this.search.page = 1
      this.total = 0
      this.listInfo = {}
    },
    goDetail(res) {
      uni.setStorageSync('__question_detail__', res)
      console.log('跳转')
      this.$xh.push('jintiku', 'pages/collect/detail')
    }
  },
  onPullDownRefresh() {
    this.init()
    this.getList(true)
  },
  onReachBottom() {
    if (this.question.length >= this.total) {
      this.finsh = true
      return
    }
    this.search.page += 1
    this.getList()
  },
  onLoad() {
    // this.getList()
  },
  onShareAppMessage() {
    return this.$xh.shareAppMessage()
  }
}
</script>
<style lang="less" scoped>
.collect_question {
  .header_select {
    width: 100%;
    background: #fff;
    display: flex;
    justify-content: flex-start;
    position: fixed;
    top: 0;
    z-index: 10;
    .select_button {
      width: 50%;
      text-align: center;
      font-size: 32rpx;
      padding: 16rpx 0;
      font-weight: 400;
      color: #262629;
      display: flex;
      justify-content: center;
      align-items: flex-start;
      image {
        width: 16rpx;
        height: 10rpx;
        margin: 18rpx 12rpx;
        transition: all 0.25s;
      }
    }
  }
  .content {
    margin-bottom: 24rpx;
    .headline {
      font-size: 30rpx;
      color: #161f30;
      font-weight: 500;
    }
    .question_content {
      padding: 30rpx 32rpx;
      background: #fff;
      border-radius: 12rpx;
      margin-top: 24rpx;
      .title {
        font-size: 30rpx;
        color: #212121;
        font-weight: 600;
      }
      .synopsis {
        display: flex;
        margin-top: 22rpx;
        .time {
          font-size: 24rpx;
          color: #898a8d;
          font-weight: 400;
        }
        .label {
          font-size: 20rpx;
          border-radius: 4rpx;
          background: #ebf1ff;
          padding: 8rpx 16rpx;
          color: #2e68ff;
          margin-right: 34rpx;
        }
        .star {
          display: flex;
          justify-content: flex-start;
          align-items: flex-start;
          // margin-top: 10rpx;
          margin-left: auto;
          image {
            width: 24rpx;
            height: 24rpx;
          }
          image:last-child {
            margin-right: 0;
          }
        }
      }
    }
  }
  .active {
    image {
      transform: rotateZ(180deg);
    }
  }
  .finsh {
    line-height: 80rpx;
    text-align: center;
    font-size: 24rpx;
    color: #ccc;
  }
}
</style>
