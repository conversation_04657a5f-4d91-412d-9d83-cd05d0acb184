<template>
  <view class="lists">
    <view
      class="list"
      v-for="(item, i) in list"
      :key="i"
      @click="goDetai(item)"
    >
      <view class="container">
        <view class="title">
          <text>{{ item.name }}</text>
        </view>
        <!-- <view class="desc">
          <text>模考时间：</text>
          <text>{{ item.start_time }}</text>
        </view> -->
        <view class="desc">
          <text>模考类型：</text>
          <text>{{ item.goods_type_id_name }}</text>
        </view>
        <view class="desc">
          <text>共有模考：</text>
          <text>{{ item.exam_num }}个</text>
        </view>
        <!-- <view class="desc">
          <text style="margin-right: 80rpx"
            >报名人数：{{ item.sign_up_num }}人</text
          >
          <text>
            参与：
            <text class="main-color">{{ getCYNum(item.exam_rounds) }}</text
            >/{{ item.exam_details_num }}场
          </text>
        </view> -->
      </view>
      <image
        src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/1669357686810b313166935768681084385_right.png"
        mode="widthFix"
        class="right"
      />
    </view>
    <view class="empty" v-if="!list.length"></view>
    <view class="tip" v-if="list.length == total && list.length != 0">
      后续活动，敬请期待
    </view>
    <view class="tip" v-if="total == 0"> 暂无任何数据 </view>
  </view>
</template>
<script>
import { getGoods } from '../../api/index'
export default {
  data() {
    return {
      list: [],
      total: 0,
      search: {
        page: 1,
        size: 10
      }
    }
  },
  onShow() {
    this.getList(true)
  },
  methods: {
    getList(bol = false) {
      getGoods({
        is_shelf: 1,
        is_usable: 1,
        type: 10,
        ...this.search
      }).then(data => {
        if (bol) {
          this.list = data.data.list ? data.data.list : []
        } else {
          this.list = [...this.list, ...(data.data.list ? data.data.list : [])]
        }
        this.total = data.data.total
        uni.stopPullDownRefresh()
      })
    },
    goDetai(item) {
      this.$xh.push(
        'jintiku',
        `pages/modelExaminationCompetition/examInfo?product_id=${item.id}&title=${item.name}`
      )
    },
    getCYNum(arr) {
      return arr.filter(item => item.status == '3').length
    }
  },
  // 下拉刷新的事件
  onPullDownRefresh() {
    this.total = 0
    this.search.page = 1
    this.getList(true)
  },
  onReachBottom() {
    if (this.list.length >= this.total) {
      return
    }
    this.search.page += 1
    this.getList()
  },
  onShareAppMessage() {
    return this.$xh.shareAppMessage()
  }
}
</script>
<style scoped lang="less">
.lists {
  min-height: 100vh;
  background-color: #fff;
  .list {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 30rpx;
    border-bottom: 1px solid #f0f0f0;
    .container {
      flex: 1;
      .title {
        font-size: 28rpx;
        font-family: AppleSystemUIFont;
        color: #333333;
        line-height: 28rpx;
        margin-bottom: 30rpx;
      }
      .desc {
        text {
          font-size: 24rpx;
          font-family: PingFangSC-Medium, PingFang SC;
          font-weight: 400;
          color: #999999;
          line-height: 24rpx;
        }
        .main-color {
          color: #387dfc;
        }
      }
    }
    .right {
      width: 40rpx;
    }
    &:first-child {
      border-top: 1px solid #f0f0f0;
    }
  }
  .tip {
    text-align: center;
    font-size: 28rpx;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #999999;
    line-height: 28rpx;
    margin-top: 60rpx;
    margin-bottom: 60rpx;
  }
  .empty {
    height: 120rpx;
  }
}
</style>
