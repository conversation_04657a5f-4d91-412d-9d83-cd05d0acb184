<template>
  <view class="examInfo">
    <view class="mian-title"> {{ title }} </view>
    <view class="time">
      <text>考试时间：</text>
      <text class="main-color">
        {{ currentInfo.start_time }}—{{ currentInfo.end_time }}
      </text>
    </view>
    <view class="table">
      <view class="tr" v-for="(exam, i) in exam_rounds" :key="i">
        <view class="td">{{ exam.start_time }}-{{ exam.end_time }}</view>
        <view class="td">{{ exam.exam_round_name }}</view>
        <view class="td">
          <view
            class="button flex-center"
            :class="{ active: status == '3' }"
            @click="sinUp(exam, i)"
          >
            <span
              v-if="
                exam.btn_is_enable == 1 &&
                (exam.status == '1' || exam.status == '5')
              "
              >进入考场</span
            >
            <span v-else> {{ exam.status_name }}</span>
          </view>
        </view>
      </view>
    </view>
    <view class="current-num flex-center">
      <text>已有</text>
      <text>{{ sign_up_count }}</text>
      <text>人报名</text>
    </view>
    <!-- <view class="live"> 解析直播课：考试当晚请到App查看 </view> -->
    <view class="btn flex-center">
      <view
        class="buutton flex-center"
        @click="goCurrentDetai"
        :class="{
          active: mock_status == '5'
        }"
      >
        {{ mock_status_name }}
      </view>
    </view>
    <view class="line" v-if="page != 'home'"></view>
    <view class="all" @click="all" v-if="page != 'home'">
      <view class="left flex-center">
        <image
          src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/16969200277943d26169692002779427480_4.png"
          mode="widthFix"
        />
        <view class="info">
          <view class="title"> 全部模考 </view>
          <view class="desc"> 参加往期错过的模考/查看往期成绩 </view>
        </view>
      </view>
      <image
        src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/1669357686810b313166935768681084385_right.png"
        mode="widthFix"
        class="right"
      />
    </view>
    <view class="ohter" v-if="page != 'home'">
      <view class="title"> 其他模考 </view>
      <view class="desc">
        <!-- <text>查看更多</text>
        <image
          src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/1669357686810b313166935768681084385_right.png"
          mode="widthFix"
        /> -->
      </view>
    </view>
    <view class="other-lists" v-if="page != 'home'">
      <view
        class="other-list"
        :class="{ active: item.id == mock_exam_id }"
        v-for="(item, i) in list"
        :key="i"
        @click="other(item)"
      >
        <view class="title"> {{ item.mock_name }} </view>
        <view class="time"> 模考时间：{{ item.start_time }} </view>
      </view>
    </view>
    <view
      class="help"
      @click="goDetai('pages/modelExaminationCompetition/help')"
    >
      帮助
    </view>

    <kfQrcode></kfQrcode>
  </view>
</template>
<script>
import { getExaminfoDetail, mockexamSignup, makeupSignup } from '../../api'
import kfQrcode from '../../components/commen/kf-qrcode.vue'
export default {
  components: {
    kfQrcode
  },
  data() {
    return {
      page: '',
      currentInfo: {},
      exam_rounds: [],
      list: [],
      product_id: '',
      title: '',
      sign_up_count: '0',
      mock_status: '',
      mock_status_name: '',
      mock_exam_id: '',
      currentIndex: 0
    }
  },
  onLoad(e) {
    this.product_id = e.product_id
    this.professional_id = e.professional_id
    this.title = e.title
    this.page = e.page
  },
  onShow() {
    this.getList()
  },
  methods: {
    sinUp(exam, i) {
      if (exam.status == '2') {
        // 未报名  请求报名接口
        mockexamSignup({
          exam_id: exam.mock_id
        }).then(data => {
          setTimeout(() => {
            this.getList()
          }, 100)
        })
        return
      }
      if (exam.status == '4') {
        this.$xh.Toast('补考还未开启哦！')
        // 补考未开启 调取补考接口
        // makeupSignup({
        //   exam_round_id: exam.mock_id
        // }).then(data => {
        //   this.getList()
        // })
        return
      }
      if (exam.btn_is_enable == 2) {
        // this.$xh.Toast('该场模考未开始！')
        return false
      }
      if (exam.status == '1' || exam.status == '5') {
        let citem = this.exam_rounds[i || this.currentIndex]
        let item = this.currentInfo
        let time =
          citem.status == 5
            ? (new Date(this.$xh.iosTime(citem.end_time)).getTime() -
                new Date(this.$xh.iosTime(citem.start_time)).getTime()) /
              1000
            : (new Date(this.$xh.iosTime(citem.end_time)).getTime() -
                new Date().getTime()) /
              1000
        let totalTime =
          (new Date(citem.end_time).getTime() -
            new Date(citem.start_time).getTime()) /
          1000
        this.$xh.push(
          'jintiku',
          `pages/examination/notice?id=${item.id}&eid=${citem.id}&pid=${
            item.professional_id
          }&pvid=${citem.exam_paper_id}&session=${this.transform(
            this.currentIndex
          )}&session_name=${
            citem.subject_name
          }&time=${time}&totalTime=${totalTime}&mock_name=${
            citem.exam_round_name
          }&status=${citem.status}`
        )
        // 已报名 去做题
        return
      }
      if (exam.status == '3') {
        // 查看成绩
        this.$xh.push(
          'jintiku',
          `pages/statistics/scoreReporting?examination_id=${exam.mock_id}&examination_session_id=${exam.id}&paper_version_id=${exam.exam_paper_id}`
        )
        return
      }

      if (exam.status == '5') {
        // 补考 去考试
        return
      }
    },
    goCurrentDetai() {
      if (this.mock_status == '5') {
        let citem = this.exam_rounds[this.currentIndex]
        this.sinUp(citem)
      }
    },
    getList() {
      getExaminfoDetail({
        product_id: this.product_id,
        mock_exam_id: this.mock_exam_id,
        professional_id: this.professional_id || ''
      }).then(data => {
        if (!data.data.mock_exam_details) {
          this.$xh.Toast('该场模考暂无任何数据！')
          return
        }
        this.currentInfo = data.data.mock_exam
        this.sign_up_count = data.data.sign_up_count
        this.exam_rounds = data.data.mock_exam_details
        this.mock_status = data.data.mock_status
        this.mock_status_name = data.data.mock_status_name
        // this.currentIndex = data.data.data_index * 1

        this.currentIndex = data.data.mock_exam_details.findIndex(
          e => e.status == '1' || e.status == '5'
        )
        if (!this.list.length) {
          this.list = data.data.mock_list
        }
      })
    },
    all() {
      uni.navigateBack({
        delta: 1
      })
    },
    goDetai(url) {
      this.$xh.push('jintiku', url)
    },
    other(item) {
      this.mock_exam_id = item.id
      this.getList()
    },
    transform(number) {
      let transformList = [
        '一',
        '二',
        '三',
        '四',
        '五',
        '六',
        '七',
        '八',
        '九',
        '十'
      ]
      return transformList[number]
    }
  },
  onShareAppMessage() {
    return this.$xh.shareAppMessage()
  }
}
</script>
<style scoped lang="less">
.examInfo {
  height: 100vh;
  overflow-y: scroll;
  background-color: #fff;
  .mian-title {
    font-size: 32rpx;
    font-family: AppleSystemUIFont;
    color: #333333;
    line-height: 32rpx;
    text-align: center;
    margin-top: 58rpx;
    margin-bottom: 58rpx;
  }
  .time {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24rpx;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #999999;
    line-height: 24rpx;
    margin-bottom: 20rpx;
  }
  .table {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-wrap: wrap;
    .tr {
      display: flex;
      align-items: center;
      .td {
        border: 1px solid #eee;
        height: 120rpx;
        line-height: 40rpx;
        text-align: center;
        font-size: 28rpx;
        display: flex;
        align-items: center;
        justify-content: center;

        .button {
          width: 150rpx;
          height: 60rpx;
          border-radius: 30rpx;
          border: 2rpx solid #387dfc;
          font-size: 28rpx;
          font-family: PingFangSC-Medium, PingFang SC;
          font-weight: 500;
          color: #387dfc;
          line-height: 60rpx;
          &.active {
            background-color: #387dfc;
            color: #fff;
          }
        }
      }
      .td:nth-child(1) {
        width: 200rpx;
        font-size: 20rpx;
      }
      .td:nth-child(2) {
        width: 276rpx;
        position: relative;
        left: -1px;
      }
      .td:nth-child(3) {
        width: 190rpx;
        position: relative;
        left: -2px;
      }
    }
    .tr:nth-child(2) {
      .td {
        position: relative;
        top: -1px;
      }
    }
    .tr:nth-child(3) {
      .td {
        position: relative;
        top: -2px;
      }
    }
  }
  .current-num {
    margin-top: 40rpx;
    margin-bottom: 40rpx;
    text {
      font-size: 24rpx;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #999999;
      line-height: 24rpx;
    }
    text:nth-child(2) {
      color: #333;
      margin: 0 6rpx;
    }
  }
  .live {
    font-size: 24rpx;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #999999;
    line-height: 24rpx;
    margin-bottom: 100rpx;
    text-align: center;
  }
  .btn {
    .buutton {
      width: 320rpx;
      height: 80rpx;
      background: #f5f5f5;
      border-radius: 45rpx;
      font-size: 32rpx;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #999999;
      line-height: 28rpx;
    }
  }
  .line {
    width: 100%;
    height: 16rpx;
    background: #f5f5f5;
    margin-top: 60rpx;
  }
  .all {
    display: flex;
    align-items: center;
    height: 125rpx;
    justify-content: space-between;
    padding: 0 40rpx;
    .left {
      image {
        width: 50rpx;
        height: 50rpx;
        margin-right: 19rpx;
      }
      .info {
        .title {
          font-size: 30rpx;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #333333;
          line-height: 30rpx;
          margin-bottom: 16rpx;
        }
        .desc {
          font-size: 24rpx;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #999999;
          line-height: 24rpx;
        }
      }
    }
    .right {
      width: 36rpx;
    }
  }
  .ohter {
    background: #f5f5f5;
    padding-left: 40rpx;
    height: 80rpx;
    display: flex;
    align-items: center;
    font-size: 30rpx;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #333333;
    line-height: 42rpx;
  }
  .other-lists {
    display: flex;
    align-items: center;
    overflow-x: scroll;
    margin-top: 40rpx;
    padding: 0 20rpx;
    .other-list {
      flex-shrink: 0;
      width: 440rpx;
      height: 210rpx;
      background: #f2f2f2;
      border-radius: 20rpx;
      padding: 22rpx 30rpx;
      margin-right: 24rpx;
      .title {
        font-size: 32rpx;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #333333;
        line-height: 42rpx;
        margin-bottom: 36rpx;
      }
      .time {
        text-align: left;
        justify-content: flex-start;
      }
    }
    .active {
      background-color: #d7e5ff;
    }
  }
  .help {
    position: fixed;
    right: 30rpx;
    top: 20rpx;
    font-size: 28rpx;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #999999;
    line-height: 28rpx;
  }
}
</style>
