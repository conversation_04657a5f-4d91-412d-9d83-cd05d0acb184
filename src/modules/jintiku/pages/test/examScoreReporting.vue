<template>
  <clientOnly>
    <div class="score_reporting">
      <div class="header-box">
        <div class="header">
          <image
            src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/169509193147644ed169509193147782088_back.png"
            mode="widthFix"
            class="back"
            @click="back"
          />
          <div class="major">
            <text>{{ student_name }}的成绩报告</text>
          </div>
        </div>
      </div>
      <div class="data_list">
        <div class="evaluate list_box">
          <div class="title_text">你的综合实力</div>
          <div class="evaluate_text">
            <text v-if="data.session_score.is_pass == 1"> 及格 </text>
            <text v-if="data.session_score.is_pass == 2"> 不及格 </text>
          </div>
          <div class="session">{{ data.session_score.examination_round }}</div>
          <div class="grade">
            <div class="grade_number">{{ data.session_score.score }}</div>
            <!-- <div class="grade_text">分数</div> -->
          </div>
        </div>
        <div class="list_box">
          <div class="title">成绩明细</div>
          <div class="content_list">
            <div>
              <div class="name">客观题得分</div>
              <div class="number">
                {{ data.session_score.objective_item_score || '-' }}分
              </div>
            </div>
            <div>
              <div class="name">主观题得分</div>
              <div class="number">
                {{ `${data.session_score.subjective_item_score}分` }}
              </div>
            </div>
            <div>
              <div class="name">试卷满分</div>
              <div class="number">
                {{ data.session_score.full_mark_score || '-' }}分
              </div>
            </div>
            <div>
              <div class="name">及格分</div>
              <div class="number">
                {{ data.session_score.passing_score || '-' }}分
              </div>
            </div>
          </div>
        </div>
        <div class="list_box" v-if="isShowSF">
          <div class="title">失分试题</div>
          <div>
            <table
              v-if="
                data.lose_points_question && data.lose_points_question.length
              "
            >
              <tr style="border: 0 none">
                <th style="" class="line line1">题号</th>
                <th style="" class="line2">得分</th>
                <th style="" class="line3">对应知识点</th>
              </tr>
              <div>
                <tr
                  v-for="(item, index) in data.lose_points_question"
                  class="lose_list"
                  :key="index"
                >
                  <td style="width: 120rpx; border-right: 0 none">
                    {{ item.sort || '-' }}
                  </td>
                  <td style="width: 120rpx; border-right: 0 none">
                    {{ item.get_score || '-' }}/{{ item.score || '-' }}
                  </td>
                  <td style="width: 100%">{{ item.knowledge || '-' }}</td>
                </tr>
              </div>
            </table>
            <div v-else style="display: flex; justify-content: center">
              <img
                style="width: 384rpx; height: 44rpx; margin-top: 22rpx"
                src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/datatpls/%E7%BC%96%E7%BB%84%2010%402x.png"
              />
            </div>
          </div>
        </div>
        <div class="list_box" style="overflow: hidden">
          <div class="title" style="margin-bottom: 26rpx">答题详情</div>
          <answer-particulars
            :data="data.answer_question_details"
            :isCorrect="data.session_score.is_correct"
            :isPublishAnswer="1"
          />
        </div>
        <div class="not_more">没有更多啦~</div>
      </div>
      <div class="background"></div>
      <div class="empty-button" @click="reanswerBtn">重新答题</div>
    </div>
  </clientOnly>
</template>
<script>
import { scorereporting } from '../../api/chapter'
import answerParticulars from '../../components/makeQuestion/answer-particulars.vue'
export default {
  components: { answerParticulars },
  props: {},
  data() {
    return {
      data: {},
      query: {},
      student_name: ''
    }
  },
  computed: {
    isShowSF() {
      if (!this.data.answer_question_details) {
        return false
      }
      let res = this.data.answer_question_details.filter(item => {
        return !this.isSubjective(item.question_type)
      })
      return !!res.length
    }
  },
  methods: {
    back() {
      uni.navigateBack({
        delta: 1
      })
    },
    init() {
      let data = {
        paper_version_id: this.query.paper_version_id,
        master_order_id: this.query.order_id,
        goods_id: this.query.goods_id
      }
      scorereporting(data).then(res => {
        this.data = res?.data
        this.data.answer_question_details =
          res?.data.answer_question_details.map(item => {
            return {
              ...item,
              question_list: item.question_list.map(citem => {
                return {
                  ...citem,
                  resource_info: citem.resource_info
                    ? JSON.parse(citem.resource_info)
                    : {}
                }
              })
            }
          })
      })
    },
    reanswerBtn() {
      uni.redirectTo({
        url:
          `/modules/jintiku/` +
          `pages/examination/test?paper_version_id=${
            this.query.paper_version_id
          }&goods_id=${this.query.goods_id}&order_id=${
            this.query.order_id
          }&title=${this.query.title}&professional_id=${
            this.query.professional_id
          }&type=8&time=${3600 * 2}`
      })
    },
    isSubjective(type) {
      return type == '8' || type == '10' || type == '14' // 14案例题
    }
  },
  onLoad(query) {
    this.query = query
    // this.init()

    let userInfo = uni.getStorageSync('__xingyun_userinfo__')
    if (userInfo.student_name == '未填写') {
      userInfo.student_name = '未填写姓名'
    }
    this.student_name = userInfo.nickname || userInfo.student_name
  },
  onShow() {
    this.init()
  },
  onShareAppMessage() {
    return this.$xh.shareAppMessage()
  }
}
</script>
<style lang="scss" scoped>
.score_reporting {
  position: relative;
  .header-box {
    padding-top: 80rpx;
    position: fixed;
    left: 0;
    top: 0;
    width: 100%;
    z-index: 2;
    background: #2e68ff;
    .header {
      position: relative;
      height: calc(96rpx);
      display: flex;
      align-items: center;
      justify-content: center;
      // margin-top: --status-bar-height;
      .back {
        position: absolute;
        width: 20rpx;
        height: 32rpx;
        left: 0rpx;
        top: 0;
        bottom: 0;
        margin: auto 0;
        z-index: 10;
        padding: 10rpx 30rpx;
      }

      .major {
        display: flex;
        align-items: center;
        justify-content: center;
        text {
          font-size: 32rpx;
          font-family: PingFangSC-Medium, PingFang SC;
          font-weight: 500;
          color: #fff;
          line-height: 36rpx;
        }
      }
    }
  }
  .data_list {
    margin-top: 220rpx;
    z-index: 1;
    position: relative;
    padding: 32rpx 24rpx;
    .list_box {
      padding: 30rpx 32rpx;
      background: #ffffff;
      border-radius: 12rpx;
      margin-bottom: 32rpx;
      .title {
        font-size: 32rpx;
        color: #161f30;
        font-weight: 500;
      }
      .content_list {
        width: 100%;
        padding: 20rpx;
        background: #f1f8ff;
        margin-top: 22rpx;
        // display: flex;
        // justify-content: space-between;
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        // grid-template-rows: repeat(2, 1fr);
        border-radius: 8rpx;
        .name {
          font-size: 26rpx;
          color: #787e8f;
          text-align: center;
        }
        .number {
          font-size: 28rpx;
          color: #2e68ff;
          margin-top: 20rpx;
          text-align: center;
          flex-shrink: 0;
        }
        .up {
          color: #44d7b6;
        }
        .down {
          color: #e02020;
        }
      }
    }

    .evaluate {
      position: relative;
      .title_text {
        color: #787e8f;
        font-size: 22rpx;
        margin-bottom: 20rpx;
      }
      .evaluate_text {
        font-size: 36rpx;
        font-weight: 400;
        color: #161f30;
        margin-bottom: 20rpx;
        display: inline-block;
        background-image: linear-gradient(to top, #2e68ff 35%, white 35%);
      }
      .session {
        color: #161f30;
        font-size: 26rpx;
        font-weight: 400;
      }
      .grade {
        width: 228rpx;
        height: 182rpx;
        position: absolute;
        top: -44rpx;
        right: 34rpx;
        background: url('https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/16967627423535267169676274235372918_%E7%BC%96%E7%BB%84%2014%E5%A4%87%E4%BB%BD%202%402x.png');
        background-size: 228rpx 182rpx;
        .grade_number {
          font-size: 38rpx;
          color: #2e68ff;
          font-weight: 500;
          text-align: center;
          margin-top: 52rpx;
        }
        .grade_text {
          font-size: 24rpx;
          color: #ffffff;
          text-align: center;
          margin-top: 4rpx;
        }
      }
    }
  }
  table {
    width: 100%;
    font-size: 26rpx;
    color: #161f30;
    margin-top: 24rpx;
    border-left: 2rpx solid #d7e5fe;
    border-right: 2rpx solid #d7e5fe;
    position: relative;
    tr,
    td {
      display: flex;
      justify-content: center;
      align-items: center;
    }
    tr {
      width: 100%;
      border-bottom: 2rpx solid #d7e5fe;
      // &:first-child {
      //   border: 0 none;
      // }
    }
    .line {
      width: 120rpx;
      border-top: 2rpx solid #d7e5fe;
      &::after {
        height: 100%;
        width: 2rpx;
        background-color: #d7e5fe;
        position: absolute;
        left: 86rpx;
        top: 0;
        content: '';
      }
    }
    .line2 {
      width: 120rpx;
      border-top: 2rpx solid #d7e5fe;
      &::after {
        height: 100%;
        width: 2rpx;
        background-color: #d7e5fe;
        position: absolute;
        left: 174rpx;
        top: 0;
        content: '';
      }
    }
    .line3 {
      width: 100%;
      border-top: 2rpx solid #d7e5fe;
    }

    th {
      background: #f1f8ff;
      height: 70rpx;
      line-height: 70rpx;
      text-align: center;
      border-right: 0;
    }
    td {
      text-align: center;
      line-height: 70rpx;
      border-top: 0;
      border-right: 0;
      min-height: 70rpx;
      border-right: 2rpx solid #d7e5fe;
    }
    td:last-child,
    th:last-child {
      border-right: 0;
    }
    td:first-child,
    th:first-child {
      border-left: 0;
    }
  }
  .background {
    background: linear-gradient(
      180deg,
      #2e68ff 0%,
      #5197fe 43%,
      rgba(255, 255, 255, 0) 100%
    );
    width: 100%;
    height: 350rpx;
    position: absolute;
    top: -48rpx;
    left: 0;
  }
  .not_more {
    line-height: 80rpx;
    text-align: center;
    font-size: 24rpx;
    color: #ccc;
    width: 100%;
    margin-bottom: 80rpx;
  }
  .empty-button {
    position: fixed;
    bottom: 40rpx;
    width: 60%;
    height: 68rpx;
    background: #2e68ff;
    border-radius: 44rpx;
    font-weight: 400;
    font-size: 26rpx;
    color: #ffffff;
    text-align: center;
    line-height: 68rpx;
    z-index: 111111;
    left: 0;
    right: 0;
    margin: auto;
  }
}
</style>
