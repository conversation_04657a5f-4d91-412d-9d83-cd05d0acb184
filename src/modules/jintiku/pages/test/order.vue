<template>
  <view>
    <view class="tab">
      <view
        class="item"
        v-for="item of tab"
        :class="{
          current: status == item.id
        }"
        @click="tabTap(item)"
        ><text>{{ item.name }}</text></view
      >
    </view>
    <orderList
      :status="item.id"
      v-show="status == item.id"
      v-if="item.init"
      v-for="item of tab"
    ></orderList>
  </view>
</template>

<script>
import orderList from '../../components/commen/order-list.vue'
export default {
  components: {
    orderList
  },
  data() {
    return {
      status: '0',
      // 订单状态|支付状态(1:待支付 2:已完成 3:待补缴 4:已取消 5:退费中 6:已退费 7:部分退费中 8:部分已退费)
      tab: [
        {
          id: '0',
          name: '全部',
          init: true
        },
        {
          id: '2',
          name: '已支付',
          init: false
        },
        {
          id: '1',
          name: '待支付',
          init: false
        },
        {
          id: '4',
          name: '已取消',
          init: false
        }
      ]
    }
  },
  onShow() {},
  methods: {
    tabTap(item) {
      this.status = item.id
      item.init = true
    }
  },
  onShareAppMessage() {
    return this.$xh.shareAppMessage()
  }
}
</script>

<style lang="scss" scoped>
.tab {
  padding: 32rpx 0 16rpx 0;
  justify-content: space-evenly;
  display: flex;
  background: #ffffff;
  .item {
    font-size: 28rpx;
    font-weight: bold;
    color: #686f81;
    line-height: 44rpx;
  }
  .current {
    font-size: 28rpx;
    font-weight: bold;
    color: #1a1b1c;
    line-height: 44rpx;
    position: relative;
    // &::after {
    //   position: absolute;
    //   bottom: -16rpx;
    //   left: calc(50% - 16rpx);

    //   content: ' ';
    //   display: block;
    //   width: 32rpx;
    //   height: 6rpx;
    //   background: #ff6616;
    //   border-radius: 4rpx;
    // }
  }
}
.list {
  height: calc(100vh - 100rpx);
  overflow-y: auto;
  .content {
    padding: 32rpx;
  }
}
</style>
