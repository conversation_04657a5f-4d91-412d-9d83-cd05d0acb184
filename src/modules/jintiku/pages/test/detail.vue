<template>
  <scroll-view scroll-y style="height: 100vh">
    <navbar title="商品详情"></navbar>
    <view class="goods-info">
      <image
        mode="scaleToFill"
        class="img"
        :src="info.material_cover_path"
      ></image>
      <view class="seckill-box">
        <view class="tl"></view>
        <view class="tr"></view>
        <view class="t">
          <view class="l">题库秒杀</view>
          <view class="r">
            <view class="text">限时秒杀</view>
            <view class="">
              <countDown :time="time" @finish="() => {}"></countDown>
            </view>
          </view>
        </view>
        <view class="price-text">
          <view class="ee">
            <view>秒杀价</view>
            <view>
              <text>￥</text><text>{{ prices[active].sale_price }}</text>
            </view>
          </view>
          <view class="ee"><view>=</view></view>
          <view class="ee">
            <view>原价</view>
            <view>
              <text>￥</text><text>{{ prices[active].original_price }}</text>
            </view>
          </view>
          <view class="ee"><view>-</view></view>
          <view class="ee">
            <view>限时优惠</view>
            <view>
              <text>￥</text><text>{{ prices[active].discount_price }}</text>
            </view>
          </view>
        </view>
      </view>
      <view class="info-box">
        <view class="name-box">
          <view class="name">{{ info.name }}</view>
          <view class="share-but" v-if="true" @click="share()">分享海报</view>
        </view>

        <view class="tags">
          <view class="ee">{{ info.num_text }}</view>
          <view class="ee">{{ info.year }}</view>
          <!-- <view class="ee">有效期 10个月</view> -->
        </view>
        <view class="path" v-if="info.tips"> {{ info.tips || '' }} </view>
        <view class="fen"></view>
        <view class="prices">
          <view
            class="ee"
            :class="{
              active: active == index
            }"
            @click="activeChange(index)"
            v-for="(item, index) of prices"
            >有效期 {{ item.month == 0 ? '永久' : item.month + '个月' }}
          </view>
        </view>
      </view>
    </view>
    <view v-if="info.type == 18" class="chapter-info">
      <view class="header">
        <image
          src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/604d173977513550467950_xingixng.png"
        ></image>
        <text
          >本题库共有 {{ info.tiku_goods_details.question_num || 0 }} 道题</text
        >
      </view>
      <view class="chapter-list">
        <!-- :disabled="info.permission_status == '2'" -->
        <treeChapter :list="lists" :disabled="true" />
      </view>
    </view>
    <view v-if="info.type == 8 || info.type == 10" class="goods-info-img">
      <image
        mode="widthFix"
        :src="completepath(info.material_intro_path)"
      ></image>
    </view>
    <view style="height: 168rpx"></view>
    <view class="pay-info" v-if="info.permission_status == '2'">
      <view class="price"
        ><text>￥</text><text>{{ prices[active].sale_price }}</text></view
      >
      <login @success="getOrder">
        <view class="but"> 立即购买</view>
      </login>
    </view>
    <view
      class="pay-info"
      style="justify-content: center"
      v-if="info.permission_status == '1'"
    >
      <view class="but" v-if="info.type == 18" @click="goDetail">
        立即刷题
      </view>
      <view class="but" v-else @click="goDetail"> 立即测试 </view>
    </view>
    <!-- 分享海报 -->
    <view class="targetShare">
      <snapshot mode="view" id="targetShare" :key="active">
        <view class="share">
          <image
            mode="scaleToFill"
            class="img"
            :src="info.material_cover_path"
          ></image>

          <view class="info-box">
            <view class="name">{{ info.name }}</view>

            <view class="tags">
              <view class="ee">{{ info.num_text }}</view>
              <view class="ee">{{ info.year }}</view>
            </view>
            <view class="flex" style="align-items: baseline">
              <view class="price">
                <text>￥</text><text>{{ prices[active].sale_price }}</text>
              </view>
              <view
                class="huaxian flex"
                style="text-decoration: line-through; margin-left: 12px"
                ><text>￥</text><text>{{ prices[active].original_price }}</text>
              </view>
            </view>
            <view class="flex qrcode-box">
              <view class="tips">
                <view>长按图片识别二维码 </view>
                <view>加入我们一起学习</view>
              </view>
              <image class="qrcode" :src="qrcodeUrl"></image>
            </view>
          </view>
        </view>
      </snapshot>
    </view>
  </scroll-view>
</template>

<script>
import treeChapter from '../../components/treeChapter/index.vue'
import navbar from '../../components/commen/navbar.vue'
import {
  getGoodsDetail,
  getOrderV2,
  wechatapplet,
  payModeListNew,
  payModeListNewDetail,
  paper,
  chapterpaper,
  miniQrcode
} from '../../api/index'
import { chapterpackageTree } from '../../api/chapter'
import { app_id } from '../../config'
import { setSubscribeMessage, goToLogin } from '../../utils'
import login from '../../components/commen/login.vue'
import countDown from '../../components/makeQuestion/count-down2.vue'
export default {
  components: {
    treeChapter,
    login,
    navbar,
    countDown
  },
  provide() {
    return {
      updatePrePriceData: () => {}
    }
  },
  data() {
    return {
      id: '',
      employee_id: '',
      professional_id: '',
      active: 0,
      prices: [{}],
      info: {
        type: 0,
        tiku_goods_details: {
          question_num: 0
        }
      },
      lists: [],

      qrcodeUrl: '',
      time: 88888,
      isfree: 0
    }
  },
  async onLoad(e) {
    //用二维码id换取真实url数据
    let query = e
    if (query.scene) {
      await miniQrcode
        .getQuery({
          scene_id: query.scene
        })
        .then(res => {
          if (res.data.data != '') {
            query = JSON.parse(res.data.data)
          }
        })
    }
    this.id = query.id
    if (query.active) {
      this.active = query.active
    }
    this.professional_id = query.professional_id
    //推广员工id用于下单时记录
    if (query.employee_id) {
      this.$store.commit('jintiku/setEmployeeId', query.employee_id)
    }
    this.getGoodsDetail()
    this.setTime()
    // this.getChapterpackageTree()
  },
  onShow() {
    this.shareQrcode()
  },
  methods: {
    activeChange(index) {
      this.active = index
      this.shareQrcode()
    },
    setTime() {
      let key = '__tiku_goods_time__'
      let time = uni.getStorageSync(key)
      let d = new Date()
      let total_time = 3600 * 8 * 1000
      let setStartTime = () => {
        uni.setStorageSync(key, d.getTime())
        this.time = total_time
      }
      if (time) {
        this.time = total_time - (d.getTime() - time)
        if (this.time < 0) {
          setStartTime()
        }
      } else {
        setStartTime()
      }
    },
    shareQrcode() {
      let that = this
      if (!this.info.id) {
        return
      }
      // if (that.qrcodeUrl) {
      //   return
      // }
      let userinfo = uni.getStorageSync('__xingyun_userinfo__')
      let employee_id = ``
      if (userinfo?.employee_info?.employee_id) {
        employee_id = `&employee_id=${userinfo?.employee_info?.employee_id}`
      }
      let path = `/modules/jintiku/pages/test/detail?id=${this.info.id}&active=${this.active}&professional_id=${this.info.professional_id}${employee_id}`
      let qrcodeUrl = `${process.env.VUE_APP_WEB_BASE}${path}`
      console.log(qrcodeUrl, '分享url')
      miniQrcode
        .get({
          page: 'modules/jintiku/pages/test/detail',
          scence_params: JSON.stringify({
            id: this.info.id,
            active: this.active,
            professional_id: this.info.professional_id,
            employee_id: userinfo?.employee_info?.employee_id || ''
          }),
          // env_version: 'trial',
          appid: app_id
        })
        .then(res => {
          that.qrcodeUrl = res.data.data
        })
      return
    },
    share(type = 2) {
      if (!this.$store.state.jintiku.token) {
        goToLogin()
        return
      }
      setSubscribeMessage('share')
      this.createSelectorQuery()
        .select('#targetShare')
        .node()
        .exec(res => {
          const node = res[0].node
          node.takeSnapshot({
            type: 'arraybuffer', // 截图类型，可选值为'arraybuffer'或'base64'
            format: 'png', // 图片格式，可选值为'jpg'或'png'
            success: res => {
              // 成功回调函数，res中包含生成的图片数据
              // console.log('生成海报成功', res)

              const f = `${wx.env.USER_DATA_PATH}/share${Math.floor(
                Math.random() * 1000
              )}.png`
              const fs = wx.getFileSystemManager()
              console.log(f, 'f')
              // 将海报数据写入本地文件
              fs.writeFileSync(f, res.data, 'binary')
              // console.log(f)
              if (type == 1) {
                wx.saveImageToPhotosAlbum({
                  filePath: f,
                  success: () => {
                    uni.showToast({
                      title: '图片保存成功！',
                      icon: 'none'
                    })
                  }
                })
              }
              if (type == 2) {
                console.log(f)
                wx.showShareImageMenu({
                  path: f,
                  success: () => {}
                })
              }
            },
            fail: res => {
              // 失败回调函数，res中包含错误信息
              console.error('生成海报失败', res)
            }
          })
        })
    },
    goDetail() {
      let item = this.info
      if (item.permission_status == '1') {
        if (item.type == 18) {
          this.$xh.push(
            'jintiku',
            `pages/chapterExercise/index?professional_id=${item.professional_id}&goods_id=${item.id}&total=${item.tiku_goods_details.question_num}&isfree=${this.isfree}`
          )
        }
        if (item.type == 10) {
          let url = `pages/modelExaminationCompetition/examInfo?product_id=${item.id}&title=${item.name}&page=home`
          if (!this.$xh.isInitName(url)) {
            setSubscribeMessage('examination')
            this.$xh.push('jintiku', url)
          }
        }
        if (item.type == 8) {
          this.$xh.push('jintiku', `pages/test/exam?id=${item.id}`)
        }
      }
    },
    isLogin() {
      return !!this.$store.state.jintiku.token
    },
    setParent(lists, prevLeval = 1) {
      lists?.forEach((item, i) => {
        item.leval = prevLeval
        if (item.child && item.child.length) {
          this.setParent(item.child, item.leval + 1)
        } else {
          // 判断是否有上一次练习
          if (item.section_type == '2' && item.is_checked == '1') {
            // 确认是知识点
            this.preData = item
          }
        }
      })
    },
    completepath(url) {
      return this.$xh.completepath(url)
    },
    getChapterpackageTree() {
      if (this.professional_id) {
        chapterpackageTree({
          professional_id: this.professional_id,
          goods_id: this.id
        }).then(data => {
          this.lists = this.filter(data?.data?.section_info)
          this.setParent(this.lists)
        })
      }
    },
    filter(list) {
      return list
        .filter(e => e.question_number != '0')
        .map(item => {
          if (item?.child?.length > 0) {
            item.child = this.filter(item?.child)
          }
          return item
        })
    },
    getGoodsDetail() {
      getGoodsDetail({
        goods_id: this.id
      }).then(res => {
        if (!Array.isArray(res.data.prices)) {
          res.data.prices = []
        }
        this.prices = res.data.prices
          .sort((a, b) => {
            if (a.month == 0) {
              return 1
            } else {
              return Number(a.month) - Number(b.month)
            }
          })
          .map(item => {
            let discount_price =
              Math.floor(
                (Number(item.original_price) - Number(item.sale_price)) * 100
              ) / 100
            return {
              ...item,
              discount_price
            }
          })
        this.isfree = 0
        if (!this.prices?.length) {
          this.isfree = 1
          this.prices = [
            {
              days: '0',
              goods_months_price_id: '',
              month: '0',
              original_price: '0.00',
              sale_price: '0.00',
              discount_price: '0.00'
            }
          ]
        }
        let info = res.data

        let num_text = `共${info.tiku_goods_details.question_num}题`
        if (info.type == 8) {
          num_text = `共${info.tiku_goods_details.paper_num}份`
        }
        if (info.type == 10) {
          num_text = `共${info.tiku_goods_details.exam_round_num}轮`
        }
        let system_id_name = info?.teaching_system?.system_id_name || ''
        if (info.type == 10) {
          system_id_name = `开考时间:${info.tiku_goods_details.exam_time}`
        }

        this.info = res.data
        this.info.professional_id = this.professional_id
        this.info.id = this.id
        this.info.num_text = num_text
        this.info.tips = system_id_name
        this.info.system_id_name =
          this.info?.teaching_system?.system_id_name || ''
        this.shareQrcode()
        if (res.data.type == 18) {
          this.getChapterpackageTree()
        }

        //
        if (res.data.type != 18) {
          let material_intro_path = this.info.material_intro_path
          let material_cover_path = this.info.material_cover_path
          //封面
          this.info.material_cover_path = material_intro_path
          //介绍
          this.info.material_intro_path = material_cover_path
        }
        if (this.info.material_cover_path) {
          this.info.material_cover_path = this.completepath(
            this.info.material_cover_path
          )
        } else {
          this.info.material_cover_path =
            'https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/datatpls/jintikutocshare.jpg'
        }

        //防止没有选专业
        let major = uni.getStorageSync('__xingyun_major__')
        if (!major.major_id) {
          let professional_id_name = this.info.professional_id_name.split('-')
          uni.setStorageSync('__xingyun_major__', {
            major_id: this.info.professional_id,
            major_name: professional_id_name[professional_id_name.length - 1]
          })
        }
      })
    },
    paySuccess() {
      // this.$xh.push('jintiku', `pages/order/paySuccess`)
    },
    //支付点击
    async getOrder() {
      if (this.isTap) {
        setTimeout(() => {
          this.isTap = false
        }, 3000)
        return
      }
      //登录后刷新
      this.shareQrcode()
      this.isTap = true
      //         flow_id: "555343665594113147"
      // is_supervise_order: 2
      // order_id: "555343665594178683"
      // pay_amount: 0.03
      // this.wechatapplet('555343665594113147')
      // return
      // 下单
      let payable_amount = this.prices[this.active].sale_price
      let student_id = uni.getStorageSync('__xingyun_userinfo__').student_id
      let goods_id = this.id
      let data = {
        business_scene: 1,
        goods: [
          {
            goods_id: goods_id,
            goods_months_price_id:
              this.prices[this.active].goods_months_price_id,
            months: this.prices[this.active].month,
            class_campus_id: '',
            class_city_id: '',
            goods_num: '1'
          }
        ],
        deposit_amount: Number(payable_amount),
        payable_amount: Number(payable_amount),
        real_amount: Number(payable_amount),
        remark: '',
        student_adddatas_id: '',
        student_id: student_id,
        total_amount: Number(payable_amount),
        app_id: app_id,
        pay_method: '',
        order_type: 10,
        discount_amount: 0,
        coupons_ids: [],
        employee_id:
          this.$store.state.jintiku.employee_id || '508948528815416786',
        // is_agent: 2,
        delivery_type: 1 // 默认总部邮寄

        // dept_id: '436047240159563069',
      }
      getOrderV2({
        ...data
      }).then(res => {
        //         flow_id: "555343665594113147"
        // is_supervise_order: 2
        // order_id: "555343665594178683"
        // pay_amount: 0.03
        if (Number(payable_amount) > 0) {
          this.getPayModeListNew({
            goods_id: goods_id,
            order_id: res.data.order_id,
            flow_id: res.data.flow_id
          })
        } else {
          this.getGoodsDetail()
          this.$xh.push(
            'jintiku',
            `pages/order/paySuccess` + `?goods_id=${this.id}`
          )
        }

        // this.wechatapplet(res.data.flow_id)
      })
    },
    getPayModeListNew(obj) {
      let userinfo = uni.getStorageSync('__xingyun_userinfo__') || {}
      payModeListNew({
        account_use: 1, // 1收款 2付款
        is_match: 1,
        is_usable: 1,
        page: 1,
        size: 100,
        account_type: 1, // 1:线上支付 2:线下支付
        order_id: obj.order_id,
        goods_ids: obj.goods_id,
        merchant_id: userinfo.merchant[0].merchant_id,
        brand_id: userinfo.merchant[0].brand_id,
        collection_scene: 1,
        collection_terminal: 8
      }).then(response => {
        console.log('财务主体列表', response?.data?.list)
        if (response?.data?.list?.length > 0) {
          let flag_row = response?.data?.list?.find(item => {
            return item?.pay_method == '6' && app_id == item?.wechat_pay_app_id
          })
          console.log(flag_row, 'flag_row')
          if (flag_row) {
            payModeListNewDetail({
              id: flag_row?.id
            }).then(value => {
              console.log('获取App_id', value?.data)
              this.wechatapplet(obj.flow_id, value?.data?.id)
            })
          }
        }
      })
    },
    wechatapplet(flow_id, finance_body_id) {
      let openid = uni.getStorageSync('__xingyun_weixinInfo__').openid
      wechatapplet({
        flow_id: flow_id,
        wechat_app_id: app_id,
        open_id: openid,
        finance_body_id: finance_body_id
      }).then(res => {
        const payParams = {
          appId: app_id,
          timeStamp: res.data.time_stamp,
          nonceStr: res.data.nonce_str,
          signType: res.data.sign_type,
          paySign: res.data.pay_sign,
          package: res.data.package,
          finance_body_id: finance_body_id
        }

        this.$xh.pay(
          payParams,
          () => {
            this.getGoodsDetail()
            this.$xh.push(
              'jintiku',
              `pages/order/paySuccess` +
                `?goods_id=${this.id}&professional_id_name=${this.info.professional_id_name}`
            )
            setSubscribeMessage('goods')
          },
          () => {
            this.$xh.Toast('支付失败！')
          }
        )
      })
    }
  },
  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function (res) {
    let userinfo = uni.getStorageSync('__xingyun_userinfo__')
    let employee_id = ``
    if (userinfo?.employee_info?.employee_id) {
      employee_id = `&employee_id=${userinfo?.employee_info?.employee_id}`
    }
    let path = `/modules/jintiku/pages/test/detail?id=${this.id}&professional_id=${this.professional_id}${employee_id}`
    console.log(path)
    return {
      title: this.info.name,
      imageUrl: this.info.material_cover_path,
      path
    }
  }
}
</script>

<style lang="scss" scoped>
.seckill-box {
  padding: 0 10px 10px 10px;
  background-color: red;
  position: relative;
  overflow: hidden;
  .tl {
    position: absolute;
    top: 0;
    left: 0;
    width: 87px;
    left: -10px;
    top: -10px;
    border-radius: 50%;
    height: 77px;
    background-image: radial-gradient(
      circle,
      #ff5d00,
      rgba($color: #ff5d00, $alpha: 0)
    );
  }
  .tr {
    position: absolute;
    width: 87px;
    right: -20px;
    top: -30px;
    border-radius: 50%;
    height: 77px;
    background-image: radial-gradient(
      circle,
      #ff5d00,
      rgba($color: #ff5d00, $alpha: 0)
    );
  }
  .price-text {
    position: relative;
    z-index: 1;
    width: 100%;
    // height: 45px;
    background-color: #ffffff;
    border-radius: 5px;
    display: flex;
    // align-items: center;
    justify-content: space-between;
    padding-top: 8px;
    padding-left: 56px;
    padding-right: 44px;
    padding-bottom: 8px;
    .ee {
      display: flex;
      flex-direction: column;
      align-items: center;
      view:nth-child(1) {
        font-size: 12px;
        color: red;
      }
      view:nth-child(2) {
        display: flex;
        align-items: baseline;
        color: #ff5430;
        text:nth-child(1) {
          font-weight: 400;
          font-size: 24rpx;
        }
        text:nth-child(2) {
          font-weight: 500;
          font-size: 34rpx;
        }
      }
    }
  }
  .t {
    position: relative;
    z-index: 1;
    height: 49px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .l {
      width: 37px;
      height: 39px;
      background-color: #df001a;
      border-radius: 5px;
      font-size: 14px;
      color: #ffffff;
      text-align: center;
    }
    .r {
      .text {
        text-align: end;
        font-size: 12px;
        color: #fed7c5;
        margin-bottom: 2px;
      }
    }
  }
}
.targetShare {
  position: fixed;
  top: 0;
  z-index: 1;
  // background: #ffffff;
  left: -100vw;
  .shareWebQrcode2 {
    width: 150px;
    height: 150px;
  }
  .share {
    background: #ffffff;
    width: 500rpx;
    .img {
      width: 500rpx;
      height: 270rpx;
    }

    .huaxian {
      font-weight: 800;
      font-size: 28rpx;
      color: #a3a3a3;
    }
    .price {
      display: flex;
      align-items: baseline;
      color: #ff5430;
      text:nth-child(1) {
        font-weight: 500;
        font-size: 28rpx;
      }
      text:nth-child(2) {
        font-weight: 800;
        font-size: 44rpx;
      }
    }
    .qrcode-box {
      justify-content: space-between;
      .tips {
        view {
          font-size: 24rpx;
          color: #a3a3a3;
        }
      }
    }
    .qrcode {
      width: 100px;
      height: 100px;
      // background-color: #f2f4f6;
    }

    .info-box {
      margin-top: -20rpx;
      width: 100%;
      // height: 102px;

      border-radius: 20rpx;
      padding: 30rpx 24rpx 24rpx 24rpx;
    }
    .name {
      font-weight: 600;
      font-size: 30rpx;
      color: #212121;
    }
    .tags {
      display: flex;
      margin-bottom: 24rpx;
      .ee {
        // width: 36px;
        margin-right: 12rpx;
        height: 34rpx;
        line-height: 34rpx;
        background: #f5f6fa;
        border-radius: 4rpx;
        padding: 0 12rpx;
        font-size: 20rpx;
        color: rgba(44, 55, 61, 0.71);
        margin-top: 16rpx;
      }
      .ee:nth-child(1) {
        background: #ebf1ff;
        color: #2e68ff;
      }
    }
  }
}

.chapter-info {
  margin-top: 20rpx;
  .chapter-list {
    width: 100%;
    background-color: #fff;
  }
  .header {
    width: 100%;
    height: 72rpx;
    background-size: 100% 72rpx;
    background-image: url(https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/3808173977504493854645_zhangjietishu.png);
    padding-left: 28rpx;
    display: flex;
    align-items: center;
    image {
      width: 28rpx;
      height: 28rpx;
    }
    text {
      font-weight: 500;
      font-size: 28rpx;
      color: #ffffff;
      margin-left: 12rpx;
    }
  }
}
.prices {
  // border-top: 1px solid #e8e9ea;
  // padding-top: 24rpx;
  display: flex;
  flex-wrap: wrap;
  // justify-content: space-between;
  .ee {
    margin-top: 24rpx;
    width: 222rpx;
    text-align: center;
    height: 64rpx;
    line-height: 64rpx;
    background: #f5f6fa;
    border-radius: 8rpx;
    font-weight: 400;
    font-size: 24rpx;
    color: rgba(3, 32, 61, 0.85);
    margin-right: 18rpx;
  }
  .ee:nth-child(3n) {
    margin-right: 0;
  }
  .active {
    background: #2e68ff;
    color: #ffffff;
  }
}
.pay-info {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 168rpx;
  background: #ffffff;
  box-shadow: 0px -1px 0px 0px rgba(240, 240, 240, 0.6);
  z-index: 1;
  display: flex;
  justify-content: space-between;
  padding: 0 32rpx 40rpx 40rpx;
  align-items: center;

  .price {
    display: flex;
    align-items: baseline;
    color: #ff5430;
    text:nth-child(1) {
      font-weight: 500;
      font-size: 28rpx;
    }
    text:nth-child(2) {
      font-weight: 800;
      font-size: 44rpx;
    }
  }
  .but {
    text-align: center;
    width: 476rpx;
    height: 80rpx;
    line-height: 80rpx;
    background: linear-gradient(163deg, #ff8928 0%, #ff5430 100%);
    box-shadow: 0px 2px 6px 0px rgba(255, 89, 50, 0.45);
    border-radius: 40rpx;
    font-weight: 500;
    font-size: 28rpx;
    color: #ffffff;
  }
}
.goods-info-img {
  width: 100%;
  // height: 515px;
  background: #ffffff;
  border-radius: 20rpx 20rpx 0 0;
  margin-top: 20rpx;
  border-radius: 20rpx;
  overflow: hidden;
  // padding-top: 24rpx;
  image {
    width: 100%;
  }
}
.goods-info {
  .img {
    width: 100%;
    height: 408rpx;
  }
  .info-box {
    // margin-top: -20rpx;
    position: relative;
    z-index: 1;
    width: 100%;
    // height: 102px;
    background: #ffffff;
    border-radius: 20rpx;
    padding: 24rpx;
    .name-box {
      display: flex;
      justify-content: space-between;
      .share-but {
        padding: 4px 8px;
        // margin-top: 24rpx;
        // width: 222rpx;
        text-align: center;
        // height: 64rpx;
        // line-height: 64rpx;
        background: #2e68ff;
        color: #ffffff;
        border-radius: 8rpx;
        font-weight: 400;
        font-size: 24rpx;
        margin-left: 18rpx;
      }
    }
  }
  .name {
    font-weight: 600;
    font-size: 30rpx;
    color: #212121;
  }
  .tags {
    display: flex;
    margin-bottom: 24rpx;
    .ee {
      // width: 36px;
      margin-right: 12rpx;
      height: 34rpx;
      line-height: 34rpx;
      background: #f5f6fa;
      border-radius: 4rpx;
      padding: 0 12rpx;
      font-size: 20rpx;
      color: rgba(44, 55, 61, 0.71);
      margin-top: 16rpx;
    }
    .ee:nth-child(1) {
      background: #ebf1ff;
      color: #2e68ff;
    }
  }
  .path {
    font-size: 24rpx;
    color: rgba(3, 32, 61, 0.65);
    padding-bottom: 16rpx;
  }
  .fen {
    border-bottom: 1px solid #e8e9ea;
  }
}
</style>
