<template>
  <view>
    <view class="info">
      <image
        v-if="info.material_intro_path"
        class="material_cover_path"
        :src="completepath(info.material_intro_path)"
      ></image>
      <view class="info-l">
        <view class="name">{{ info.name }}</view>
        <view class="tags">
          <view class="ee">{{ info.num_text }}</view>
          <view class="ee">{{ info.year }}</view>
          <view class="ee">共{{ info.tiku_goods_details.question_num }}题</view>
        </view>
      </view>
    </view>

    <view class="chapterpaperList" v-if="chapterpaperList.length">
      <view class="item" v-for="item of chapterpaperList">
        <view class="header"
          ><text>{{ item.name }}</text></view
        >
        <view class="body">
          <view
            class="ee"
            v-for="(ee, index) of item.list"
            @click="makeQuestion(ee, item)"
          >
            <view class="num"
              ><text>{{ num(index) }}</text>
              <view class="back"></view>
            </view>
            <view class="name">{{ ee.name }}</view>
            <view class="but" v-if="ee.paper_exercise_id == '0'">开始考试</view>
            <view class="but" v-if="ee.paper_exercise_id != '0'">查看报告</view>
          </view>
        </view>
      </view>

      <view style="height: 20rpx"></view>
    </view>

    <view class="paperList" v-if="paperList.length">
      <view class="item" v-for="item of paperList" @click="makeQuestion(item)">
        <view class="name">{{ item.name }}</view>
        <view class="but" v-if="item.paper_exercise_id == '0'">开始考试</view>
        <view class="but" v-if="item.paper_exercise_id != '0'">查看报告</view>
      </view>
      <view style="height: 20rpx"></view>
    </view>

    <kfQrcode></kfQrcode>

    <view style="height: 80rpx"></view>
  </view>
</template>

<script>
import {
  getGoodsDetail,
  getOrderV2,
  wechatapplet,
  payModeListNew,
  payModeListNewDetail,
  paper,
  chapterpaper
} from '../../api/index'

import login from '../../components/commen/login.vue'
import kfQrcode from '../../components/commen/kf-qrcode.vue'
export default {
  components: {
    login,
    kfQrcode
  },
  data() {
    return {
      id: '',
      professional_id: '',
      active: 0,
      prices: [{}],
      info: {
        type: 0,
        tiku_goods_details: {}
      },
      lists: [],
      chapterpaperList: [],
      paperList: []
    }
  },
  onLoad(query) {
    this.id = query.id
    this.professional_id = query.professional_id
  },
  onShow() {
    this.getGoodsDetail()
  },
  methods: {
    num(index) {
      let n = index + 1
      return n ? '0' + n : n
    },
    isLogin() {
      return !!this.$store.state.jintiku.token
    },
    completepath(url) {
      return this.$xh.completepath(url)
    },
    makeQuestion(ee, item) {
      // ee.paper_exercise_id = '0'
      if (ee.paper_exercise_id == '0') {
        this.$xh.push(
          'jintiku',
          `pages/examination/test?paper_version_id=${ee.id}&goods_id=${
            this.id
          }&order_id=${
            this.info.permission_order_id || 'order_id'
          }&sub_order_id=${'ddd'}&title=${ee.name}&professional_id=${
            this.info.professional_id
          }&type=8&time=${3600 * 2}`
        )
      }
      if (ee.paper_exercise_id != '0') {
        this.$xh.push(
          'jintiku',
          `pages/test/examScoreReporting?professional_id=${this.info.professional_id}&paper_version_id=${ee.id}&order_id=${this.info.permission_order_id}&goods_id=${this.info.id}&title=${ee.name}`
        )
      }
    },
    getPaper() {
      if (!this.isLogin) {
        return
      }
      if (this.info.data_type == '3') {
        chapterpaper({
          id: this.id,
          order_id: this.info.permission_order_id
        }).then(res => {
          this.chapterpaperList = res.data
        })
      }
      if (this.info.data_type == '1') {
        paper({
          id: this.id,
          order_id: this.info.permission_order_id
        }).then(res => {
          this.paperList = res.data
        })
      }
    },
    getGoodsDetail() {
      getGoodsDetail({
        goods_id: this.id
      }).then(res => {
        if (!Array.isArray(res.data.prices)) {
          res.data.prices = []
        }
        this.prices = res.data.prices?.sort((a, b) => {
          if (a.month == 0) {
            return 1
          } else {
            return Number(a.month) - Number(b.month)
          }
        })
        let info = res.data
        let num_text = `共${info.tiku_goods_details.question_num}题`
        if (info.type == 8) {
          num_text = `共${info.tiku_goods_details.paper_num}份`
        }
        if (info.type == 10) {
          num_text = `共${info.tiku_goods_details.exam_round_num}轮`
        }
        let system_id_name = info?.teaching_system?.system_id_name || ''
        if (info.type == 10) {
          system_id_name = `开考时间:${info.tiku_goods_details.exam_time}`
        }

        this.info = res.data
        this.info.num_text = num_text
        this.info.tips = system_id_name
        this.info.system_id_name =
          this.info?.teaching_system?.system_id_name || ''

        if (this.info.permission_operation_type == '2') {
          this.info.permission_order_id = '1'
        }

        if (this.info.permission_status == '1') {
          if (res.data.type == 18) {
            this.getChapterpackageTree()
          } else if (res.data.type == 8) {
            this.getPaper()
          }
        }
      })
    }
  },
  onShareAppMessage() {
    return this.$xh.shareAppMessage()
  }
}
</script>

<style lang="scss" scoped>
.paperList {
  .item {
    margin: 24rpx 24rpx 0 24rpx;
    padding: 32rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: #fff;
    border-radius: 12px;
    box-shadow: 0 0 30rpx rgba(27, 38, 55, 0.06);
    overflow: hidden;
    .name {
      font-weight: 600;
      font-size: 28rpx;
      color: #262629;
    }
    .but {
      margin-left: auto;
      width: 160rpx;
      text-align: center;
      height: 56rpx;
      line-height: 56rpx;
      background: #2e68ff;
      border-radius: 64rpx;

      font-weight: 400;
      font-size: 28rpx;
      color: #ffffff;
      flex-shrink: 0;
    }
  }
}
.chapterpaperList {
  .item {
    background: linear-gradient(180deg, #ecf3ff 0%, #fafcff 43%, #ffffff 100%);
    border-image: linear-gradient(
        180deg,
        rgba(75, 145, 245, 0.32),
        rgba(255, 255, 255, 1)
      )
      2 2;
    margin: 24rpx 24rpx 0rpx 24rpx;
    border-radius: 12rpx;
    overflow: hidden;
    .body {
      background: #ffffff;
      .ee {
        height: 118rpx;
        margin: 0 28rpx;
        display: flex;
        align-items: center;
        border-bottom: 1px solid #e8e9ea;
        .num {
          width: 64rpx;
          font-weight: 600;
          font-size: 36rpx;
          color: #424b57;
          position: relative;

          .back {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 44rpx;
            height: 14rpx;
            background: linear-gradient(
              270deg,
              rgba(255, 195, 30, 0) 0%,
              #ff900d 100%
            );
            background-size: 44rpx 14rpx;
          }
        }
        .name {
          font-weight: 600;
          font-size: 28rpx;
          color: #262629;
        }
        .but {
          margin-left: auto;
          width: 160rpx;
          text-align: center;
          height: 56rpx;
          line-height: 56rpx;
          background: #2e68ff;
          border-radius: 64rpx;
          font-weight: 400;
          font-size: 28rpx;
          color: #ffffff;
          flex-shrink: 0;
        }
      }
      .ee:nth-last-child(1) {
        border-bottom: none;
      }
    }
    .header {
      // background: linear-gradient(
      //   180deg,
      //   #ecf3ff 0%,
      //   #fafcff 43%,
      //   #ffffff 100%
      // );
      // background-size: 100% 346rpx;
      // height: 106rpx;
      padding: 32rpx;
      .text {
        font-weight: 500;
        font-size: 30rpx;
        color: #262629;
      }
    }
  }
}
.info {
  padding: 32rpx;
  background-color: #fff;
  display: flex;
  .info-l {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    .name {
      margin-bottom: 24rpx;
    }
  }
  .material_cover_path {
    width: 100rpx;
    height: 100rpx;
    margin-right: 24rpx;
  }
}
.tags {
  display: flex;
  // margin-bottom: 24rpx;
  .ee {
    // width: 36px;
    margin-right: 12rpx;
    height: 34rpx;
    line-height: 34rpx;
    background: #f5f6fa;
    border-radius: 4rpx;
    padding: 0 12rpx;
    font-size: 20rpx;
    color: rgba(44, 55, 61, 0.71);
    margin-top: 16rpx;
  }
  .ee:nth-child(1) {
    background: #ebf1ff;
    color: #2e68ff;
  }
}
</style>
