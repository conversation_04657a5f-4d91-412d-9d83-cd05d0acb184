<template>
  <view class="wrong_question_details">
    <head-height :statusBarHeight.sync="statusBarHeight" />
    <!-- 顶部功能预览 -->
    <view class="priview-time" :style="{ top: statusBarHeight + 'px' }">
      <image
        src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/169509193147644ed169509193147782088_back.png"
        mode="widthFix"
        class="back"
        @click="back"
      />
      <view class="nums">
        <text>{{ current + 1 }}</text>
        <text>/{{ lists.length }}</text>
      </view>
    </view>
    <view class="uni-margin-wrap">
      <swiper
        class="swiper"
        :indicator-dots="false"
        :autoplay="false"
        :current="current"
        :duration="300"
        :circular="false"
        :disable-touch="false"
        :skip-hidden-item-layout="false"
        :key="isReview"
        @change="swiperChange"
        :style="{ height: swiperHeight * 2 + 'rpx' }"
      >
        <swiper-item v-for="(item, index) in lists" :key="index">
          <view :id="'height_calculate' + index" style="padding-bottom: 130rpx">
            <select-question
              v-for="(jtem, j) in item.stem_list"
              :key="j"
              :info="item"
              :stem="jtem"
              @selecte="selecte"
              :answer="isReview == 'false'"
              :questionNumber="index + 1"
            />
            <view v-if="isReview == 'false' || item.isFinish">
              <view
                class="isCorrect"
                v-if="isReview == 'true' && item.user_option && item.type != 8"
              >
                <view
                  class="is_right_button correct"
                  :class="{
                    correct: item.answer_status == 1,
                    error: item.answer_status == 2,
                    half: item.answer_status == 3
                  }"
                >
                  {{ getStateText(item.answer_status) }}
                </view>
              </view>
              <view class="answer" v-if="item.type != 8">
                <view
                  class="blue_text"
                  v-if="item.answer_status != 1"
                  style="margin-bottom: 24rpx"
                >
                  正确答案：
                  {{ transformAnswer(item.stem_list[0].answer).join('、') }}
                </view>
                <view style="color: #000000" v-if="item.user_option">
                  你的答案：
                  {{ transformAnswer(item.user_option).join('、') }}
                </view>
              </view>
              <view class="explain_box" style="margin-top: 40rpx">
                <view class="explain"> 解析： </view>
                <view v-html="item.parse" class="content" />
              </view>
              <view v-if="item.parse && item.parse.includes('audio')">
                <view
                  class="audio"
                  v-for="(src, a) in getAudioAll(item.parse)"
                  :key="a"
                >
                  <my-audio
                    style="text-align: left"
                    :music="src"
                    name="解析"
                    controls
                  ></my-audio>
                </view>
              </view>
              <view class="explain_box">
                <view class="explain"> 知识点： </view>
                <view v-html="item.knowledge_ids_name" class="content" />
              </view>
              <view class="explain_box">
                <view class="explain"> 难易度： </view>
                <view class="star">
                  <view
                    v-for="citem in 5"
                    :key="citem"
                    style="margin-right: 9rpx"
                  >
                    <img
                      src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/16953700953269c48169537009532642625_%E7%BC%96%E7%BB%84%E5%A4%87%E4%BB%BD%205%402x.png"
                      alt=""
                      v-if="citem < item.level"
                    />
                    <img
                      src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/16953700763015ff616953700763011336_Fill%202%402x.png"
                      alt=""
                      v-else
                    />
                  </view>
                </view>
              </view>
            </view>
            <view class="error_button" @click="onErrorClick(item)">
              试题纠错
            </view>
          </view>
        </swiper-item>
      </swiper>
      <view class="footer_button">
        <view
          class="button out"
          v-if="isReview == 'false'"
          @click="onRemoveClick"
          >移出</view
        >
        <view
          class="button sign"
          v-if="isReview == 'true'"
          @click="onAnalysisClick"
        >
          查看解析
        </view>
        <view
          class="button sign"
          v-if="isReview == 'false' && lists[current].is_mark == '2'"
          @click="onMarkClick(false)"
          >标记</view
        >
        <view
          class="button sign"
          v-if="isReview == 'false' && lists[current].is_mark == '1'"
          @click="onMarkClick(true)"
          >取消标记</view
        >
      </view>
      <error-correction
        v-model="errorModule"
        :question_id="question_id"
        :question_version_id="question_version_id"
        :version="version"
      />
      <filtrate-select
        title="选择标签"
        :selectList="selectList"
        @close="filtrateModule = false"
        v-model="filtrateModule"
        isMultiple
        @success="onSuccessClick"
      />
    </view>
  </view>
</template>
<script>
import { setQuestionLists, getAudioAll } from '../../utils/index.js'
import {
  wronganswerbookMark,
  wronganswerbookRemove
} from '../../api/wrongQuestionBook'
import errorCorrection from '../../components/makeQuestion/error-correction.vue'
// import selectQuestion from '../../components/select-question.vue'
import selectQuestion from '../../components/makeQuestion/select-question.vue'
import filtrateSelect from '../../components/collect/filtrate-select.vue'
import headHeight from '../../components/commen/head-height.vue'
import myAudio from '../../components/commen/my-audio.vue'
export default {
  name: 'wrong-question-details',
  components: {
    errorCorrection,
    selectQuestion,
    filtrateSelect,
    headHeight,
    myAudio
  },
  props: {},
  data() {
    return {
      statusBarHeight: 0,
      lists: [],
      current: 0,
      transformList: [
        'A',
        'B',
        'C',
        'D',
        'E',
        'F',
        'G',
        'H',
        'I',
        'J',
        'K',
        'L',
        'M',
        'N',
        'O',
        'P',
        'Q',
        'R',
        'S',
        'T',
        'U',
        'V',
        'W',
        'X',
        'Y',
        'Z'
      ],
      errorModule: false,
      isReview: false,
      filtrateModule: false,
      selectList: [
        {
          id: '思路错误',
          label: '思路错误'
        },
        {
          id: '理解错误',
          label: '理解错误'
        },
        {
          id: '概念不清',
          label: '概念不清'
        },
        {
          id: '审题不清',
          label: '审题不清'
        }
      ],
      question_id: '',
      question_version_id: '',
      version: '',
      swiperHeight: 0
    }
  },
  onLoad(e) {
    this.isReview = e.isReview
    // this.isFinish = !e.isReview
  },
  methods: {
    init() {
      // let allData = JSON.parse(uni.getStorageSync('dataInfo')).listInfo
      let tmpData = JSON.parse(uni.getStorageSync('dataInfo')).listInfo.map(
        res => {
          return setQuestionLists(
            res.question_list?.map(item => {
              return {
                ...item,
                isFinish: false
              }
            })
          )
        }
      )
      let tmpItem = []
      tmpData.map(item => {
        item.map(citem => {
          tmpItem.push(citem)
        })
      })
      this.lists = tmpItem
      this.$nextTick(() => {
        this.setSwiperHeight()
      })
    },
    swiperChange(e) {
      this.current = e.detail.current
      this.$nextTick(() => {
        this.setSwiperHeight()
      })
    },
    getStateText(type) {
      switch (type) {
        case '1':
          return '正确'
        case '2':
          return '错误'
        case '3':
          return '半对'
      }
    },
    selecte(info) {
      if (this.isReview == 'true') {
        let isNext = false
        this.lists = this.lists.map(res => {
          if (res.sub_question_id == info.sub_question_id) {
            isNext = info.stem_list[0].multiple
            let tmpNumber = info.stem_list[0].answer.length
            let tmpOption = JSON.parse(
              JSON.stringify(info.user_option.split(','))
            ).sort()
            if (isNext) {
              if (!info.isFinish) {
                info.isFinish = tmpOption.length == tmpNumber
              }
            } else {
              info.isFinish = true
            }
            info.answer_status =
              tmpOption.join() == info.stem_list[0].answer.join() ? '1' : '2'
            return { ...info, doubt: false }
          }
          return res
        })
        if (!isNext) {
          this.next()
        }
      }
    },
    next() {
      if (this.current >= this.lists.length - 1) {
        this.$xh.Toast('已经是最后一题了哦！')
        return
      }
      this.current++
    },
    transformAnswer(item) {
      if (typeof item == 'string') {
        item = item.split(',')
      }
      let tmpArray = JSON.parse(JSON.stringify(item)).sort()
      return tmpArray.map(res => {
        return this.transformList[res]
      })
    },
    onAnalysisClick() {
      this.lists[this.current].isFinish = true
      this.$nextTick(() => {
        this.setSwiperHeight()
      })
    },
    onMarkClick(type) {
      if (type) {
        let data = {
          action_type: '2',
          mark_tab: '1',
          wrong_answer_book_id: this.lists[this.current].wrong_answer_book_id
        }
        wronganswerbookMark(data).then(res => {
          this.$xh.Toast('操作成功')
          this.lists[this.current].is_mark = '2'
        })
      } else {
        this.filtrateModule = true
      }
    },
    onSuccessClick(e) {
      let data = {
        action_type: '1',
        mark_tab: e.join(','),
        wrong_answer_book_id: this.lists[this.current].wrong_answer_book_id
      }
      wronganswerbookMark(data).then(res => {
        this.$xh.Toast('操作成功')
        this.lists[this.current].is_mark = '1'
      })
    },
    onRemoveClick() {
      wronganswerbookRemove({
        wrong_answer_book_id: this.lists[this.current].wrong_answer_book_id
      }).then(res => {
        this.$xh.Toast('操作成功')
      })
    },
    onErrorClick(data) {
      this.question_version_id = data.stem_list[0].question_version_id
      this.question_id = data.stem_list[0].id
      this.version = data.version
      this.errorModule = true
    },
    back() {
      uni.navigateBack({
        delta: 1
      })
    },
    setSwiperHeight() {
      let element = '#height_calculate' + this.current
      let query = uni.createSelectorQuery().in(this)
      query.select(element).boundingClientRect()
      query.exec(res => {
        if (res && res[0]) {
          this.swiperHeight = res[0].height
        }
      })
    },
    getAudioAll: getAudioAll
  },
  onShow() {
    this.init()
  },
  onShareAppMessage() {
    return this.$xh.shareAppMessage()
  }
}
</script>
<style lang="less" scoped>
.wrong_question_details {
  background: #fff;
  .priview-time {
    position: fixed;
    left: 0;
    top: 140rpx;
    width: 100vw;
    height: 96rpx;
    background-color: #fff;
    border-bottom: 1px solid #eee;
    display: flex;
    align-items: center;
    // justify-content: space-between;
    justify-content: center;
    padding: 0 40rpx;
    z-index: 11;
    .nums {
      text {
        font-size: 32rpx;
        color: #000000;
      }
      text:last-child {
        color: #949494;
      }
    }
    .time {
      text {
        font-size: 32rpx;
        color: #000000;
      }
    }
    .back {
      position: absolute;
      width: 19rpx;
      height: 32rpx;
      left: 30rpx;
      top: 0;
      bottom: 0;
      margin: auto 0;
    }
    .error {
      position: absolute;
      left: 80rpx;
      top: 0;
      bottom: 0;
      margin: auto 0;
      width: 120rpx;
      height: 44rpx;
      text-align: center;
      line-height: 44rpx;
      background: #f5f5f5;
      border-radius: 8rpx;
      font-size: 24rpx;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 800;
      color: #999999;
    }
  }
  .uni-margin-wrap {
    width: calc(100vw);
    height: calc(100vh - 100rpx);
    overflow-y: scroll;
    padding-bottom: calc(80rpx + 42rpx + 36rpx);
    box-sizing: border-box;
  }
  .swiper {
    padding: 0 24rpx;
    padding-top: 120rpx;
  }
  .title {
    color: #000000;
    font-size: 32rpx;
    // display: flex;
    // justify-content: flex-start;
    .thematic_stem {
      display: flex;
      justify-content: flex-start;
    }
    .type_name {
      color: #2e68ff;
      margin-right: 6rpx;
    }
  }
  .select_content {
    color: #000000;
    font-size: 32rpx;
    margin-top: 52rpx;
    .select {
      display: flex;
      justify-content: flex-start;
      margin-top: 58rpx;
    }
  }
  .isCorrect {
    margin-top: 48rpx;
    display: flex;
    justify-content: flex-start;
    .is_right_button {
      // padding: 22rpx 14rpx;
      width: 100rpx;
      height: 56rpx;
      text-align: center;
      line-height: 56rpx;
      border-radius: 8rpx;
      color: #ffffff;
      margin-right: 20rpx;
      margin-right: 40rpx;
    }
    .correct {
      background: #2e68ff;
    }
    .error {
      background: #f04f54;
    }
    .half {
      background: #f99300;
    }
    .score {
      margin-right: 20rpx;
    }
    .error_score {
      color: #f04f54;
    }
    .blue_text {
      color: #2e68ff;
    }
    .red_text {
      color: #f04f54;
    }
    .yellow_text {
      color: #f99300;
    }
  }
  .answer {
    margin: 24rpx 0 25rpx 0;
    font-size: 28rpx;
    .blue_text {
      color: #2e68ff;
    }
  }
  .explain_box {
    display: flex;
    justify-content: flex-start;
    align-items: flex-start;
    font-size: 26rpx;
    flex-wrap: wrap;

    .explain {
      color: #161f30;
      font-weight: 500;
      line-height: 52rpx;
    }
    .content {
      color: #161f30;
      line-height: 52rpx;
      flex: 1;
      word-break: break-all;
    }
    .star {
      // margin-left: auto;
      display: flex;
      justify-content: flex-start;
      align-items: flex-start;
      margin-top: 10rpx;
      img {
        width: 24rpx;
        height: 24rpx;
      }
      img:last-child {
        margin-right: 0;
      }
    }
  }
  .error_button {
    font-size: 28rpx;
    color: #2e68ff;
    margin-top: 40rpx;
  }
  .footer_button {
    // margin-top: 82rpx;
    width: 100%;
    font-size: 24rpx;
    display: flex;
    justify-content: center;
    position: fixed;
    bottom: 60rpx;
    .button {
      width: 280rpx;
      height: 68rpx;
      line-height: 68rpx;
      text-align: center;

      border-radius: 32rpx;
      font-weight: 400;
    }
    .out {
      color: #2e68ff;
      border: 2rpx solid rgba(46, 104, 255, 0.5);
      background: #ebf1ff;
      margin-right: 70rpx;
    }
    .sign {
      background: #2e68ff;
      color: #fff;
    }
  }
  .next {
    .btn {
      border-radius: 40rpx;
      border: 1px solid transparent;
      color: #fff;
      background: linear-gradient(270deg, #6d7dff 0%, #b3a1ff 100%);
    }
  }
}
</style>
