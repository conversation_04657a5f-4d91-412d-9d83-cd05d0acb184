<template>
  <view class="wrong_question_book">
    <view class="header_tab">
      <view class="left_tab">
        <view
          class="tab_name"
          @click="handleTabClick(0)"
          :class="{ init: current == 0 }"
        >
          全部
        </view>
        <view
          class="tab_name"
          @click="handleTabClick(1)"
          :class="{ init: current == 1 }"
          >标记</view
        >
        <view
          class="tab_name"
          @click="handleTabClick(2)"
          :class="{ init: current == 2 }"
        >
          易错
        </view>
      </view>
      <view class="right_screen">
        <view class="screen_button" @click="showTime = true">筛选</view>
        <img
          src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/16968447581417f1c16968447581417423_%E7%BC%96%E7%BB%84%2013%402x.png"
          alt=""
          class="screen_img"
        />
      </view>
    </view>
    <!-- <view class="content_box"> -->
    <swiper
      class="swiper"
      :indicator-dots="false"
      :autoplay="false"
      :current="current"
      :duration="300"
      :circular="false"
      :disable-touch="false"
      :skip-hidden-item-layout="false"
      @change="swiperChange"
      :style="{ height: swiperHeight * 2 + 'rpx' }"
    >
      <swiper-item v-for="(t, d) in lists" :key="d">
        <view
          :id="'height_calculate' + d"
          style="padding-bottom: 130rpx"
          v-if="t.listInfo"
        >
          <view v-for="(item, index) in t.listInfo" :key="index">
            <view class="headline">
              {{ item.question_type_name
              }}{{
                item.question_type != '8' &&
                item.question_type != '9' &&
                item.question_type != '10' &&
                item.question_type != '11'
                  ? '题'
                  : ''
              }}
              共{{ item.question_num }}道
            </view>
            <view
              class="content"
              v-for="(citem, cindex) in item.question_list"
              :key="cindex"
              @click="goDetail(citem, false)"
            >
              <view
                class="question_content"
                v-for="(jitem, jindex) in citem.stem_list"
                :key="jindex"
              >
                <view
                  v-if="citem.thematic_stem"
                  style="display: flex; justify-content: start"
                >
                  <view style="white-space: nowrap">病例：</view>
                  <view class="title" v-html="citem.thematic_stem" />
                </view>
                <view
                  style="display: flex; justify-content: start"
                  v-else-if="!citem.thematic_stem && !isB1(citem.type)"
                >
                  <view>{{ cindex + 1 }}、</view>
                  <view
                    class="title"
                    v-html="jitem.content"
                    style="word-wrap: break-word; width: auto; overflow: hidden"
                  />
                </view>
                <!--  -->
                <view class="options_title" v-if="isB1(citem.type)">
                  <view
                    class="option_item"
                    v-for="(option, idx) in JSON.parse(jitem.option)"
                    s
                    :key="idx"
                  >
                    <view>{{ transformList[idx] }}、</view>
                    <view v-html="option"></view>
                  </view>
                </view>
                <view class="time">{{
                  citem.created_at_val.substring(0, 16)
                }}</view>
                <!-- <view class="synopsis"> -->
                <view class="synopsis" v-if="citem.tags">
                  <view
                    class="label"
                    v-for="(tag, tagidx) in citem.tags.split(',')"
                    :key="tagidx"
                    >{{ tag }}</view
                  >
                </view>
                <view class="star">
                  <text class="level_name">难易度：</text>
                  <view
                    v-for="sitem in 5"
                    :key="sitem"
                    style="margin-right: 9rpx"
                  >
                    <img
                      src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/16953700953269c48169537009532642625_%E7%BC%96%E7%BB%84%E5%A4%87%E4%BB%BD%205%402x.png"
                      alt=""
                      v-if="sitem < citem.level"
                    />
                    <img
                      src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/16953700763015ff616953700763011336_Fill%202%402x.png"
                      alt=""
                      v-else
                    />
                  </view>
                </view>
                <!-- </view> -->
              </view>
              <img
                src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/16976826428496323169768264284940885_%E7%BC%96%E7%BB%84%205%402x%20(1).png"
                alt=""
                class="fallibilitv_img"
                v-show="citem.is_fallibilitv == 1"
              />
              <img
                src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/1697682633668467716976826336686294_%E7%BC%96%E7%BB%84%402x%20(2).png"
                alt=""
                class="mark_img"
                v-show="citem.is_mark == 1"
              />
            </view>
          </view>
          <view class="finsh">没有更多啦~</view>
        </view>
        <view v-else class="placeholder_box">
          <view class="cnt">
            <img
              src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/16975967239088c80169759672390853792_%E7%BC%96%E7%BB%84%205%402x.png"
              alt=""
            />
            <p>暂无错题~</p>
          </view>
        </view>
      </swiper-item>
    </swiper>
    <view class="footer_button" v-if="lists[0].listInfo.length">
      <view class="button" @click="goDetail(lists[0], true)"> 错题复查 </view>
    </view>
    <!-- </view> -->
    <!-- <filtrate-select
      selectName="选择时间范围"
      :selectList="selectList"
      @select="onFiltrate"
      @close="filtrateModule = false"
      :moduleVlaue="filtrateModule"
    /> -->
    <select-time-range
      v-model="showTime"
      :type.sync="search.time_range"
      :start_date.sync="search.start_date"
      :end_date.sync="search.end_date"
      :selectList="selectList"
      @success="success"
    ></select-time-range>
  </view>
</template>
<script>
// import filtrateSelect from '../../components//makeQuestion/filtrate-select'
import { getWronganswerbook } from '../../api//wrongQuestionBook'
import selectTimeRange from '../../components/collect/select-time-range.vue'
import { isB1 } from '../../utils/index'
export default {
  name: 'wrong-question-book',
  components: {
    // filtrateSelect
    selectTimeRange
  },
  data() {
    return {
      transformList: [
        'A',
        'B',
        'C',
        'D',
        'E',
        'F',
        'G',
        'H',
        'I',
        'J',
        'K',
        'L',
        'M',
        'N',
        'O',
        'P',
        'Q',
        'R',
        'S',
        'T',
        'U',
        'V',
        'W',
        'X',
        'Y',
        'Z'
      ],
      showTime: false,
      swiperHeight: '',
      filtrateModule: false,
      current: 0,
      lists: [],
      selectList: [
        {
          id: '0',
          label: '全部'
        },
        {
          id: '1',
          label: '近3天'
        },
        {
          id: '2',
          label: '一周内'
        },
        {
          id: '3',
          label: '一月内'
        },
        {
          id: '4',
          label: '自定义'
        }
      ],
      search: {
        time_range: '0',
        start_date: '',
        end_date: ''
      }
    }
  },
  // onLoad(e) {
  //   this.init()
  // },
  onShow() {
    this.init()
  },

  methods: {
    isB1,
    init() {
      // this.searchFn()
      this.lists = []
      // let tmpData = []
      let data = {
        ...this.search,
        time_range: this.search.time_range
      }
      getWronganswerbook({ data_type: 1, ...data }).then(res => {
        this.lists.push({ listInfo: res.data })
        this.$nextTick(() => {
          this.setSwiperHeight()
        })
        getWronganswerbook({ data_type: 2, ...data }).then(res => {
          this.lists.push({ listInfo: res.data })
          getWronganswerbook({ data_type: 3, ...data }).then(res => {
            this.lists.push({ listInfo: res.data })
          })
        })
        // this.lists = tmpData
      })
    },
    searchFn() {
      this.lists[this.current] = {}
      let data = {
        ...this.search,
        time_range: this.search.time_range
      }
      getWronganswerbook({ data_type: this.current + 1, ...data }).then(res => {
        this.lists[this.current] = { listInfo: res.data }
        // this.$nextTick(() => {
        // this.$set(this.lists[this.current], 'listInfo', res.data)
        // })
        console.log(this.lists[this.current])
        this.$nextTick(() => {
          this.$nextTick(() => {
            this.setSwiperHeight()
          })
        })
        this.$forceUpdate()
      })
    },
    success(e) {
      this.searchFn()
    },
    swiperChange(e) {
      this.current = e.detail.current
      this.searchFn()
      // this.$nextTick(() => {
      //   this.setSwiperHeight()
      // })
      // this.getSwiper(e.currentTarget.dataset.i)
      // this.getSwiper()
    },
    handleTabClick(index) {
      this.current = index
    },
    goDetail(data, type) {
      let url = 'pages/wrongQuestionBook/wrongQuestionDetails'
      this.$xh.push('jintiku', `${url}?isReview=${type}`)
      if (type) {
        let tmpData = JSON.stringify(data)
        uni.setStorageSync('dataInfo', tmpData)
      } else {
        let listInfo = { listInfo: [{ question_list: [data] }] }
        let tmpData = JSON.stringify(listInfo)
        uni.setStorageSync('dataInfo', tmpData)
      }
    },
    setSwiperHeight() {
      let element = '#height_calculate' + this.current
      let query = uni.createSelectorQuery().in(this)
      query.select(element).boundingClientRect()
      query.exec(res => {
        if (res && res[0]) {
          this.swiperHeight = res[0].height
        } else {
          this.swiperHeight = '700'
        }
      })
    }
  },
  onPullDownRefresh() {
    this.success()
  },
  onShareAppMessage() {
    return this.$xh.shareAppMessage()
  }
}
</script>
<style lang="less" scoped>
.wrong_question_book {
  width: 100%;
  min-height: 100vh;
  // background: #fff;
  .header_tab {
    width: 100%;
    position: fixed;
    top: 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16rpx 24rpx;
    font-size: 32rpx;
    color: #262629;
    font-weight: 400;
    background: #fff;
    z-index: 11;
    .left_tab {
      display: flex;
      justify-content: flex-start;
    }
    .tab_name {
      margin-right: 60rpx;
    }
    .right_screen {
      display: flex;
      justify-content: flex-start;
      .screen_button {
        font-size: 28rpx;
        color: #03203d;
      }
      .screen_img {
        width: 24rpx;
        height: 24rpx;
        margin: 8rpx 0 0 8rpx;
      }
    }
    .init {
      color: #387dfc;
    }
  }
  // .content_box {
  //   width: 100%;
  //   height: 100%;
  //   position: absolute;
  .swiper {
    margin-top: 80rpx;
    padding: 32rpx 24rpx;
    .content {
      margin-bottom: 24rpx;
      position: relative;
      .headline {
        font-size: 30rpx;
        color: #161f30;
        font-weight: 500;
      }
      .question_content {
        padding: 30rpx 32rpx;
        background: #fff;
        border-radius: 12rpx;
        margin-top: 24rpx;
        .title {
          font-size: 30rpx;
          color: #212121;
          font-weight: 600;
        }
        .options_title {
          display: grid;
          grid-template-columns: repeat(2, 1fr);
          .option_item {
            display: flex;
          }
        }
        .time {
          font-size: 24rpx;
          color: #898a8d;
          font-weight: 400;
          margin: 22rpx 0 24rpx;
        }
        .synopsis {
          display: flex;
          .label {
            font-size: 20rpx;
            border-radius: 4rpx;
            background: #ebf1ff;
            padding: 8rpx 16rpx;
            color: #2e68ff;
            margin-right: 10rpx;
          }
        }
        .star {
          display: flex;
          justify-content: flex-start;
          align-items: flex-start;
          margin-top: 24rpx;
          margin-left: auto;
          .level_name {
            font-size: 24rpx;
            color: #787e8f;
          }
          img {
            width: 24rpx;
            height: 24rpx;
          }
          img:last-child {
            margin-right: 0;
          }
        }
      }
      .mark_img {
        width: 32rpx;
        height: 36rpx;
        position: absolute;
        top: 0;
        right: 20rpx;
      }
      .fallibilitv_img {
        width: 68rpx;
        height: 54rpx;
        position: absolute;
        top: 0;
        left: 0;
      }
    }
  }
  // }
  .footer_button {
    width: 100%;
    .button {
      width: 600rpx;
      height: 72rpx;
      line-height: 72rpx;
      text-align: center;
      color: #fff;
      background: #2e68ff;
      border-radius: 34rpx;
      margin: 0 auto;
    }
    position: fixed;
    bottom: 60rpx;
  }
  .placeholder_box {
    padding-top: 180rpx;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    .cnt {
      img {
        width: 300rpx;
        height: 220rpx;
      }
      p {
        text-align: center;
        color: #787e8f;
        font-size: 32rpxs;
        margin-top: 40rpx;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
      }
    }
  }
  .finsh {
    line-height: 80rpx;
    text-align: center;
    font-size: 24rpx;
    color: #ccc;
    width: 100%;
  }
}
</style>
