<template>
  <view class="question-index">
    <view class="nav-bar"></view>
    <!-- 专业切换 -->
    <view class="header-box">
      <view class="header">
        <view class="major flex-center" @click="selectMajorFn">
          <text>{{ major_name }}</text>
          <image
            src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/16986561288221b15169865612882259776_select.png"
            mode="widthFix"
          />
        </view>
      </view>
    </view>
    <view class="content">
      <!-- <index-nav ref="indexNav" page_type="test"></index-nav> -->

      <template v-if="goodsList10.length">
        <view class="title">
          <view class="line"></view>
          <text>模拟考试 </text>
        </view>
        <view v-if="isLogin()">
          <!-- <examination-list
            :data="testLists"
            :isShowNav="false"
          ></examination-list> -->
          <examination-test-list
            :data="goodsList10"
            :isPay="true"
          ></examination-test-list>
        </view>
        <view class="not_data" v-else>
          <view class="img">
            <img
              src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/16954369620338446169543696203498545_%E7%BC%96%E7%BB%84%402x%20(4).png"
              alt=""
            />
          </view>
          <view class="desc">登录后查看相关测验哦！</view>
          <view
            class="button goLogin"
            @click="goDetail('pages/loginCenter/index')"
          >
            去登录
          </view>
        </view>
      </template>

      <template>
        <view class="title">
          <view class="line"></view>
          <text>测验</text>
        </view>
        <view v-if="isLogin()">
          <examination-test-list
            :data="goodsList8"
            :isPay="true"
          ></examination-test-list>
        </view>
        <view class="not_data" v-else>
          <view class="img">
            <img
              src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/16954369620338446169543696203498545_%E7%BC%96%E7%BB%84%402x%20(4).png"
              alt=""
            />
          </view>
          <view class="desc">登录后查看相关测验哦！</view>
          <view
            class="button goLogin"
            @click="goDetail('pages/loginCenter/index')"
          >
            去登录
          </view>
        </view>
      </template>
      <view
        v-if="preInfo.practice_progress_text"
        style="width: 100%; height: 102rpx"
      ></view>
    </view>
    <!-- 底部- 上次练习位置 -->
    <view
      class="last-time"
      @click="preDetail"
      v-if="preInfo.practice_progress_text"
    >
      <view class="title">
        <view class="one"> 上次练习： </view>
        <view class="tow"> {{ preInfo.practice_progress_text }} </view>
      </view>
      <view class="continue">
        <text>继续练习</text>
        <image
          src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/16948470288266a15169484702882655742_bi.png"
          mode="widthFix"
        />
      </view>
    </view>
    <!-- 选择专业组件 -->
    <select-major
      v-model="majorid"
      :show.sync="majorshow"
      :major_name.sync="major_name"
      @input="chackMajor"
    ></select-major>
  </view>
</template>
<script>
import selectMajor from '../../components/select-major.vue'
import noData from '../../components/commen/no-data.vue'
import { getAllExam, getPreInfo, getGoods } from '../../api/index'
import examinationList from '../../components/makeQuestion/examination-list.vue'
import examinationTestList from '../../components/makeQuestion/examination-test-list.vue'
import indexNav from '../../components/commen/index-nav.vue'
import { goToLogin, goToMajor } from '../../utils/index'
import { shelf_platform_id } from '../../config'
export default {
  components: {
    selectMajor,
    noData,
    examinationList,
    examinationTestList,
    indexNav
  },
  data() {
    return {
      majorid: '',
      major_name: '选择专业',
      majorshow: false,
      index: 0,
      preInfo: {
        scene: 0,
        practice_progress_text: ''
      },
      // 上次练习的信息
      urlInfo: {
        1: 'pages/chapterExercise/index', // 章节练习
        2: 'pages/questionChallenge/index', // 真题闯关
        3: 'pages/intelligentEvaluation/index', // 智能测评
        5: 'pages/examEntry/index', // 考点词条
        6: 'pages/modelExaminationCompetition/index'
      },
      goodsList8: [],
      goodsList10: []
    }
  },
  onLoad(e) {
    this.init(e)
  },
  onShow() {
    this.startFun()
  },
  onHide() {
    this.majorshow = false
  },
  methods: {
    updata() {
      this.goodsList8 = []
      this.goodsList10 = []
      this.getGoods()
    },
    getGoods() {
      if (!this.isLogin()) {
        uni.stopPullDownRefresh()
        return
      }
      let { major_id = '', major_name = '' } =
        uni.getStorageSync('__xingyun_major__')
      getGoods({
        shelf_platform_id,
        professional_id: major_id,
        type: '8',
        is_buyed: 1
      }).then(res => {
        this.goodsList8 = res.data.list
        uni.stopPullDownRefresh()
      })
      getGoods({
        shelf_platform_id,
        professional_id: major_id,
        type: '10',
        is_buyed: 1
      }).then(res => {
        this.goodsList10 = res.data.list
        uni.stopPullDownRefresh()
      })
    },
    init(e) {
      let {
        employee_id = '',
        promoter_id = '',
        promoter_type = '',
        student_id = ''
      } = e
      if (employee_id) {
        uni.setStorageSync('__xingyun_share__', {
          employee_id,
          promoter_id,
          promoter_type,
          student_id
        })
      }
    },

    isLogin() {
      return !!this.$store.state.jintiku.token
    },
    getTestExm() {
      if (!this.isLogin()) {
        return
      }
      let data = {
        exam_type: '1,2,3',
        from_client: 'ios',
        source: '2',
        noloading: true
      }
      getAllExam(data).then(res => {
        this.testLists = res.data.list ? res.data.list : []
      })
    },
    goDetail(url) {
      if (!this.isLogin()) {
        // this.$xh.push('jintiku', 'pages/loginCenter/index')
        goToLogin('goDetail')
        return
      }
      this.$xh.push('jintiku', url)
    },
    startFun() {
      let { major_id = '', major_name = '' } =
        uni.getStorageSync('__xingyun_major__')
      if (this.majorid && major_id && this.majorid != major_id) {
        this.major_name = major_name
        this.majorid = major_id
        this.updata()
      } else if (major_id && major_name) {
        this.major_name = major_name
        this.majorid = major_id
        this.getGoods()
      } else {
        if (!this.isLogin()) {
          return
        }
        // this.$xh.push('jintiku', `pages/major/index`)
        goToMajor()
        return
      }
      // this.getInfo()
      // this.getPreDesc()
    },
    getInfo() {
      this.getTestExm()
    },
    getPreDesc() {
      if (!this.isLogin()) {
        return
      }
      getPreInfo({
        noloading: true
      }).then(data => {
        this.preInfo = data.data
      })
    },
    chackMajor(value) {
      this.major_id = value
      // if (this.$refs.indexNav.init) {
      //   setTimeout(() => {
      //     this.$refs.indexNav.init()
      //   }, 500)
      // }
      setTimeout(() => {
        this.getInfo()
        this.updata()
      }, 500)
    },
    preDetail() {
      if (urlInfo[this.preInfo.scene]) {
        this.$xh.push('jintiku', urlInfo[this.preInfo.scene])
      }
    },
    selectMajorFn() {
      if (!this.isLogin()) {
        this.goDetail('pages/loginCenter/index')
        return
      }
      this.majorshow = !this.majorshow
    }
  },
  onPullDownRefresh() {
    this.updata()
  },
  onShareAppMessage() {
    return this.$xh.shareAppMessage()
  }
}
</script>

<style scoped lang="less">
.question-index {
  background-color: #fff;
  min-height: 100vh;
  overflow-y: auto;
  // .nav-bar {
  //   height: --status-bar-height;
  //   background: red;
  //   width: 100%;
  // }
  .header-box {
    padding-top: 80rpx;
    /* #ifdef H5 */
    padding-top: 0rpx;
    /* #endif */
    height: 435rpx;
    position: fixed;
    left: 0;
    top: 0;
    width: 100%;
    z-index: 1;
    background: linear-gradient(
      180deg,
      #2e68ff 0%,
      #2e68ff 43%,
      rgba(255, 255, 255, 0) 100%
    );
    .header {
      position: relative;
      height: calc(96rpx);
      display: flex;
      align-items: center;
      //justify-content: center;
      // margin-top: --status-bar-height;
      padding-left: 24rpx;
      .head {
        position: absolute;
        height: 64rpx;
        width: 64rpx;
        overflow: hidden;
        border-radius: 50%;
        left: 30rpx;
        top: 0;
        bottom: 0;
        margin: auto 0;
        image {
          width: 100%;
          height: auto;
        }
      }
      .major {
        display: flex;
        align-items: center;
        justify-content: center;
        text {
          font-size: 32rpx;
          font-family: PingFangSC-Medium, PingFang SC;
          font-weight: 500;
          color: #fff;
          line-height: 32rpx;
        }
        image {
          width: 20rpx;
          height: 20rpx;
          margin-left: 20rpx;
        }
        &.active {
          text {
          }
        }
      }
    }
  }
  .content {
    background: #fff;
    //height: calc(100vh - 500rpx);
    position: relative;
    z-index: 1;
    margin-top: 175rpx;
    /* #ifdef H5 */
    margin-top: 90rpx;
    /* #endif */
    border-top-right-radius: 20rpx;
    border-top-left-radius: 20rpx;
    padding: 32rpx 24rpx 60rpx 24rpx;
    .title {
      font-size: 32rpx;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 600;
      color: #161f30;
      line-height: 32rpx;
      margin-bottom: 24rpx;
      display: flex;
      align-items: center;
      .line {
        width: 6rpx;
        height: 30rpx;
        background: #2e68ff;
        border-radius: 4rpx;
        margin-right: 10rpx;
      }
    }
  }
  .last-time {
    position: fixed;
    left: 0;
    bottom: 0;
    height: 74rpx;
    background-color: #eff5ff;
    z-index: 2;
    padding: 0 24rpx;
    width: 100%;
    display: flex;
    justify-content: space-between;
    .title {
      display: flex;
      align-items: center;
      font-size: 24rpx;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #2e68ff;
      .tow {
        font-weight: 800;
      }
    }
    .continue {
      display: flex;
      align-items: center;
      text {
        font-size: 24rpx;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #2e68ff;
        margin-right: 16rpx;
      }
      image {
        width: 26rpx;
        height: 26rpx;
      }
    }
  }
  .not_data {
    width: 100%;
    padding-top: 120rpx;
    padding-bottom: 120rpx;
    .img {
      width: 229rpx;
      height: 180rpx;
      margin: 0 auto;
      img {
        width: 100%;
        height: 100%;
      }
    }
    .desc {
      text-align: center;
      font-size: 26rpx;
      margin-top: 40rpx;
      color: #ccc;
    }
    .goLogin {
      width: 260rpx;
      height: 80rpx;
      background: #2e68ff;
      color: #ffffff;
      font-size: 28rpx;
      text-align: center;
      line-height: 80rpx;
      border-radius: 38rpx;
      margin: 40rpx auto;
    }
  }
}
</style>
