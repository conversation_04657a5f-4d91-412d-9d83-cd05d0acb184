<template>
  <view class="question-index">
    <view class="backgroud-color-blue"></view>
    <view class="nav-bar"></view>
    <!-- 专业切换 -->
    <view class="header-box">
      <view class="header">
        <view v-if="false" class="head" @click="goDetail('pages/userInfo/index')">
          <image
            src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/16693568262487366166935682624823545_touxiang.png"
            mode="widthFix" />
        </view>
        <view class="major flex-center" @click="selectMajorFn">
          <text>{{ major_name }}</text>
          <image
            src="https://yakaixin.oss-cn-beijing.aliyuncs.com/down.png"
            mode="widthFix" />
        </view>
      </view>
    </view>
    <view class="banner-view">
      <!-- 学习日历组件 - 新增 -->
      <study-calendar :persist-days="'31'" :total-questions="'700'"
        :accuracy-rate="'12'" :is-checked-in="false" @check-in="handleCheckIn" />
    </view>
    <view style="margin-top: 20rpx;">
      <study-card-grid @cardClick="handleCardClick" />
    </view>
    <view class="content">
      <view class="title">
		<img style="margin-right: 10rpx; width: 25rpx;height: 25rpx;" src="https://yakaixin.oss-cn-beijing.aliyuncs.com/title-icon.png" alt="" />
        <text>章节模考</text>
      </view>
      <index-nav></index-nav>
	  
      <view class="title" style="margin-top: 30rpx;">
        <img style="margin-right: 10rpx; width: 25rpx;height: 25rpx;" src="https://yakaixin.oss-cn-beijing.aliyuncs.com/title-icon.png" alt="" />
        <text>科目模考</text>
      </view>
	  <index-nav-item :formData="getFormData(goodsList810)"></index-nav-item>
	  
	  <view class="title" style="margin-top: 30rpx;">
	    <img style="margin-right: 10rpx; width: 25rpx;height: 25rpx;" src="https://yakaixin.oss-cn-beijing.aliyuncs.com/title-icon.png" alt="" />
	    <text>模拟考试</text>
	  </view>
	  <index-nav-item :formData="getFormData(goodsList810)"></index-nav-item>
	</view>
    <!-- 选择专业组件 -->
    <select-major v-model="majorid" :show.sync="majorshow" :major_name.sync="major_name"
      @input="chackMajor"></select-major>
  </view>
</template>
<script>
import selectMajor from '../../components/select-major.vue'
import noData from '../../components/commen/no-data.vue'
import {
  getAllExam,
  getPreInfo,
  getGoods,
  activityRaram
} from '../../api/index'
import StudyCardGrid from '../../components/commen/study-card-grid.vue'
import StudyCalendar from '../../components/study-calendar.vue'
import examinationList from '../../components/makeQuestion/examination-list.vue'
import examinationTestList from '../../components/makeQuestion/examination-test-list.vue'
import examinationTestItem from '../../components/makeQuestion/examination-test-item.vue'
import indexNav from '../../components/commen/index-nav.vue'
import indexNavItem from '../../components/commen/index-nav-item.vue'
import { goToLogin, goToMajor, isVersionUpdata } from '../../utils/index'
import { shelf_platform_id } from '../../config'
export default {
  components: {
    selectMajor,
    StudyCardGrid,
    StudyCalendar,
    noData,
    examinationList,
    indexNav,
	indexNavItem,
    examinationTestList,
    examinationTestItem,

  },
  data() {
    return {
      bannerList: [
        "https://uviewui.com/swiper/swiper1.png",
        "https://uviewui.com/swiper/swiper2.png",
        "https://uviewui.com/swiper/swiper3.png",
      ],
      tabIndex: '1',
      tabs: [
        {
          name: '测验',
          id: '1'
        },
        {
          name: '练习',
          id: '2'
        }
      ],
      majorid: '',
      major_name: '选择专业',
      majorshow: false,
      index: 0,
      testLists: [],
      preInfo: {
        scene: 0,
        practice_progress_text: ''
      },
      // 上次练习的信息
      urlInfo: {
        1: 'pages/chapterExercise/index', // 章节练习
        2: 'pages/questionChallenge/index', // 真题闯关
        3: 'pages/intelligentEvaluation/index', // 智能测评
        5: 'pages/examEntry/index', // 考点词条
        6: 'pages/modelExaminationCompetition/index'
      },
      goodsList18: [],
      goodsList810: [],
      recommendList: [],
      query: {}
    }
  },
  onLoad(e) {
    console.log(e, '首页参数')
    this.query = e

    this.init(e)
    // this.getGoods()
    // this.autoUpdate()
    isVersionUpdata(() => {
      if (!this.$store.state.jintiku.token) {
        this.$store.dispatch('jintiku/UNlOGIN')
        goToLogin('goToLogin33')
      }
      this.majorid = ''
      this.major_name = '选择专业'
      this.preInfo = {
        scene: 0,
        practice_progress_text: ''
      }
      this.testLists = []
    })
  },
  onShow() {
    console.log(process.env.VUE_APP_EXTENDUID, 'process.env.VUE_APP_EXTENDUID')
    // console.log(this.index)
    // if (this.index <= 2) {
    //   this.startFun()
    //   this.index++
    // }
    // console.log('App Show')

    this.startFun()
    this.autoUpdate()
    this.activityRecord()
  },
  onHide() {
    this.majorshow = false
  },
  methods: {
	  getFormData(list){
		  return list[0];
	  },
    handleCheckIn() {
      // 处理打卡逻辑
    },
    handleCardClick(type) {
      console.log('点击了卡片:', type)
      // 自定义处理逻辑
    },
    updata() {
      this.goodsList18 = []
      this.goodsList810 = []
      this.recommendList = []
      this.getGoods()
      // this.$refs.indexNav.init()
    },
	getTestExm() {
	  if (!this.isLogin()) {
	    return
	  }
	  let data = {
	    exam_type: '1,2,3',
	    from_client: 'ios',
	    source: '2',
	    noloading: true
	  }
	  getAllExam(data).then(res => {
	    this.testLists = res.data.list ? res.data.list : []
	  })
    getGoods() {
      // if (!this.isLogin()) {
      //   uni.stopPullDownRefresh()
      //   return
      // }
      if (this.goodsList18.length > 0 || this.goodsList810.length > 0) {
        return
      }
      let { major_id = '', major_name = '' } =
        uni.getStorageSync('__xingyun_major__')
      getGoods({
        shelf_platform_id,
        professional_id: major_id,
        type: 18
      }).then(res => {
        this.goodsList18 = res.data.list

        this.recommendList = this.recommendList.concat(
          res.data.list.filter(
            e => e.is_homepage_recommend == 1 && e.permission_status == '2'
          )
        )
      })
      getGoods({
        shelf_platform_id,
        professional_id: major_id,
        type: '8,10'
      }).then(res => {
        this.goodsList810 = res.data.list
        this.recommendList = this.recommendList.concat(
          res.data.list.filter(
            e => e.is_homepage_recommend == 1 && e.permission_status == '2'
          )
        )
        uni.stopPullDownRefresh()
      })
    },
    init(e) {
		getTestExm();
      if (e?.scene) {
        this.xyppid = e.scene
        this.$store.commit('jintiku/setXyppid', e.scene)

        activityRaram({
          id: e.scene
        }).then(res => {
          this.$store.commit(
            'jintiku/setEmployeeId',
            res.data.xy_employee_id || ''
          )
        })
      }
      if (e.employee_id) {
        this.$store.commit('jintiku/setEmployeeId', e.employee_id)
      }
      if (e.major_id && e.major_name) {
        uni.setStorageSync('__xingyun_major__', {
          major_id: e.major_id,
          major_name: e.major_name
        })
      }
      let major = uni.getStorageSync('__xingyun_major__')
      if (!major.major_id) {
        uni.setStorageSync('__xingyun_major__', {
          major_id: '524033912737962623',
          major_name: '口腔执业医师'
        })
      }
      let {
        employee_id = '',
        promoter_id = '',
        promoter_type = '',
        student_id = ''
      } = e
      if (employee_id) {
        uni.setStorageSync('__xingyun_share__', {
          employee_id,
          promoter_id,
          promoter_type,
          student_id
        })
      }
    },
    update() {
      let updateManager = uni.getUpdateManager()
      if (!updateManager) {
        return
      } else {
        //新版本更新
        if (uni.canIUse('getUpdateManager')) {
          //判断当前微信版本是否支持版本更新
          updateManager.onCheckForUpdate(function (res) {
            if (res.hasUpdate) {
              // 请求完新版本信息的回调
              updateManager.onUpdateReady(function () {
                uni.showModal({
                  title: '更新提示',
                  content: '新版本已经准备好，是否重启应用？',
                  success: function (res) {
                    if (res.confirm) {
                      // 新的版本已经下载好，调用 applyUpdate 应用新版本并重启
                      updateManager.applyUpdate()
                    }
                  }
                })
              })
              updateManager.onUpdateFailed(function () {
                uni.showModal({
                  // 新的版本下载失败
                  title: '已经有新版本了哟~',
                  content:
                    '新版本已经上线啦~，请您删除当前小程序，到微信 “发现-小程序” 页，重新搜索打开哦~'
                })
              })
            } else {
            }
          })
        } else {
          uni.showModal({
            // 如果希望用户在最新版本的客户端上体验您的小程序，可以这样子提示
            title: '溫馨提示',
            content:
              '当前微信版本过低，部分功能无法使用，请升级到最新微信版本后重试。'
          })
        }
      }
    },
    autoUpdate: function () {
      //#ifdef MP-WEIXIN
      var self = this
      // 获取小程序更新机制兼容
      if (wx.canIUse('getUpdateManager')) {
        const updateManager = wx.getUpdateManager()
        //1. 检查小程序是否有新版本发布
        updateManager.onCheckForUpdate(function (res) {
          console.log(res, '查看检查机制')
          // 请求完新版本信息的回调
          if (res.hasUpdate) {
            //2. 小程序有新版本，则静默下载新版本，做好更新准备
            updateManager.onUpdateReady(function () {
              console.log(new Date())
              wx.showModal({
                title: '更新提示',
                content: '新版本已经准备好，是否重启应用？',
                success: function (res) {
                  if (res.confirm) {
                    //3. 新的版本已经下载好，调用 applyUpdate 应用新版本并重启
                    updateManager.applyUpdate()
                  } else if (res.cancel) {
                    //如果需要强制更新，则给出二次弹窗，如果不需要，则这里的代码都可以删掉了
                    wx.showModal({
                      title: '温馨提示~',
                      content:
                        '本次版本更新涉及到新的功能添加，旧版本无法正常访问的哦~',
                      success: function (res) {
                        self.autoUpdate()
                        return
                        //第二次提示后，强制更新
                        if (res.confirm) {
                          // 新的版本已经下载好，调用 applyUpdate 应用新版本并重启
                          updateManager.applyUpdate()
                        } else if (res.cancel) {
                          //重新回到版本更新提示
                          self.autoUpdate()
                        }
                      }
                    })
                  }
                }
              })
            })
            updateManager.onUpdateFailed(function () {
              // 新的版本下载失败
              wx.showModal({
                title: '已经有新版本了哟~',
                content:
                  '新版本已经上线啦~，请您删除当前小程序，重新搜索打开哟~'
              })
            })
          }
        })
      } else {
        // 如果希望用户在最新版本的客户端上体验您的小程序，可以这样子提示
        wx.showModal({
          title: '提示',
          content:
            '当前微信版本过低，无法使用该功能，请升级到最新微信版本后重试。'
        })
      }
      // #endif
    },
    isLogin() {
      return !!this.$store.state.jintiku.token
    },
    goDetail(url) {
      if (!this.isLogin()) {
        // this.$xh.push('jintiku', 'pages/loginCenter/index')
        goToLogin('goToLogin44')
        return
      }
      this.$xh.push('jintiku', url)
    },
    startFun() {
      let { major_id = '', major_name = '' } =
        uni.getStorageSync('__xingyun_major__')
      if (this.majorid && major_id && this.majorid != major_id) {
        this.major_name = major_name
        this.majorid = major_id
      } else if (major_id && major_name) {
        this.major_name = major_name
        this.majorid = major_id
        // this.getGoods()
      } else {
        if (!this.isLogin()) {
          return
        }
        // this.$xh.push('jintiku', `pages/major/index`)
        if (this.query.major_id && this.query.major_name) {
          uni.setStorageSync('__xingyun_major__', {
            major_id: this.query.major_id,
            major_name: this.query.major_name
          })
        } else {
          uni.setStorageSync('__xingyun_major__', {
            major_id: '524033912737962623',
            major_name: '口腔执业医师'
          })
        }
        goToMajor()
        return
      }
      this.updata()
      this.getPreDesc()
    },

    getPreDesc() {
      if (!this.isLogin()) {
        return
      }
      getPreInfo({
        noloading: true
      }).then(data => {
        this.preInfo = data.data
      })
    },
    chackMajor(value) {
      this.major_id = value
      // if (this.$refs.indexNav.init) {
      //   setTimeout(() => {
      //     this.$refs.indexNav.init()
      //   }, 500)
      // }
      setTimeout(() => {
        this.updata()
      }, 500)
    },

    selectMajorFn() {
      // if (!this.isLogin()) {
      //   this.goDetail('pages/loginCenter/index')
      //   return
      // }
      this.majorshow = !this.majorshow
    },
    // 活动记录
    activityRecord() {
      if (this.$store.state.jintiku.xyppid && this.isLogin()) {
        this.$store.dispatch('jintiku/ACTIVITY_RECORD')
      } else {
        console.log(this.xyppid, 'this.xyppid')
        if (this.xyppid) {
          this.$store.commit('jintiku/setXyppid', this.xyppid)
          this.$store.dispatch('jintiku/ACTIVITY_RECORD')
        }
      }
    }
  },
  onPullDownRefresh() {
    this.updata()
  },
  onShareAppMessage() {
    return this.$xh.shareAppMessage()
  }
}
</script>

<style scoped lang="less">

.banner-view {
  position: relative;
  z-index: 2;
  width: 100%;
  padding: 0 25rpx;
  height: 210rpx;
  box-sizing: border-box;
}

.tabs {
  height: 70rpx;
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;

  .tab-item {
    margin-right: 40rpx;
    width: 112rpx;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;

    .name {
      font-weight: 400;
      font-size: 32rpx;
      color: #666666;
      text-align: center;
    }

    .img {
      display: none;
    }
  }

  .active {
    .name {
      font-weight: 500;
      font-size: 36rpx;
      color: #000000;
    }

    .img {
      display: block;
      position: absolute;
      bottom: 0;
      left: calc(50% - 18rpx);
      width: 36rpx;
      height: 16rpx;
    }
  }
}

.question-index {
  background-color: #F5F5F5;
  min-height: 100vh;
  overflow-y: auto;

  // .nav-bar {
  //   height: --status-bar-height;
  //   background: red;
  //   width: 100%;
  // }
  .backgroud-color-blue {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 50vh;
    pointer-events: none;
    background: linear-gradient(180deg,
        #B8E8FC 0%,
        #E9F7FF 80%,
        rgba(255, 255, 255, 0) 100%);
  }

  .header-box {
    padding-top: 80rpx;
    /* #ifdef H5 */
    padding-top: 0rpx;
    /* #endif */
    width: 100%;

    .header {
      position: relative;
      z-index: 2;
      height: calc(96rpx);
      display: flex;
      align-items: center;
      // justify-content: center;
      padding-left: 24rpx;

      // margin-top: --status-bar-height;
      .head {
        position: absolute;
        height: 64rpx;
        width: 64rpx;
        overflow: hidden;
        border-radius: 50%;
        left: 30rpx;
        top: 0;
        bottom: 0;
        margin: auto 0;

        image {
          width: 100%;
          height: auto;
        }
      }

      .major {
        display: flex;
        align-items: center;
        justify-content: center;

        text {
          font-size: 40rpx;
          font-family: PingFangSC-Medium, PingFang SC;
          font-weight: 500;
          line-height: 40rpx;
        }

        image {
          width: 20rpx;
          height: 20rpx;
          margin-left: 20rpx;
        }

        &.active {
          text {}
        }
      }
    }
  }

  .content {
    position: relative;
    z-index: 1;
    border-top-right-radius: 32rpx;
    border-top-left-radius: 32rpx;
    padding: 32rpx 24rpx 60rpx 24rpx;

    .title {
      font-size: 32rpx;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 600;
      color: #161f30;
      line-height: 32rpx;
      margin-bottom: 24rpx;
      display: flex;
      align-items: center;

      .line {
        width: 6rpx;
        height: 30rpx;
        background: #2e68ff;
        border-radius: 4rpx;
        margin-right: 10rpx;
      }
    }

    .cards {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 42rpx;

      .card {
        width: 340rpx;
        height: 90rpx;
        display: flex;
        align-items: center;
        justify-content: space-between;
        background: #eff4ff;
        border-radius: 16rpx;
        padding: 0 24rpx;

        text {
          font-size: 28rpx;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #000;
          line-height: 30rpx;
          margin-right: 46rpx;
        }

        image {
          width: 12rpx;
          height: 22rpx;
        }

        .left {
          image {
            width: 34rpx;
            height: 34rpx;
            margin-right: 20rpx;
          }
        }
      }
    }

    .test-card {
      margin-bottom: 40rpx;

      .conteiner {
        border-radius: 16rpx;
        padding: 24rpx 32rpx;
        box-shadow: rgba(99, 99, 99, 0.2) 0px 2px 8px 0px;
      }

      .test-card-title {
        font-size: 30rpx;
        font-family: PingFangSC-Semibold, PingFang SC;
        font-weight: 600;
        color: #212121;
        line-height: 42rpx;
        margin-bottom: 16rpx;
      }

      .date-time {
        font-size: 24rpx;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #898a8d;
        line-height: 24rpx;
        margin-bottom: 32rpx;
      }

      .line {
        height: 1rpx;
        background-color: #e8e9ea;
        margin-bottom: 36rpx;
      }

      .bottom {
        display: flex;
        align-items: center;
        justify-content: space-between;

        .left-name {
          display: flex;
          align-items: center;

          .name-title,
          .desc,
          .time {
            font-size: 22rpx;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #8a8b8c;
            line-height: 32rpx;
          }

          .name-title {
            font-weight: 800;
            color: #000;
          }

          .time {
            margin-left: 32rpx;
          }
        }
      }

      .button {
        width: 160rpx;
        height: 56rpx;
        line-height: 56rpx;
        background: #2e68ff;
        border-radius: 64px;
        font-size: 28rpx;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #ffffff;
        text-align: center;
      }
    }
  }

  .last-time {
    position: fixed;
    left: 0;
    bottom: 0;
    height: 74rpx;
    background-color: #eff5ff;
    z-index: 2;
    padding: 0 24rpx;
    width: 100%;
    display: flex;
    justify-content: space-between;

    .title {
      display: flex;
      align-items: center;
      font-size: 24rpx;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #2e68ff;

      .tow {
        font-weight: 800;
      }
    }

    .continue {
      display: flex;
      align-items: center;

      text {
        font-size: 24rpx;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #2e68ff;
        margin-right: 16rpx;
      }

      image {
        width: 26rpx;
        height: 26rpx;
      }
    }
  }

  .not_data {
    width: 100%;
    padding-top: 120rpx;

    .img {
      width: 229rpx;
      height: 180rpx;
      margin: 0 auto;

      img {
        width: 100%;
        height: 100%;
      }
    }

    .desc {
      text-align: center;
      font-size: 26rpx;
      margin-top: 40rpx;
      color: #ccc;
    }

    .goLogin {
      width: 260rpx;
      height: 80rpx;
      background: #2e68ff;
      color: #ffffff;
      font-size: 28rpx;
      text-align: center;
      line-height: 80rpx;
      border-radius: 38rpx;
      margin: 40rpx auto;
    }
  }
}
</style>
