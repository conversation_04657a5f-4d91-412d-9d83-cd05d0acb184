<template>
  <view class="set">
    <u-modal :show="show" title="章节练习" @confirm="setQuestion">
      <view class="slot-content">
        <input
          type="number"
          class="input"
          placeholder="请输入题目数"
          v-model="input_chapter_number"
        />
      </view>
    </u-modal>
    <view class="lists">
      <view class="list" @click="show = true">
        <view class="name"> 章节练习 </view>
        <view class="right">
          <text>每次{{ chapter_number }}道题</text>
          <image
            src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/1669357686810b313166935768681084385_right.png"
            mode="widthFix"
          />
        </view>
      </view>
      <!-- <view class="list">
        <view class="name"> 清理缓存 </view>
        <view class="right">
          <text>5MB</text>
          <image
            src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/1669357686810b313166935768681084385_right.png"
            mode="widthFix"
          />
        </view>
      </view> -->
      <!-- <view class="list" @click="goDetail">
        <view class="name"> 意见反馈 </view>
        <view class="right">
          <image
            src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/1669357686810b313166935768681084385_right.png"
            mode="widthFix"
          />
        </view>
      </view> -->
      <!-- <view class="list" @click="goDetail">
        <view class="name"> 关于我们 </view>
        <view class="right">
          <image
            src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/1669357686810b313166935768681084385_right.png"
            mode="widthFix"
          />
        </view>
      </view> -->
      <view class="list" @click="goDetail('pages/userInfo/privacy')">
        <view class="name"> 隐私协议 </view>
        <view class="right">
          <image
            src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/1669357686810b313166935768681084385_right.png"
            mode="widthFix"
          />
        </view>
      </view>
      <view
        class="list"
        @click="goDetail('pages/userInfo/userServiceAgreement')"
      >
        <view class="name"> 用户协议 </view>
        <view class="right">
          <image
            src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/1669357686810b313166935768681084385_right.png"
            mode="widthFix"
          />
        </view>
      </view>
      <view class="list" @click="UNlOGIN()">
        <view class="name"> 退出登录 </view>
        <view class="right">
          <image
            src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/1669357686810b313166935768681084385_right.png"
            mode="widthFix"
          />
        </view>
      </view>
    </view>
  </view>
</template>
<script>
import { getExamTime, setTimeInfo } from '../../api/userInfo'
export default {
  onLoad() {
    getExamTime({}).then(data => {
      this.chapter_number = data.data.chapter_number
      this.input_chapter_number = data.data.chapter_number
    })
    uni.getStorageInfo({
      success(res) {
        console.log(res)
      }
    })
  },
  data() {
    return {
      show: false,
      chapter_number: '',
      input_chapter_number: ''
    }
  },
  methods: {
    UNlOGIN() {
      uni.showModal({
        title: '提示',
        content: '确定退出登录',
        success: res => {
          if (res.confirm) {
            this.$store.dispatch('jintiku/UNlOGIN')
            uni.switchTab({
              url: '/modules/jintiku/pages/index/index',
              fail(err) {
                console.log(err)
              }
            })
          } else if (res.cancel) {
            console.log('用户点击取消')
          }
        }
      })
    },
    setQuestion() {
      if (this.input_chapter_number * 1 <= 0) {
        this.$xh.Toast('请输入正确的数字！')
        return
      }
      setTimeInfo({
        chapter_number: this.input_chapter_number
      }).then(data => {
        this.$xh.Toast('设置成功！')
        this.show = false
        this.chapter_number = this.input_chapter_number
      })
    },
    goDetail(url) {
      // this.$xh.Toast('敬请期待！')
      this.$xh.push('jintiku', url)
    }
  },
  onShareAppMessage() {
    return this.$xh.shareAppMessage()
  }
}
</script>
<style scoped lang="less">
.set {
  height: 100vh;
  background-color: #fff;
  padding: 20rpx;
  .lists {
    .list {
      height: 92rpx;
      line-height: 92rpx;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-bottom: 1px solid #eee;
      .name {
        font-size: 28rpx;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #161f30;
      }
      .right {
        display: flex;
        align-items: center;
        justify-content: center;
        text {
          font-size: 26rpx;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #787e8f;
        }
        image {
          width: 30rpx;
        }
      }
    }
  }
  .input {
    border: 1px solid #eee;
    width: 400rpx;
    height: 50rpx;
    line-height: 50rpx;
    padding-left: 30rpx;
    font-size: 24rpx;
  }
}
</style>
