<template>
  <view class="report">
    <view class="tabs">
      <view
        class="tab flex-center"
        :class="{ active: index == 0 }"
        @click="index = 0"
      >
        学习数据
      </view>
      <view
        class="tab flex-center"
        :class="{ active: index == 1 }"
        @click="index = 1"
      >
        成绩报告
      </view>
    </view>
    <view class="contaner">
      <view class="studt-data" v-if="index == 0">
        <!-- 学习数据 -->
        <view class="card">
          <view class="num-info">
            <view class="shuatiliang">
              <view class="top">
                <text>{{ baseInfo.total_num }}</text>
                <text>道</text>
              </view>
              <view class="text"> 刷题量 </view>
            </view>
            <view class="line"></view>
            <view class="shuatiliang">
              <view class="top">
                <text>{{ baseInfo.today_learn_time }}</text>
                <text>天</text>
              </view>
              <view class="text"> 坚持天数 </view>
            </view>
          </view>
          <view class="static-cards">
            <view class="static-card">
              <view class="static-title"> 正确率 </view>
              <view class="static-title"> {{ baseInfo.correct_rate }}% </view>
            </view>
            <view class="static-card">
              <view class="static-title"> 学习累计时长 </view>
              <view class="static-title"> {{ baseInfo.learn_time }}h </view>
            </view>
            <view class="static-card">
              <view class="static-title"> 学习知识点 </view>
              <view class="static-title"> {{ baseInfo.knowledge_num }}个 </view>
            </view>
          </view>
          <view class="title"> 刷题量 </view>
          <view class="card-tabs">
            <view
              class="card-tab"
              :class="{ active: question_num_type == 1 }"
              @click="question_num_type = 1"
            >
              最近一周
            </view>
            <view
              class="card-tab"
              :class="{ active: question_num_type == 2 }"
              @click="question_num_type = 2"
            >
              按月查看
            </view>
          </view>
          <view class="echarts">
            <qiun-data-charts
              type="column"
              :opts="opts"
              :chartData="questionTypeData"
              v-if="questionTypeDataShow"
            />
            <view class="noData" v-else> 暂无任何数据！ </view>
          </view>
          <view class="current">
            <view class="current-title">今日刷题</view>
            <view class="current-text">{{ baseInfo.today_total_num }}道</view>
          </view>
        </view>
        <view class="card">
          <view class="title"> 学习时长（h） </view>
          <view class="card-tabs">
            <view
              class="card-tab"
              :class="{ active: question_hour_type == 1 }"
              @click="question_hour_type = 1"
            >
              最近一周
            </view>
            <view
              class="card-tab"
              :class="{ active: question_hour_type == 2 }"
              @click="question_hour_type = 2"
            >
              按月查看
            </view>
          </view>
          <view class="echarts">
            <qiun-data-charts
              type="column"
              :opts="opts"
              :chartData="chartData"
              v-if="chartDataShow"
            />
            <view class="noData" v-else> 暂无任何数据！ </view>
          </view>
          <view class="current">
            <view class="current-title">今日学习</view>
            <view class="current-text">{{ baseInfo.today_learn_time }}h</view>
          </view>
        </view>
        <view class="card">
          <view class="title"> 易错知识点 </view>
          <view class="lists">
            <view
              class="list"
              v-for="(item, i) in baseInfo.knowledge_err_list"
              :key="i"
            >
              <view class="tip-title">
                <text>{{ i + 1 }}</text>
                <text>{{ item.knowledge_id_name }}</text>
              </view>
              <view class="times"> {{ item.fault_sum }}次 </view>
            </view>
          </view>
        </view>
      </view>
      <view class="cj-report" v-if="index == 1">
        <!-- 成绩报告 -->
        <view class="container">
          <view class="search">
            <input
              type="text"
              class="input"
              placeholder="输入考试名称"
              v-model="examination_name"
            />
            <view class="button flex-center" @click="getExam">搜索</view>
          </view>
          <view class="lists">
            <view
              class="list"
              v-for="(item, i) in mockList"
              :key="i"
              @click="goDetail(item)"
            >
              <view class="time"> {{ item.hand_paper_time }} </view>
              <view class="li box-show">
                <view class="title"> {{ item.examination_name }} </view>
                <view class="static">
                  <view class="left">
                    <view class="cj">
                      <view class="text">成绩</view>
                      <view class="text main-color">{{ item.score }}分</view>
                    </view>
                    <view class="cj">
                      <view class="text">排名</view>
                      <view class="text main-color">{{ item.rank }}</view>
                    </view>
                  </view>
                  <view class="status">
                    <image
                      v-if="item.is_pass == '1'"
                      src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/16969942606636768169699426066363349_jige.png"
                      mode="widthFix"
                      class="success"
                    />

                    <image
                      src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/16969942734451def169699427344583935_bujige.png"
                      mode="widthFix"
                      class="fail"
                      v-else
                    />
                  </view>
                </view>
              </view>
            </view>
            <view class="flex-center no-data" v-if="!mockList.length">
              暂无任何数据！
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>
<script>
import { getLearningData, getScorereporting } from '../../api/userInfo'
export default {
  data() {
    return {
      index: 0,
      questionTypeData: {},
      questionTypeDataShow: true,
      chartData: {},
      chartDataShow: true,
      opts: {
        fontColor: '#2E68FF',
        legend: {
          show: false
        },
        xAxis: {
          disableGrid: true
        },
        yAxis: {
          data: [
            {
              min: 0
            }
          ]
        },
        extra: {
          column: {
            barBorderCircle: true,
            type: 'group',
            width: 20,
            activeBgColor: '#000000',
            activeBgOpacity: 0.08
          }
        }
      },
      baseInfo: {
        correct_rate: '正确率',
        knowledge_err_list: [
          {
            fault_sum: '错误次数',
            knowledge_id: '知识点id'
          }
        ],
        knowledge_num: '学习知识点数量',
        learn_time: '学习时长',
        to_month_do_question_num: [
          {
            date: 'string',
            num: 0
          }
        ],
        to_month_learn_time: [
          {
            date: 'string',
            learn_time: 0
          }
        ],
        to_week_do_question_num: [
          {
            date: 'string',
            num: 0
          }
        ],
        to_week_learn_time: [
          {
            date: 'string',
            learn_time: 0
          }
        ],
        today_learn_time: '今日学习时长',
        today_total_num: '今日刷题量',
        total_num: '刷题量'
      },
      question_num_type: 1,
      question_hour_type: 1,
      mockList: [],
      examination_name: ''
    }
  },
  onShow() {
    this.getServerData()
  },
  watch: {
    question_num_type() {
      this.getEchartsData()
    },
    question_hour_type() {
      this.getEchartsData()
    }
  },
  methods: {
    getEchartsData() {
      this.questionTypeDataShow = true
      this.chartDataShow = true
      if (this.question_num_type == '1') {
        let questionTypeData = {
          categories: this.baseInfo.to_week_do_question_num.map(
            item => item.date
          ),
          series: [
            {
              name: '一周刷题正确率',
              data: this.baseInfo.to_week_do_question_num.map(
                item => item.num * 1
              )
            }
          ]
        }
        this.questionTypeData = JSON.parse(JSON.stringify(questionTypeData))
        if (!this.baseInfo.to_week_do_question_num.length) {
          this.questionTypeDataShow = false
        }
      } else {
        let questionTypeData = {
          categories: this.baseInfo.to_month_do_question_num.map(
            item => item.date
          ),
          series: [
            {
              name: '一周刷题正确率',
              data: this.baseInfo.to_month_do_question_num.map(
                item => item.num * 1
              )
            }
          ]
        }
        this.questionTypeData = JSON.parse(JSON.stringify(questionTypeData))
        if (!this.baseInfo.to_month_do_question_num.length) {
          this.questionTypeDataShow = false
        }
      }
      if (this.question_hour_type == '1') {
        let chartData = {
          categories: this.baseInfo.to_week_learn_time.map(item => item.date),
          series: [
            {
              name: '一周刷题正确率',
              data: this.baseInfo.to_week_learn_time.map(
                item => item.learn_time * 1
              )
            }
          ]
        }
        this.chartData = JSON.parse(JSON.stringify(chartData))
        if (!this.baseInfo.to_week_learn_time.length) {
          this.chartDataShow = false
        }
      } else {
        let chartData = {
          categories: this.baseInfo.to_month_learn_time.map(item => item.date),
          series: [
            {
              name: '一周刷题正确率',
              data: this.baseInfo.to_month_learn_time.map(
                item => item.learn_time * 1
              )
            }
          ]
        }
        this.chartData = JSON.parse(JSON.stringify(chartData))
        if (!this.baseInfo.to_month_learn_time.length) {
          this.chartDataShow = false
        }
      }
    },
    getExam() {
      getScorereporting({
        examination_name: this.examination_name
      }).then(data => {
        console.log(data.data)
        this.mockList = data.data ? data.data : []
      })
    },
    getServerData() {
      getLearningData({}).then(data => {
        let res = data.data
        this.baseInfo = res
        this.setDateFormat(this.baseInfo.to_month_do_question_num)
        this.setDateFormat(this.baseInfo.to_month_learn_time)
        this.setDateFormat(this.baseInfo.to_week_do_question_num)
        this.setDateFormat(this.baseInfo.to_week_learn_time)
        this.getEchartsData()
      })
      this.getExam()
    },
    setDateFormat(list) {
      list.map(item => {
        item.date = item.date.replace(/\d+-/, '')
        return item
      })
    },
    goDetail(item) {
      return
      let user_id = uni.getStorageSync('__xingyun_userinfo__').student_id
      this.$xh.push(
        'jintiku',
        `pages/statistics/scoreReporting?examination_session_id=${item.paper_version_id}&paper_version_id=${item.paper_version_id}&examination_id=${item.paper_version_id}&user_id=${user_id}`
      )
    }
  },
  onShareAppMessage() {
    return this.$xh.shareAppMessage()
  }
}
</script>
<style scoped lang="less">
.report {
  display: flex;
  flex-direction: column;
  height: 100vh;
  .tabs {
    height: 80rpx;
    line-height: 80rpx;
    background-color: #fff;
    display: flex;
    .tab {
      flex: 1;
    }
    .active {
      font-size: 32rpx;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 800;
      color: #2e68ff;
      line-height: 32rpx;
    }
  }
  .contaner {
    flex: 1;
    .studt-data {
      .card {
        padding: 20rpx;
        border-radius: 20rpx;
        background-color: #fff;
        margin-bottom: 20rpx;
        .num-info {
          display: flex;
          justify-content: space-around;
          margin-bottom: 40rpx;
          .shuatiliang {
            text-align: center;
            font-size: 24rpx;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #787e8f;
            line-height: 24rpx;
            flex: 1;
            .top {
              margin-bottom: 24rpx;
              text:first-child {
                font-size: 32rpx;
                font-family: PingFangSC-Medium, PingFang SC;
                font-weight: 800;
                color: #2e68ff;
                line-height: 30rpx;
                margin-right: 14rpx;
              }
            }
          }
          .line {
            width: 2rpx;
            height: 80rpx;
            background: #d7e5fe;
          }
        }
        .static-cards {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: 40rpx;
          .static-card {
            width: 200rpx;
            height: 120rpx;
            background: #f1f8ff;
            border-radius: 12rpx;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            .static-title {
              font-size: 26rpx;
              font-family: PingFangSC-Regular, PingFang SC;
              font-weight: 400;
              color: #787e8f;
              &:last-child {
                font-size: 28rpx;
                font-family: PingFangSC-Medium, PingFang SC;
                font-weight: 900;
                color: #161f30;
                margin-top: 16rpx;
              }
            }
          }
        }
        .title {
          font-size: 30rpx;
          font-family: PingFangSC-Medium, PingFang SC;
          font-weight: 800;
          margin-bottom: 40rpx;
          color: #161f30;
        }
        .card-tabs {
          display: flex;
          .card-tab {
            flex: 1;
            text-align: center;
            height: 60rpx;
            font-size: 26rpx;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #787e8f;
            line-height: 26rpx;
          }
          .active {
            font-size: 26rpx;
            font-family: PingFangSC-Medium, PingFang SC;
            font-weight: 800;
            color: #161f30;
            line-height: 26rpx;
          }
        }
        .current {
          display: flex;
          align-items: center;
          margin-top: 24rpx;
          margin-top: 44rpx;
          font-size: 26rpx;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #787e8f;
          line-height: 26rpx;
          .current-text {
            color: #161f30;
            margin-left: 24rpx;
          }
        }
        .lists {
          .list {
            display: flex;
            align-items: center;
            justify-content: space-between;
            .tip-title {
              font-size: 26rpx;
              font-family: PingFangSC-Medium, PingFang SC;
              font-weight: 800;
              color: #000000;
              line-height: 56rpx;
              text:first-child {
                margin-right: 16rpx;
              }
            }
            .times {
              font-size: 26rpx;
              font-family: PingFangSC-Regular, PingFang SC;
              font-weight: 400;
              color: #787e8f;
              line-height: 56rpx;
            }
          }
        }
      }
    }
    .cj-report {
      padding: 30rpx;
      .container {
        padding: 20rpx;
        border-radius: 20rpx;
        background-color: #fff;
        margin-bottom: 20rpx;
        .search {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: 40rpx;
          .input {
            width: 516rpx;
            height: 68rpx;
            background: #f6f7f8;
            border-radius: 32rpx;
            padding-left: 32rpx;
          }
          .button {
            width: 132rpx;
            height: 68rpx;
            background: #2e68ff;
            border-radius: 32rpx;
            font-size: 24rpx;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #ffffff;
            line-height: 34rpx;
          }
        }
        .lists {
          .list {
            margin-bottom: 40rpx;
            .time {
              font-size: 30rpx;
              font-family: PingFangSC-Medium, PingFang SC;
              font-weight: 500;
              color: #161f30;
              line-height: 30rpx;
              margin-bottom: 24rpx;
            }
            .li {
              padding: 30rpx 24rpx;
              padding-bottom: 0;
              border-radius: 20rpx;
              margin-bottom: 20rpx;
              .title {
                font-size: 30rpx;
                font-family: PingFangSC-Semibold, PingFang SC;
                font-weight: 600;
                color: #212121;
                line-height: 42rpx;
              }
              .static {
                display: flex;
                justify-content: space-between;
                .left {
                  display: flex;
                  align-items: center;
                  .cj {
                    margin-right: 58rpx;
                    .text {
                      font-size: 26rpx;
                      font-family: PingFangSC-Regular, PingFang SC;
                      font-weight: 400;
                      color: #787e8f;
                      line-height: 30rpx;
                    }
                    .main-color {
                      margin-top: 24rpx;
                      color: #2e68ff;
                      font-size: 28rpx;
                      font-weight: 800;
                    }
                  }
                }
                .status {
                  image {
                    width: 150rpx;
                    height: 146rpx;
                  }
                }
              }
            }
          }
        }
      }
    }
  }
  .noData {
    text-align: center;
    line-height: 300rpx;
    color: #ccc;
    font-size: 24rpx;
  }
}
</style>
