<template>
  <view class="page-baidu">
    <!-- 顶部功能预览 -->
    <view class="priview-time">
      <view class="static-text">
        <image
          src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/16952026981143f8f169520269811490483_pause.png"
          mode="widthFix"
          @click="pauseFn"
        />
        <view class="nums">
          <text>{{ maxCurrent }}</text>
          <text>/{{ maxTotal }}</text>
        </view>
      </view>
      <view class="process">
        <view class="process-box">
          <!-- 做对的进度条 -->
          <view
            class="success-process"
            :style="{ width: (successTotalNum / maxTotal) * 100 + '%' }"
          >
            <view
              class="num"
              v-if="successTotalNum % 5 != 0 || successTotalNum == 0"
            >
              {{ successTotalNum }}
            </view>
          </view>
          <!-- 计算阳光位置 -->
          <view
            class="sun"
            v-for="(item, i) in sun"
            :key="i"
            :style="{
              left: `calc(${item.pos} - 18rpx )`
            }"
          >
            <image
              src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/1695204471793207b169520447179377211_sun1.png"
              mode="widthFix"
              v-if="successTotalNum >= item.successNum"
            />
          </view>
        </view>
      </view>
    </view>
    <view class="h96"></view>
    <!-- 倒计时组件 -->
    <countdown @end="tiemEnd" ref="countdown" />
    <view class="uni-margin-wrap">
      <!-- 做题组件 -->
      <practise-question-swiper
        :lists.sync="lists"
        :index.sync="current"
        :disableTouch="true"
        :skipHiddenItemLayout="true"
        ref="exercise"
        @success="submit"
        @next="next"
      ></practise-question-swiper>
    </view>
    <!-- 操作 -->
    <view class="options" v-if="pause">
      <view class="xx">休息中...</view>
      <view class="option" @click="startFn">
        <image
          src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/16952814828718ea3169528148287133396_pl.png"
          mode="widthFix"
        />
        <text>继续闯关</text>
      </view>
      <view class="option" @click="reset">
        <image
          src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/16952815031053c21169528150310539257_re.png"
          mode="widthFix"
        />
        <text>重新开始</text>
      </view>
      <view class="option" @click="back">
        <image
          src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/16952815292322121169528152923283917_log.png"
          mode="widthFix"
        />
        <text>退出练习</text>
      </view>
    </view>
  </view>
</template>
<script>
import questionAnswer from '../../components/makeQuestion/question-answer.vue'
import answerSheet from '../../components/makeQuestion/answer-sheet.vue'
import practiseQuestionSwiper from '../../components/makeQuestion/practise-question-swiper.vue'
import headHeight from '../../components/commen/head-height.vue'
import countdown from '../../components/commen/countdown.vue'
import {
  setQuestionLists,
  questionHelper,
  rollBACKPageData,
  setSubmitPageData
} from '../../utils/index'
import { getQuestionsList, setAnswer } from '../../api/commen'
export default {
  components: {
    questionAnswer,
    answerSheet,
    practiseQuestionSwiper,
    headHeight,
    countdown
  },
  data() {
    return {
      lists: [], // 小题的数据列
      total: 0, // 统计的小题数量
      maxTotal: 0, // 统计的答题数量
      maxList: [], // 大题的数据列 - 把每道题的答题时间挂到这上面
      sun: [], // 阳光
      startDate: +new Date(), // 整体的开始时间
      maxStart: +new Date(), // 每道题的开始时间
      // 当前选定值
      current: 0,
      // 当前时间
      time: 0,
      knowledge_id: '',
      chapter_id: '',
      pause: false, // 是否暂停
      isSubmit: false
    }
  },
  computed: {
    // 计算当前用户答对了多少题
    successTotalNum() {
      // return this.lists.reduce((pre, cur) => {
      //   return pre + (questionHelper.diffAnswer(cur) ? 1 : 0)
      // }, 0)
      // 数据回滚
      let data = rollBACKPageData(this.lists)
      let num = 0
      data.forEach(item => {
        let result = item.user_option.find(res => {
          return (
            JSON.stringify(res.answer.sort()) !=
            JSON.stringify(res.selected.sort())
          )
        })
        if (!result) {
          // 如果不存在做错的 说明这道大题做对了
          num += 1
        }
      })
      return num
    },
    // 计算用户答题到哪个答题了
    maxCurrent() {
      if (this.lists[this.current]) {
        let id = this.lists[this.current].id
        return this.maxList.findIndex(res => res.id == id) + 1
      } else {
        return 1
      }
    }
  },
  watch: {
    maxCurrent(val) {
      let time = +new Date()
      let cost_time = (time - this.maxStart) / 1000
      this.maxStart = +new Date()
      this.maxList[val - 2].cost_time = cost_time
    }
  },
  methods: {
    next() {
      this.$refs.countdown.reset && this.$refs.countdown.reset()
    },
    reset() {
      this.$refs.countdown.destory && this.$refs.countdown.destory()
      uni.navigateBack({
        delta: 1
      })
    },
    back() {
      uni.navigateBack({
        delta: 2
      })
    },
    pauseFn() {
      this.pause = true
      this.$refs.countdown.pause && this.$refs.countdown.pause()
    },
    startFn() {
      this.pause = false
      this.$refs.countdown.play && this.$refs.countdown.play()
    },
    tiemEnd() {
      // 一题的时间到
      // 时间到 就下一题
      if (this.current + 1 >= this.total) {
        // 已经完事了 -执行交卷逻辑
        this.submit()
      } else {
        this.current += 1
        // 重置时间
        this.$refs.countdown.reset && this.$refs.countdown.reset(true)
      }
    },
    selecte(info) {
      this.lists = this.lists.map(res => {
        if (res.questionid == info.questionid) {
          return {
            ...res,
            ...info
          }
        }
        return res
      })
      this.next()
    },
    // 交卷
    submit() {
      if (this.isSubmit) {
        return
      }
      this.maxList[this.maxList.length - 1].cost_time =
        (+new Date() - this.maxStart) / 1000

      let resultQuestion = setSubmitPageData(this.lists).map((item, i) => {
        return {
          ...item,
          cost_time: Math.ceil(this.maxList[i].cost_time)
        }
      })
      let obj = {
        type: '2',
        question_info: JSON.stringify(resultQuestion),
        cost_time: Math.floor((+new Date() - this.startDate) / 1000),
        product_id: this.knowledge_id
      }
      setAnswer({
        ...obj
      }).then(data => {
        console.log(data.data.practice_record_id)
        // 提交成功
        this.isSubmit = true
        uni.redirectTo({
          url: `/modules/jintiku/pages/questionChallenge/report?knowledge_id=${this.knowledge_id}&chapter_id=${this.chapter_id}&record_id=${data.data.practice_record_id}`,
          fail(err) {
            console.log(err)
          }
        })
      })
    },
    sheetchange(index) {
      this.current = index
    },
    getList() {
      getQuestionsList({
        chapter_id: this.chapter_id,
        knowledge_id: this.knowledge_id,
        type: 2
      }).then(data => {
        if (!data.data.section_info) {
          this.$xh.Toast('该关卡下暂无任何题哦！')
          return
        }
        let maxTotal = data.data.section_info.length
        this.maxTotal = data.data.section_info.length
        this.lists = setQuestionLists(data.data.section_info)
        this.maxList = data.data.section_info
        this.total = this.lists.length
        // 5道题换一个阳关
        while (maxTotal > 0) {
          if (maxTotal % 5 == 0) {
            this.sun.push({})
          }
          maxTotal -= 1
        }
        this.sun = this.sun.map((item, i) => {
          return {
            pos: ((5 * (i + 1)) / this.maxTotal) * 100 + '%', // 阳光位置
            successNum: 5 * (i + 1) // 获取阳光所需要答对的大题目数
          }
        })
      })
    }
  },
  onLoad(e) {
    uni.enableAlertBeforeUnload({
      message: '学贵有恒，不要轻言放弃，确认退出吗？',
      success: function (res) {
        console.log('点击确认按钮了：', res)
      },
      fail: function (errMsg) {
        console.log('点击取消按钮了：', errMsg)
      }
    })
    this.knowledge_id = e.knowledge_id
    this.chapter_id = e.chapter_id
    this.getList()
  },
  onUnload() {
    this.$refs.countdown.destory && this.$refs.countdown.destory()
  },
  onShareAppMessage() {
    return this.$xh.shareAppMessage()
  }
}
</script>
<style scoped lang="less">
.page-baidu {
  position: relative;
  .head-height {
    position: relative;
    z-index: 10;
  }
  .priview-time {
    position: fixed;
    left: 0;
    top: 0rpx;
    width: 100vw;
    height: 96rpx;
    background-color: #fff;
    border-bottom: 1px solid #eee;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 40rpx;
    z-index: 10;
    .nums {
      text {
        font-size: 32rpx;
        color: #000000;
      }
      text:last-child {
        color: #949494;
      }
    }
    .time {
      text {
        font-size: 32rpx;
        color: #000000;
      }
    }
    .static-text {
      display: flex;
      align-items: center;
      image {
        width: 40rpx;
        height: 40rpx;
        margin-right: 37rpx;
      }
    }
    .process {
      flex: 1;
      padding-left: 37rpx;
      .process-box {
        width: 100%;
        height: 22rpx;
        background: #f5f5f5;
        border-radius: 11rpx;
        position: relative;
        .success-process {
          position: absolute;
          left: 0;
          top: 0;
          height: 100%;
          background-color: #d6e4fe;
          border-radius: 11rpx;
          .num {
            position: absolute;
            right: -18rpx;
            top: 0;
            bottom: 0;
            margin: auto 0;
            background: url('https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/1695203776686a87216952037766866067_cicle.png')
              no-repeat;
            background-size: cover;
            width: 32rpx;
            height: 32rpx;
            color: #fff;
            text-align: center;
            line-height: 32rpx;
            font-size: 20rpx;
          }
        }
        .sun {
          width: 32rpx;
          height: 32rpx;
          background: url('https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/1695204140721c1ef169520414072269534_s2.png')
            no-repeat;
          background-size: cover;
          position: absolute;
          top: 0;
          bottom: 0;
          margin: auto 0;
          image {
            width: 100%;
            height: 100%;
          }
        }
        // .sun9 {
        //   left: calc(9 / 15 * 100% - 18rpx);
        // }
        // .sun12 {
        //   left: calc(12 / 15 * 100% - 18rpx);
        // }
        // .sun15 {
        //   left: calc(15 / 15 * 100% - 18rpx);
        // }
      }
    }
  }
  .h96 {
    height: 96rpx;
    background-color: #fff;
    position: relative;
    z-index: 9;
  }
  .title {
    font-size: 32rpx;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 800;
    color: #000000;
    height: 94rpx;
    line-height: 94rpx;
    text-align: center;
  }
  .time {
    height: 64rpx;
    line-height: 64rpx;
    background: #f5f8ff;
    font-size: 24rpx;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #2e68ff;
    text-align: center;
  }
  .uni-margin-wrap {
    width: calc(100vw);
    height: calc(100vh - 100rpx);
    overflow-y: scroll;
    padding-bottom: calc(80rpx + 42rpx + 36rpx);
    // padding-top: 96rpx;
    box-sizing: border-box;
  }
  .options {
    position: fixed;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: #fff;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 11;
    .xx {
      color: #2e68ff;
      font-size: 28rpx;
      margin-top: -400rpx;
    }
    .option {
      margin-top: 40rpx;
      display: flex;
      align-items: center;
      image {
        width: 32rpx;
        height: 32rpx;
        margin-right: 20rpx;
      }
      text {
        color: #333;
        font-size: 28rpx;
      }
    }
  }
}
</style>
