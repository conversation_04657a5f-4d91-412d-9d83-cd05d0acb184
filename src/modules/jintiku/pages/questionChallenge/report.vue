<template>
  <view class="report">
    <view
      class="head"
      :class="{
        head1: getStart == 1,
        head2: getStart == 2,
        head3: getStart >= 3
      }"
    >
      <view class="title">{{ textMap[getStart].comment }}</view>
      <view class="rate">
        <l-circular-progress
          type="halfCircular"
          :percent="right_rate"
          :lineWidth="10"
          bgColor="#EDF5FF"
          gradualColor="#95C0FA"
          :boxWidth="180"
          :boxHeight="180"
          :fontShow="false"
        ></l-circular-progress>
        <view class="rate-text">
          <view class="text">正确率</view>
          <view class="static"> {{ right_rate }}% </view>
        </view>
      </view>
      <view class="stars stars3">
        <!-- 星星 -->
      </view>
    </view>
    <view class="answer">
      共{{ total }}题，答对{{ right_num }}题，答错{{ fault_num }}题
    </view>
    <view class="button flex-center">
      {{ textMap[getStart].btn }}
      <button open-type="share" class="share">分享</button>
    </view>
    <view class="button flex-center" @click="look"> 查看解析 </view>
    <view class="bottom">
      <view class="left btn active" @click="reset">重玩本关</view>
      <view
        class="right btn"
        :class="{ active: getStart >= 3 }"
        @click="next(getStart >= 3)"
      >
        下一关
      </view>
    </view>
  </view>
</template>
<script>
import lCircularProgress from '../../../../components/l-circular-progress.vue'
import { report } from '../../api/questionChallenge'
export default {
  components: {
    lCircularProgress
  },
  data() {
    return {
      getStart: 0,
      textMap: {
        0: {
          comment: '继续努力',
          btn: '求安慰'
        },
        1: {
          comment: '正好及格',
          btn: '找人PK'
        },
        2: {
          comment: '再接再厉',
          btn: '炫耀一下'
        },
        3: {
          comment: '完美通关',
          btn: '孤独求败'
        }
      },
      knowledge_id: '',
      chapter_id: '',
      record_id: '',
      right_rate: 0,
      total: 0,
      fault_num: 0,
      right_num: 0
    }
  },
  onLoad(e) {
    this.knowledge_id = e.knowledge_id
    this.chapter_id = e.chapter_id
    this.record_id = e.record_id
    this.getStatic()
  },
  methods: {
    next(flag) {
      if (!flag) {
        return
      }
      // uni.$on('nextChallenge', (data) => {
      //   console.log('监听到事件来自 update ，携带参数 msg 为：' + data.msg);
      // })
      uni.$emit('nextChallenge')
    },
    reset() {
      uni.navigateBack({
        delta: 1
      })
    },
    getStatic() {
      report({
        real_id: this.knowledge_id,
        record_id: this.record_id
      }).then(data => {
        let res = data.data
        this.getStart = res.sunshine * 1
        this.right_rate = res.right_rate * 1
        this.fault_num = res.fault_num * 1
        this.right_num = res.right_num * 1
        this.total = this.right_num + this.fault_num
      })
    },
    look() {
      // 查看解析
      this.$xh.push(
        'jintiku',
        `pages/makeQuestion/lookAnalysisQuestion?type=2&knowledge_id=${this.knowledge_id}&chapter_id=${this.chapter_id}&practice_record_id=${this.record_id}`
      )
    }
  },
  onShareAppMessage() {
    return {
      title: this.textMap[this.getStart].comment,
      content: this.textMap[this.getStart].btn,
      desc: this.textMap[this.getStart].btn
    }
  }
}
</script>
<style scoped lang="less">
.report {
  height: 100vh;
  background-color: #fff;
  .head {
    height: 666rpx;
    background: url('https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/169744299239758451697442992397997_wain.png')
      no-repeat;
    background-size: cover;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    .title {
      font-size: 50rpx;
      font-family: PingFangSC-Medium, PingFang SC;
      color: #666666;
      line-height: 50rpx;
      margin-bottom: 48rpx;
    }
    .rate {
      position: relative;
      .rate-text {
        position: absolute;
        left: 0;
        right: 0;
        top: 60rpx;
        // bottom: 0;
        margin: auto;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        .text {
          font-size: 28rpx;
          font-family: PingFangSC-Medium, PingFang SC;
          font-weight: 500;
          color: #999999;
          line-height: 28rpx;
          margin-bottom: 26rpx;
        }
        .static {
          font-size: 72rpx;
          font-family: PingFangSC-Medium, PingFang SC;
          font-weight: 500;
          color: #666666;
          line-height: 72rpx;
        }
      }
    }
    .stars {
      width: 467rpx;
      height: 132rpx;
      background: url('https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/16974435368367d75169744353683771678_bg.png')
        no-repeat;
      background-size: cover;
      margin-top: -140rpx;
    }
    &.head1 {
      background: url('https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/169744401280025d3169744401280085137_h1.png')
        no-repeat;
      background-size: cover;
      .stars {
        background: url('https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/169744379311354b8169744379311469091_s1.png')
          no-repeat;
        background-size: cover;
      }
    }
    &.head2 {
      background: url('https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/169744406123584fe169744406123587783_h2.png')
        no-repeat;
      background-size: cover;
      .stars {
        background: url('https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/1697443865461519a169744386546123274_s2.png')
          no-repeat;
        background-size: cover;
      }
    }
    &.head3 {
      background: url('https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/16974441042388b3d169744410423995491_h3.png')
        no-repeat;
      background-size: cover;
      .stars {
        background: url('https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/1697443922497db4e16974439224974597_s3.png')
          no-repeat;
        background-size: cover;
      }
    }
  }
  .answer {
    font-size: 32rpx;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #666666;
    line-height: 32rpx;
    text-align: center;
    margin-bottom: 49rpx;
    margin-top: 39rpx;
  }
  .button {
    width: 360rpx;
    height: 80rpx;
    background: #ffffff;
    border-radius: 40rpx;
    border: 2rpx solid #387dfc;
    font-size: 32rpx;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #387dfc;
    margin: 0 auto;
    margin-bottom: 30rpx;
    position: relative;
    .share {
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      opacity: 0;
    }
  }
  .bottom {
    position: fixed;
    left: 0;
    bottom: 40rpx;
    width: 100%;
    height: 100rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 30rpx;
    border-top: 1px solid #f0f0f0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .btn {
      font-size: 28rpx;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #999999;
      // line-height: 40rpx;
      line-height: 100rpx;
    }
    .active {
      color: #387dfc;
    }
  }
}
</style>
