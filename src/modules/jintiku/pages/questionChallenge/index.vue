<template>
  <view class="questionChallenge">
    <view class="static" @click="goRecord">
      <view class="process">
        <view class="title">完成进度</view>
        <view class="num">{{ staticNum.progress_rate }}%</view>
      </view>
      <view class="process">
        <view class="title">通过关卡</view>
        <view class="num">{{ staticNum.already_checkpoint_number }}</view>
      </view>
      <view class="process">
        <view class="title">获得阳光</view>
        <view class="num">{{ staticNum.already_level_number }}颗</view>
      </view>
    </view>
    <view class="lists">
      <view
        class="list"
        @click="goDetail(item)"
        v-for="(item, i) in list"
        :key="i"
      >
        <!-- 徽章标志 -->
        <view class="bar" :class="{ active: item.is_checked == '1' }">
          {{ i + 1 }}
        </view>
        <view class="list-content">
          <view class="title">
            <view class="chapt">{{ item.name }}</view>
            <!-- <view class="desc">生物化学</view> -->
          </view>
          <view class="content-static">
            <view class="game-card">
              <text class="text">关卡：</text>
              <view class="text-flex">
                <text>{{ item.already_checkpoint_number }}</text>
                <text>/{{ item.checkpoint_number }}</text>
              </view>
            </view>
            <view class="game-card">
              <text class="text">阳光：</text>
              <view class="text-flex">
                <text>{{ item.already_level_number }}</text>
                <text>/{{ item.level_number }}</text>
              </view>
            </view>
          </view>
          <view class="pre-look" v-if="item.is_checked == '1'">上次查看</view>
        </view>
      </view>
    </view>
  </view>
</template>
<script>
import { getChallengelist } from '../../api/questionChallenge'
export default {
  data() {
    return {
      staticNum: {
        already_checkpoint_number: 0,
        already_level_number: 0,
        progress_rate: 0
      },
      list: []
    }
  },
  onShow() {
    this.getList()
  },
  methods: {
    getList() {
      getChallengelist({}).then(data => {
        this.staticNum = data.data
        this.list = data.data.checkpoint_info
      })
    },
    goDetail(item) {
      this.$xh.push(
        'jintiku',
        `pages/questionChallenge/levelList?real_id=${item.id}`
      )
    },
    goRecord() {
      this.$xh.push('jintiku', `pages/questionChallenge/history`)
    }
  },
  onShareAppMessage() {
    return this.$xh.shareAppMessage()
  }
}
</script>
<style scoped lang="less">
.questionChallenge {
  padding: 30rpx;
  min-height: 100vh;
  background-color: #fff;
  .static {
    height: 142rpx;
    background: #eff5ff;
    border-radius: 16rpx;
    display: flex;
    justify-content: space-around;
    align-items: center;
    .process {
      .title {
        font-size: 24rpx;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #999999;
        line-height: 24rpx;
      }
      .num {
        font-size: 28rpx;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #333333;
        line-height: 28rpx;
        text-align: center;
        margin-top: 30rpx;
      }
    }
  }
  .lists {
    .list {
      height: 147rpx;
      display: flex;
      align-items: flex-start;
      padding-top: 39rpx;
      .bar {
        width: 34rpx;
        height: 32rpx;
        margin-top: 6rpx;
        margin-right: 20rpx;
        background: url('https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/16951820529958a9f169518205299511034_jb.png')
          no-repeat;
        background-size: cover;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #444444;
        font-size: 22rpx;
        &.active {
          background: url('https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/16951821179805d78169518211798156048_jba.png')
            no-repeat;
          background-size: cover;
          color: #fff;
        }
      }
      .list-content {
        padding: 32rpx 0;
        padding-top: 0rpx;
        border-bottom: 1px solid #f0f0f0;
        height: 100%;
        position: relative;
        .title {
          display: flex;
          align-items: center;
          font-size: 28rpx;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #333333;
          .chapt {
            margin-right: 4rpx;
          }
        }
        .content-static {
          display: flex;
          align-items: center;
          margin-top: 12rpx;
          .game-card {
            display: flex;
            align-items: center;
            margin-right: 180rpx;
            text {
              font-size: 24rpx;
              font-family: PingFangSC-Regular, PingFang SC;
              font-weight: 400;
              color: #999999;
            }
            .text-flex {
              text:first-child {
                color: #387dfc;
              }
            }
          }
        }
        .pre-look {
          position: absolute;
          right: -20rpx;
          top: 0;
          width: 110rpx;
          height: 35rpx;
          line-height: 35rpx;
          background-color: #ffa4d6;
          color: #fff;
          font-size: 20rpx;
          text-align: center;
          border-top-right-radius: 15rpx;
          border-bottom-left-radius: 15rpx;
        }
      }
    }
  }
}
</style>
