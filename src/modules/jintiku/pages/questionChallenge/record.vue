<template>
  <view class="record">
    <head-height></head-height>
    <view class="head">
      <backbtn />
    </view>
  </view>
</template>
<script>
import headHeight from '../../components/commen/head-height.vue'
import backbtn from '../../components/commen/backbtn.vue'
export default {
  components: {
    headHeight,
    backbtn
  },
  data() {
    return {}
  },
  methods: {
    back() {
      uni.navigateBack({
        delta: 1
      })
    }
  },
  onShareAppMessage() {
    return this.$xh.shareAppMessage()
  }
}
</script>
