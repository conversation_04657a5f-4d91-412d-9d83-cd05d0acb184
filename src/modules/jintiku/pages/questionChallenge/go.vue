<template>
  <view class="go">
    <view class="title"> 真题闯关 </view>
    <view class="desc"> 答题全对赢取三阳开泰 </view>
    <view class="go-btn button" @click="goDetail"></view>
    <view class="nums">
      <view class="num"></view>
      <view class="num"></view>
      <view class="num"></view>
    </view>
  </view>
</template>
<script>
export default {
  data() {
    return {
      knowledge_id: '',
      chapter_id: ''
    }
  },
  onLoad(e) {
    this.knowledge_id = e.knowledge_id
    this.chapter_id = e.chapter_id
  },
  methods: {
    goDetail() {
      this.$xh.push(
        'jintiku',
        `pages/questionChallenge/practise?knowledge_id=${this.knowledge_id}&chapter_id=${this.chapter_id}`
      )
    }
  },
  onShareAppMessage() {
    return this.$xh.shareAppMessage()
  }
}
</script>
<style scoped lang="less">
.go {
  height: 100vh;
  background-color: #439cfc;
  display: flex;
  align-items: center;
  flex-direction: column;
  justify-content: center;
  .title {
    font-size: 48rpx;
    font-family: STSongti-SC-Black, STSongti-SC;
    font-weight: 900;
    color: #ffffff;
    line-height: 48rpx;
    text-shadow: 0px 2rpx 2rpx #2868d8;
    margin-bottom: 40rpx;
  }
  .desc {
    font-size: 32rpx;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #ffffff;
    line-height: 32rpx;
    text-shadow: 0px 2rpx 1rpx #2868d8;
  }
  .go-btn {
    width: 220rpx;
    height: 220rpx;
    background: url('https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/16952003276003ed5169520032760131388_go.png')
      no-repeat;
    background-size: cover;
    margin-top: 120rpx;
    margin-bottom: 30rpx;
  }
  .nums {
    display: flex;
    align-items: center;
    justify-content: center;
    .num {
      width: 220rpx;
      height: 220rpx;
      background: url('https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/1695200524017f9cf169520052401777044_1.png')
        no-repeat;
      background-size: cover;
      margin-right: 30rpx;
    }
    .num:nth-child(2) {
      background: url('https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/169520056071859e8169520056071844772_2.png')
        no-repeat;
      background-size: cover;
    }
    .num:nth-child(3) {
      background: url('https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/16952005858729b6816952005858729918_3.png')
        no-repeat;
      background-size: cover;
      margin-right: 0rpx;
    }
  }
}
</style>
