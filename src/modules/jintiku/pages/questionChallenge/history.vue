<template>
  <view class="page">
    <view class="tabs">
      <view class="tab" :class="{ active: current == 0 }" @click="current = 0">
        闯关记录
      </view>
      <view class="tab" :class="{ active: current == 1 }" @click="current = 1">
        闯关数据
      </view>
    </view>
    <view class="swiper-wrapper">
      <swiper
        class="swiper"
        :indicator-dots="false"
        :autoplay="false"
        :current="current"
        :duration="300"
        :circular="false"
        @change="swiperChange"
      >
        <swiper-item :key="1">
          <view class="swiper-item">
            <scroll-view
              :scroll-top="0"
              scroll-y="true"
              class="scroll-Y"
              @scrolltolower="load"
            >
              <view class="lists">
                <view
                  class="list"
                  v-for="(item, i) in list"
                  :key="i"
                  @click="look(item)"
                >
                  <view class="title"> {{ item.name }} </view>
                  <view class="static flex">
                    <view class="time">2018-09-02</view>
                    <view class="total">
                      共{{ item.question_number }}道题， 做对
                      {{ item.correct_num }}题
                    </view>
                  </view>
                  <image
                    src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/170254457508323ab170254457508368902_right.png"
                    mode="widthFix"
                  />
                </view>
              </view>
              <view class="no-data" v-if="!list.length">
                暂无任何闯关记录哦！
              </view>
            </scroll-view>
          </view>
        </swiper-item>
        <swiper-item :key="2">
          <view class="swiper-item data-site">
            <view class="major"> {{ infoStatic.class_name }} </view>
            <view class="bag">
              <view class="cicle">
                <l-circular-progress
                  :boxWidth="150"
                  :boxHeight="150"
                  class="cicle-main"
                  :percent="getSureNum(infoStatic.task_completion)"
                  :fontShow="false"
                  bgColor="#EDF5FF"
                  :lineWidth="8"
                  progressColor="#387DFC"
                  gradualColor="#95C0FA"
                >
                  <view class="circular-wrapper">
                    <view class="task-title"> 任务完成度 </view>
                    <view class="task-line">
                      {{ getSureNum(infoStatic.task_completion) }}%
                    </view>
                  </view>
                </l-circular-progress>
              </view>
            </view>
            <view class="cgjls">
              <view class="title">
                <view class="line"></view>
                <view class="text">闯关进度</view>
                <view class="num">
                  <text class="active">
                    {{ getSureNum(infoStatic.pass_real_number) }}/
                  </text>
                  <text class="gray">
                    {{ getSureNum(infoStatic.real_number) }}
                  </text>
                  <text>关</text>
                </view>
              </view>
              <view class="tj flex">
                <view class="one">
                  <text>超越</text>
                  <text class="active"
                    >{{ getSureNum(infoStatic.exceed_rate) }}%</text
                  >
                  <text>的学霸</text>
                </view>
                <view class="one">
                  <text>完成度排名</text>
                  <text class="active">{{ getSureNum(infoStatic.rank) }}</text>
                </view>
              </view>
            </view>
            <view class="cgjls">
              <view class="title">
                <view class="line"></view>
                <view class="text">获得阳光</view>
                <view class="num">
                  <text class="active">
                    {{ getSureNum(infoStatic.level_number) }}/
                  </text>
                  <text class="gray">
                    {{ getSureNum(infoStatic.total_level_number) }}
                  </text>
                  <text>颗</text>
                </view>
              </view>
              <view class="tj flex">
                <view class="one">
                  <text>使用时长</text>
                  <text class="active">
                    {{ getSureNum(infoStatic.cost_time) }}
                  </text>
                  <text class="active">小时</text>
                </view>
              </view>
            </view>
            <view class="cgjls">
              <view class="title">
                <view class="line"></view>
                <view class="text">答题量</view>
                <view class="num">
                  <text class="active" style="margin-right: 4rpx">
                    {{ getSureNum(infoStatic.do_number) }}
                  </text>
                  <text>题</text>
                </view>
              </view>
              <view class="tj flex">
                <view class="one">
                  <text>正确率</text>
                  <text class="active">
                    {{ getSureNum(infoStatic.correct_rate) }}%
                  </text>
                </view>
              </view>
            </view>
          </view>
        </swiper-item>
      </swiper>
    </view>
  </view>
</template>
<script>
import lCircularProgress from '../../../../components/l-circular-progress.vue'
import { getRecordList, getRecordData } from '../../api/questionChallenge'
export default {
  components: {
    lCircularProgress
  },
  data() {
    return {
      current: 0,
      list: [],
      chapter_id: '',
      infoStatic: {
        class_name: '', // 专业名称
        correct_rate: '', // 正确率
        cost_time: '0', // 闯关用时
        do_number: '0', // 答题数量
        exceed_rate: '', // 超越百分比
        level_number: '0', // 获得阳光数量
        pass_real_number: '0', // 通过关卡数量
        rank: '0', // 完成度排名
        real_number: '0', // 关卡数量
        task_completion: '', // 任务完成度
        total_level_number: '0' // 总阳光数
      }
    }
  },
  onLoad() {
    this.init()
  },
  methods: {
    init() {
      getRecordList({}).then(data => {
        this.list = data.data.list ? data.data.list : []
        this.chapter_id = data.data.one_real_id
      })
      getRecordData({}).then(data => {
        this.infoStatic = data.data
      })
    },
    swiperChange(e) {
      this.current = e.detail.current
      if (this.current == 0) {
        uni.setNavigationBarTitle({
          title: '闯关记录'
        })
      } else {
        uni.setNavigationBarTitle({
          title: '闯关数据'
        })
      }
    },
    getSureNum(num) {
      return num ? num * 1 : 0
    },
    load() {
      console.log('出发')
    },
    look(item) {
      // 查看解析 this.knowledge_id
      this.$xh.push(
        'jintiku',
        `pages/makeQuestion/lookAnalysisQuestion?type=2&knowledge_id=${item.knowledge_id}&chapter_id=${this.chapter_id}&practice_record_id=${item.practice_record_id}`
      )
    }
  },
  onShareAppMessage() {
    return this.$xh.shareAppMessage()
  }
}
</script>
<style scoped lang="less">
.page {
  height: 100vh;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  .tabs {
    display: flex;
    height: 90rpx;
    align-items: center;
    justify-content: space-around;
    padding: 0 40rpx;
    border-bottom: 1px solid #f0f0f0;
    .tab {
      flex: 1;
      text-align: center;
      height: 90rpx;
      line-height: 90rpx;
      font-size: 32rpx;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      color: #999999;
      line-height: 90rpx;
      &.active {
        color: #333333;
        font-size: 36rpx;
      }
    }
  }
  .swiper-wrapper {
    flex: 1;
    .swiper {
      height: 100%;
      .swiper-item {
        height: 100%;
        .scroll-Y {
          height: 100%;
        }
        .lists {
          .list {
            height: 150rpx;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            align-items: flex-start;
            justify-content: center;
            flex-direction: column;
            padding: 0 30rpx;
            position: relative;
            .title {
              font-size: 28rpx;
              font-family: AppleSystemUIFont;
              color: #333333;
              line-height: 28rpx;
              margin-bottom: 30rpx;
            }
            .time {
              font-size: 24rpx;
              font-family: PingFangSC, PingFang SC;
              font-weight: 500;
              color: #999999;
              line-height: 24rpx;
              margin-right: 20rpx;
            }
            .total {
              font-size: 24rpx;
              font-family: PingFangSC, PingFang SC;
              font-weight: 500;
              color: #999999;
              line-height: 24rpx;
            }
            image {
              width: 19rpx;
              height: 32rpx;
              position: absolute;
              right: 30rpx;
              top: 0;
              bottom: 0;
              margin: auto 0;
            }
          }
        }
      }
      .data-site {
        .major {
          margin-top: 62rpx;
          margin-bottom: 48rpx;
          font-size: 32rpx;
          font-family: AppleSystemUIFont;
          color: #333333;
          line-height: 32rpx;
          text-align: center;
        }
        .bag {
          width: 646rpx;
          height: 281rpx;
          background: url('https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/17025484663631549170254846636385590_bg.png')
            no-repeat left center;
          background-size: contain;
          margin: 0 auto;
          margin-bottom: 50rpx;
          .cicle {
            transform: rotateZ(-90deg);
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            .circular-wrapper {
              width: 100%;
              height: 100%;
              display: flex;
              align-items: center;
              position: absolute;
              left: 0;
              top: 0;
              display: flex;
              flex-direction: column;
              align-items: center;
              justify-content: center;
              transform: rotateZ(90deg);
              .task-title {
                font-size: 28rpx;
                font-family: PingFangSC, PingFang SC;
                font-weight: 500;
                color: #999999;
                line-height: 40rpx;
              }
              .task-line {
                font-size: 72rpx;
                font-family: PingFangSC, PingFang SC;
                font-weight: 500;
                color: #666666;
                line-height: 72rpx;
                margin-top: 14rpx;
              }
            }
          }
        }
        .cgjls {
          background: url('https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/1702550190251876a170255019025168972_bg2.png')
            no-repeat;
          background-size: contain;
          width: 690rpx;
          height: 150rpx;
          margin: 0 auto;
          margin-bottom: 20rpx;
          padding: 30rpx 20rpx;
          .title {
            display: flex;
            align-items: center;
            margin-bottom: 12rpx;
            .line {
              width: 6rpx;
              height: 30rpx;
              background: #387dfc;
              border-radius: 3rpx;
              border-radius: 5rpx;
              margin-right: 10rpx;
            }
            .text {
              font-size: 32rpx;
              font-family: AppleSystemUIFont;
              color: #333333;
              line-height: 32rpx;
            }
            .active {
              color: #387dfc;
              margin-left: 4rpx;
            }
            .gray {
              color: #999999;
              margin-right: 4rpx;
            }
          }
          .tj {
            padding-left: 18rpx;
            .one {
              margin-right: 52rpx;
              text {
                font-size: 28rpx;
                font-family: PingFangSC, PingFang SC;
                font-weight: 500;
                color: #333333;
                line-height: 28rpx;
              }
              .active {
                color: #387dfc;
                margin: 0 4rpx;
              }
            }
          }
        }
      }
    }
  }
}
</style>
