<template>
  <view class="levelList">
    <swiper
      class="swiper"
      :indicator-dots="false"
      :autoplay="false"
      :current="current"
      :duration="300"
      :circular="false"
      previous-margin="50px"
      next-margin="50px"
      @change="swiperChange"
    >
      <swiper-item v-for="(item, index) in lists" :key="index">
        <view class="swiper-item" :class="{ active: index == current }">
          <view class="card">
            <view class="title"> {{ item.name }} </view>
            <view class="suns">
              <view class="sun">
                <image
                  src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/16951943215167c99169519432151692460_active.png"
                  mode="widthFix"
                  v-if="item.level_number * 1 >= 1"
                />
              </view>
              <view class="sun">
                <image
                  src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/16951943215167c99169519432151692460_active.png"
                  mode="widthFix"
                  v-if="item.level_number * 1 >= 2"
                />
              </view>
              <view class="sun">
                <image
                  src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/16951943215167c99169519432151692460_active.png"
                  mode="widthFix"
                  v-if="item.level_number * 1 >= 3"
                />
              </view>
            </view>
            <view class="static"> {{ index + 1 }}/{{ lists.length }} </view>
            <!-- <view class="desc"> 慢性阻塞性肺疾病的节的补充描述 补充描述 </view> -->
            <view
              class="suo-button button"
              :class="{ untie: item.is_pass == '1' || no_pass_key == index }"
              @click="
                goDetail(
                  `pages/questionChallenge/go?knowledge_id=${item.id}&chapter_id=${chapter_id}`,
                  item,
                  index
                )
              "
            >
              <view class="suo">
                <image
                  src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/16951944537499513169519445374910016_suo.png"
                  mode="widthFix"
                />
              </view>
              <text>闯关</text>
            </view>
            <view
              class="suo-button button border"
              :class="{ untie: item.is_pass == '1' || no_pass_key == index }"
              @click="goDetail('pages/examEntry/index', item, index)"
            >
              <view class="suo">
                <image
                  src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/16951944537499513169519445374910016_suo.png"
                  mode="widthFix"
                />
              </view>
              <text>速记</text>
            </view>
          </view>
          <view class="jump" @click="skipFn(item)" v-if="no_pass_key == index">
            跳过
          </view>
        </view>
      </swiper-item>
    </swiper>
    <!-- 跳过的弹框 -->
    <view class="mask" v-if="skip">
      <view class="box">
        <view class="title">是否通过消耗三颗阳光跳过该关卡？</view>
        <view class="flex">
          <view class="button cancel" @click="skip = false"> 下次再说 </view>
          <view class="button success" @click="unblanking">
            跳关
            <!-- <button open-type="share" class="share">跳关</button> -->
          </view>
        </view>
      </view>
    </view>
  </view>
</template>
<script>
import { getCheckpoint, skipChallenge } from '../../api/questionChallenge'
export default {
  data() {
    return {
      lists: [],
      current: 0,
      no_pass_key: 0, // 未解锁的第一个关卡下标志
      skip: false,
      real_id: '',
      chapter_id: '',
      currentData: {}
    }
  },
  onLoad(e) {
    this.real_id = e.real_id
    uni.$on('nextChallenge', data => {
      if (this.current >= this.lists.length - 1) {
        this.$xh.Toast('已经是最后一关了哦！')
        return
      }
      // 下一关
      this.current += 1
      let item = this.lists[this.current]
      // this.goDetail(
      //   `pages/questionChallenge/go?knowledge_id=${item.id}&chapter_id=${this.chapter_id}`,
      //   item,
      //   this.current
      // )
      this.$xh.push(
        'jintiku',
        `pages/questionChallenge/go?knowledge_id=${item.id}&chapter_id=${this.chapter_id}`
      )
    })
  },
  methods: {
    swiperChange(e) {
      this.current = e.detail.current
    },
    goDetail(url, item, index) {
      if (!this.isPass(item, index)) {
        return
      }
      this.$xh.push('jintiku', url)
    },
    skipFn(item) {
      if (item.level_number < 3) {
        this.$xh.Toast('抱歉，跳过该关卡需要收集三颗阳光哦！')
        return
      }
      this.currentData = item
      this.skip = true
    },
    unblanking() {
      // 解锁
      skipChallenge({
        real_id: this.real_id
      }).then(data => {
        this.$xh.Toast('解锁成功!')
        this.skip = false
        this.getInfo()
      })
    },
    getInfo() {
      getCheckpoint({
        real_id: this.real_id
      }).then(data => {
        let res = data.data
        this.current = res.no_pass_key
        this.no_pass_key = res.no_pass_key
        this.lists = res.list
        this.chapter_id = res.chapter_id
      })
    },
    isPass(item, index) {
      // 检测该关卡是否可以通过
      if (item.is_pass == '1' || this.no_pass_key == index) {
        return true
      } else {
        this.$xh.Toast('解锁条件：通过上一关卡！')
        return false
      }
    }
  },
  onShow() {
    this.getInfo()
  },
  onShareAppMessage() {
    console.log('分享了，进行解锁操作')
    this.unblanking()
    // TODO
    return {
      title: '分享！！！',
      content: '解锁',
      desc: '我要解锁'
    }
  }
}
</script>

<style scoped lang="less">
.levelList {
  height: 100vh;
  overflow: hidden;
  background: linear-gradient(180deg, #45a0fc 0%, #387efc 100%);
  .swiper {
    height: 1000rpx;
    margin-top: 160rpx;
    .swiper-item {
      width: 560rpx;
      height: 830rpx;
      background-color: #fff;
      margin: 0 auto;
      margin-top: 60rpx;
      border-radius: 28rpx;
      transition: all 0.25s;
      transform: scale(0.9);
      position: relative;
      .jump {
        position: absolute;
        width: 64rpx;
        height: 48rpx;
        right: 40rpx;
        top: -48rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24rpx;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #ffffff;
        background: #ffb800;
        border-radius: 10rpx;
        border-bottom-left-radius: 0;
        border-bottom-right-radius: 0;
      }
      .card {
        padding: 60rpx 45rpx;
        .title {
          font-size: 32rpx;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #333333;
          line-height: 32rpx;
          text-align: center;
          margin-top: 20rpx;
          margin-bottom: 60rpx;
        }
        .suns {
          display: flex;
          align-items: center;
          justify-content: center;
          .sun {
            width: 88rpx;
            height: 88rpx;
            background: url('https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/16951942978353d6b169519429783586353_sun.png')
              no-repeat;
            background-size: cover;
            margin-right: 22rpx;
            &:last-child {
              margin-right: 0;
            }
            &:nth-child(2) {
              position: relative;
              top: -20rpx;
            }
            image {
              width: 100%;
              height: 100%;
            }
          }
        }
        .static {
          font-size: 32rpx;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #333333;
          line-height: 32rpx;
          margin-bottom: 160rpx;
          text-align: center;
        }
        .desc {
          text-align: center;
          font-size: 28rpx;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #333333;
          line-height: 42rpx;
          margin-bottom: 58rpx;
        }
        .suo-button {
          height: 72rpx;
          line-height: 72rpx;
          background: #dddddd;
          border-radius: 36px;
          text-align: center;
          position: relative;
          margin-bottom: 24rpx;
          .suo {
            width: 56rpx;
            height: 56rpx;
            background: rgba(0, 0, 0, 0.1);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            position: absolute;
            left: 6rpx;
            top: 0;
            bottom: 0;
            margin: auto 0;
            image {
              width: 32rpx;
              height: 34rpx;
            }
          }
          text {
            color: #fff;
          }
        }
        .untie {
          background: #387dfc;
          &.border {
            background: #fff;
            border: 1px solid #387dfc;
            text {
              color: #387dfc;
            }
          }
          .suo {
            display: none;
          }
        }
      }
    }
    .active {
      transform: scale(1);
    }
  }
  .mask {
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.3);
    position: fixed;
    left: 0;
    top: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    .box {
      width: 70%;
      height: 300rpx;
      background-color: #fff;
      border-radius: 20rpx;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      .title {
        height: 160rpx;
        line-height: 160rpx;
        text-align: center;
        color: #333;
      }
      .flex {
        border-top: 1px solid #eee;
        .button {
          flex: 1;
          text-align: center;
          font-size: 28rpx;
          position: relative;
          height: 80rpx;
          line-height: 80rpx;
        }
        .cancel {
          border-right: 1px solid #eee;
          color: #ccc;
        }
        .share {
          position: absolute;
          left: 0;
          top: 0;
          width: 100%;
          height: 100%;
          opacity: 0;
        }
      }
    }
  }
}
</style>
