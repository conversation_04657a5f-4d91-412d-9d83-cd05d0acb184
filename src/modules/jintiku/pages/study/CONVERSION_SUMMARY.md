# Study模块技术栈转换总结

## 转换概述

将 `src/modules/jintiku/pages/study` 文件夹中的代码从H5技术栈转换为UniApp技术栈。

## 主要转换内容

### 1. 模板语法转换
- `<div>` → `<view>`
- `<img>` → `<image>`
- `<span>` → `<text>`
- `<iframe>` → `<web-view>` (仅在pdf/index.vue中)
- 移除了可选链操作符 `?.` 改为显式判断
- 添加了 `:key` 属性到 `v-for` 循环中

### 2. 脚本语法转换
- Vue 3 Composition API → Vue 2 Options API
- `<script setup lang="ts">` → `<script>`
- 移除了 TypeScript 类型注解
- `useHead()` → `uni.setNavigationBarTitle()`
- `useRoute()` → `onLoad(options)`
- `useRouter()` → `this.$xh.push()`
- `onMounted()` → `onLoad()`
- `ref()` → `data()` 中的响应式数据
- 路由监听 → `onShow()` 生命周期

### 3. 样式转换
- `lang="scss"` → `lang="less"`

### 4. API调用转换
- 原H5项目中的 `study` API对象在当前项目中不存在
- 将相关API调用改为TODO注释和console.log占位符
- 保留了 `getToken` API的正常调用

### 5. 状态管理转换
- `useStore.user()` → `this.$store.state.jintiku`
- 使用Vuex的commit方法进行状态更新

### 6. 路由跳转转换
- `router.push()` → `this.$xh.push()`

## 转换后的文件列表

1. **index.vue** - 主页面，包含学习中心的主要功能
2. **detail/index.vue** - 课程详情页面
3. **video/index.vue** - 视频回放页面
4. **live/index.vue** - 直播页面
5. **myCourse/index.vue** - 我的课程页面
6. **pdf/index.vue** - PDF预览页面
7. **dataDownload/index.vue** - 资料下载页面

## ✅ 已完成的修复内容

### API接口修复
已在 `src/modules/jintiku/api/index.js` 中添加了学习相关API：

```javascript
// 学习中心相关API
// 获取学习日历
export const calendar = function (data = {}) {
  return http({
    url: '/c/study/learning/calendar',
    method: 'GET',
    data
  })
}

// 获取日期课节
export const dateLessons = function (data = {}) {
  return http({
    url: '/c/study/learning/lesson',
    method: 'GET',
    data
  })
}

// 获取日期课程
export const dateCourse = function (data = {}) {
  return http({
    url: '/c/study/learning/plan',
    method: 'GET',
    data
  })
}

// 添加学习数据
export const addStudyData = function (data = {}) {
  return http({
    url: '/c/live/data/add',
    method: 'POST',
    data
  })
}

// 手机号登录获取token
export const getTokenByPhone = function (data = {}) {
  return http({
    url: '/c/student/login/appphone',
    method: 'POST',
    data,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    }
  })
}
```

### Store状态修复
已在 `src/modules/jintiku/store/index.js` 中添加了状态管理：

```javascript
state: {
  from: '', // 来源平台
  phoneCode: '', // 手机验证码
  // ... 其他状态
}

mutations: {
  setFrom(store, from) {
    store.from = from
  },
  setPhoneCode(store, phoneCode) {
    store.phoneCode = phoneCode
  },
  // ... 其他mutations
}
```

### API调用修复
- 修复了 `index.vue` 中的所有API调用，使用正确的API函数
- 修复了 `video/index.vue` 和 `live/index.vue` 中的学习数据记录API
- 所有API调用都已按照wb-1page项目的逻辑进行实现

### 组件依赖
确保以下组件存在并正常工作：
- `ModuleStudySelectDate`
- `ModuleStudyLessonsList`
- `ModuleStudyCourseList`
- `ModuleStudyNotLearn`
- `ModuleStudyDetail`
- `ModuleStudyVideoBaijiayunPlayback`
- `ModuleStudyLiveBaijiayunLive`
- `ModuleStudyMyCourse`
- `ModuleStudyDataDownload`

## 注意事项

1. ✅ 所有API调用已按照wb-1page项目的逻辑实现
2. 组件引用需要确保对应的组件文件存在
3. 路由跳转使用了项目的自定义路由方法 `this.$xh.push()`
4. 保留了Vant UI组件的使用（如 `<van-loading />`）
5. 定时器在页面卸载时需要清理，已在相关页面添加 `onUnload` 生命周期
6. ✅ Store状态管理已添加from和phoneCode支持

## 修复完成状态

### ✅ 已完成
- [x] Vue 3 → Vue 2 语法转换
- [x] Composition API → Options API 转换
- [x] 模板语法转换（div → view等）
- [x] API接口实现（参考wb-1page项目）
- [x] Store状态管理修复
- [x] 学习数据记录API修复
- [x] 路由跳转修复

### 📋 待验证
- [ ] 组件依赖是否存在
- [ ] 后端API接口是否正常
- [ ] 不同平台兼容性测试

## 测试建议

1. 测试页面基本渲染
2. 测试路由跳转功能
3. 测试API调用（所有API已实现）
4. 测试组件交互
5. 测试在不同平台（小程序、H5、App）的兼容性
6. 测试学习数据记录功能
7. 测试日历和课程数据获取
