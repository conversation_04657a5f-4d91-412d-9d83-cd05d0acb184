<template>
  <view class="wrap">
    <!-- UniApp中使用web-view组件来显示网页 -->
    <web-view :src="url"></web-view>
  </view>
</template>

<script>
export default {
  name: "StudyPdf",
  data() {
    return {
      url: '',
    };
  },
  onLoad(options) {
    // 设置页面标题
    uni.setNavigationBarTitle({
      title: '文件预览'
    });

    // 构建PDF预览URL
    if (options.pdfUrl) {
      this.url = '/pdf/web/viewer.html?file=' + options.pdfUrl
    }
  }
};
</script>

<style lang="less" scoped>
.wrap {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  bottom: 0;
}
</style>

