<template>
	<view class="study">
		<view class="nav-bar"></view>
		<!-- 自定义导航栏 -->
		<view class="header-box">
			<view class="header">
				<!-- 状态栏占位 -->
				<view class="status-bar"></view>
				<!-- 导航栏内容 -->
				<view class="header-content">
					<text class="header-title">上课</text>
				</view>
			</view>
		</view>
		<ModuleStudySelectDate :dotArr="dotArr" :selected="selected" fixed @change="changeSelected"
			@more="showSelectTeachingType = false" />
		<view v-if="loading" style="text-align: center; margin-top: 50px">
			<van-loading />
		</view>
		<ModuleStudyLessonsList v-if="lessonsList && lessonsList.lesson_num && lessonsList.lesson_num != '0'"
			:info="lessonsList" />
		<ModuleStudyCourseList
			v-if="lessonsList && lessonsList.lesson_attendance && lessonsList.lesson_attendance.length"
			v-model="showSelectTeachingType" :list="courseList" :loading="courseListLoading"
			:teaching_type="teaching_type" />
		<ModuleStudyNotLearn v-if="
        (!lessonsList || !lessonsList.lesson_attendance || !lessonsList.lesson_attendance.length) &&
        !courseList.length &&
        !loading
      " />
		<view v-if="showSelectTeachingType" class="mask" @click="showSelectTeachingType = false"
			@touchmove.stop.prevent>
			<view class="study-plan" @click.stop="stopPropagation">
				<view class="study-plan-box">
					<view class="study-plan-title">
						<view class="line"></view>
						<text>学习计划</text>
					</view>
					<view class="study-plan-middle">
						<view class="select-teaching-type">
							<view class="study-plan-middle-left-text">
								<text style="font-weight: 600">{{
                  teaching_type
                    ? getTeachingTypeName(teaching_type)
                    : "授课形式"
                }}</text>
								<image class="select-teaching-type-img"
									src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/3b9a173314148529583348_jiao1.png" />
							</view>
						</view>
						<view class="my-course" @click="goMyCourse">
							<image class="my-course-img"
								src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/652517359747899528889_wodekecheng.png" />
							<text>我的课程</text>
						</view>
					</view>
				</view>
				<view class="study-plan-select-list">
					<view v-for="item in teachingTypeOption" :key="item.id" :class="{
              'study-plan-select-list-item-active': teaching_type == item.id,
            }" class="study-plan-select-list-item" @click="teachingTypeChange(item.id)">
						{{ item.name }}
					</view>
				</view>
			</view>
		</view>
	</view>
</template>
<script>
	// url 参数
	// code：手机号
	// from：来自什么平台：安卓"and"、苹果"ios"
	import {
		getTokenByPhone,
		calendar,
		dateLessons,
		dateCourse
	} from '../../api/index'

	// 导入组件
	import ModuleStudySelectDate from '../../components/study/selectDate.vue'
	import ModuleStudyLessonsList from '../../components/study/lessonsList.vue'
	import ModuleStudyCourseList from '../../components/study/courseList.vue'
	import ModuleStudyNotLearn from '../../components/study/notLearn.vue'

	function getNowFormatDate() {
		const date = new Date(+new Date());
		const seperator1 = "-";
		const year = date.getFullYear();
		let month = date.getMonth() + 1;
		let strDate = date.getDate();
		if (month >= 1 && month <= 9) {
			month = "0" + month;
		}
		if (strDate >= 0 && strDate <= 9) {
			strDate = "0" + strDate;
		}
		const currentdate = year + seperator1 + month + seperator1 + strDate;
		return currentdate;
	}

	export default {
		name: "StudyIndex",
		components: {
			ModuleStudySelectDate,
			ModuleStudyLessonsList,
			ModuleStudyCourseList,
			ModuleStudyNotLearn
		},
		data() {
			return {
				selected: getNowFormatDate(),
				teachingTypeOption: [{
						name: "全部",
						id: ""
					},
					{
						name: "录播",
						id: "3"
					},
					{
						name: "直播",
						id: "1"
					},
					{
						name: "面授",
						id: "2"
					},
				],
				teaching_type: "",
				showSelectTeachingType: false,
				lessonsList: {},
				courseList: [],
				courseListLoading: false,
				loading: true,
				dotArr: [],
				statusBarHeight: 0, // 状态栏高度
			};
		},
		onLoad(options) {
			// 获取系统信息，设置状态栏高度
			const systemInfo = uni.getSystemInfoSync();
			this.statusBarHeight = systemInfo.statusBarHeight || 0;

			// 设置CSS变量（仅在H5环境下）
			// #ifdef H5
			if (typeof document !== 'undefined' && document.documentElement) {
				document.documentElement.style.setProperty('--status-bar-height', this.statusBarHeight + 'px');
			}
			// #endif

			// 设置页面标题和导航栏样式
			uni.setNavigationBarTitle({
				title: '学习中心'
			});

			// #ifdef H5
			// H5环境下设置导航栏颜色
			uni.setNavigationBarColor({
				frontColor: '#000000',
				backgroundColor: '#ffffff'
			});
			// #endif

			// 处理路由参数
			if (options.from) {
				this.$store.commit('jintiku/setFrom', options.from);
			}
			if (options.phone) {
				this.$store.commit('jintiku/setPhoneCode', options.phone);
			}
			this.isLogin();
		},
		onShow() {
			// 页面显示时检查是否需要刷新数据
			this.getData();
		},
		methods: {
			// 阻止事件冒泡
			stopPropagation() {
				// 空函数，用于阻止事件冒泡
			},
			// 获取授课形式名称
			getTeachingTypeName(id) {
				const item = this.teachingTypeOption.find(item => item.id == id);
				return item ? item.name : '';
			},
			isLogin() {
				let self = this;
				// 检查登录状态
				if (!this.$store.state.jintiku.token) {
					// 如果有phone参数，尝试获取token
					const options = this.$mp && this.$mp.query ? this.$mp.query : {};
					if (options.phone) {
						let token = options.phone.replace(/\s/g, "+") + "";
						console.log(options.phone, "未处理空格");
						console.log(token, "已处理空格");
						getTokenByPhone({
								token,
							})
							.then(({
								data
							}) => {
								// 保存用户信息到store
								this.$store.commit('jintiku/setToken', data.token);
								this.$store.commit('jintiku/setUserinfo', data);
								self.getData();
							})
							.catch((err) => {
								console.log(err);
								uni.showToast({
									title: err.msg && err.msg[0] ? err.msg[0] : '登录失败',
									icon: 'none'
								});
								if (err.code == 100002) {
									// 跳转到登录页面
									this.$xh.push('jintiku', 'pages/login/index');
								}
							});
					} else {
						self.getData();
					}
				} else {
					self.getData();
				}
			},
			// 返回指定日期的所在月份的第一天与最后一天
			getMonthFirstAndLastDay(dateStr) {
				const date = new Date(dateStr);
				const year = date.getFullYear();
				const month = date.getMonth();

				// 指定月的第一天
				const firstDay = new Date(year, month, 1);
				const firstDayFormatted = `${year}-${String(
        firstDay.getMonth() + 1
      ).padStart(2, "0")}-${String(firstDay.getDate()).padStart(2, "0")}`;

				// 指定月的最后一天
				const lastDay = new Date(year, month + 1, 0);
				const lastDayFormatted = `${year}-${String(
        lastDay.getMonth() + 1
      ).padStart(2, "0")}-${String(lastDay.getDate()).padStart(2, "0")}`;

				return [firstDayFormatted, lastDayFormatted];
			},
			// 返回指定日期的前一个月的第一天与后一个月的最后一天
			getPrevMonthFirstAndNextMonthLastDay(dateStr) {
				const date = new Date(dateStr);
				const year = date.getFullYear();
				const month = date.getMonth();

				// 前一个月的第一天
				const prevMonthFirstDay = new Date(year, month - 1, 1);
				const prevMonthFirstDayFormatted = `${prevMonthFirstDay.getFullYear()}-${String(
        prevMonthFirstDay.getMonth() + 1
      ).padStart(2, "0")}-${String(prevMonthFirstDay.getDate()).padStart(
        2,
        "0"
      )}`;

				// 后一个月的第一天
				const nextMonthFirstDay = new Date(year, month + 2, 0);
				const nextMonthFirstDayFormatted = `${nextMonthFirstDay.getFullYear()}-${String(
        nextMonthFirstDay.getMonth() + 1
      ).padStart(2, "0")}-${String(nextMonthFirstDay.getDate()).padStart(
        2,
        "0"
      )}`;

				return [prevMonthFirstDayFormatted, nextMonthFirstDayFormatted];
			},
			getCalendar() {
				let [start_date, end_date] = this.getPrevMonthFirstAndNextMonthLastDay(
					new Date()
				);
				calendar({
						end_date,
						start_date,
					})
					.then((res) => {
						if (res.data && res.data.length) {
							this.dotArr = res.data
								.filter((item) => item.status == "1")
								.map((item) => item.date);
						} else {
							this.dotArr = [];
						}
					})
					.catch((err) => {
						console.log(err);
						uni.showToast({
							title: err.msg && err.msg[0] ? err.msg[0] : '获取日历失败',
							icon: 'none'
						});
						if (err.code == 100002) {
							this.$xh.push('jintiku', 'pages/login/index');
						}
					});
			},
			getDateLessons() {
				dateLessons({
						date: this.selected,
					})
					.then(({
						data
					}) => {
						this.lessonsList = data || {};
						if (
							this.lessonsList &&
							this.lessonsList.lesson_attendance &&
							this.lessonsList.lesson_attendance.length
						) {
							this.lessonsList.lesson_attendance =
								this.lessonsList.lesson_attendance.map((item) => {
									return {
										...item,
										evaluation_type_top: item.evaluation_type.filter(
											(btn) =>
											btn.is_separate == "2" &&
											btn.paper_version_id &&
											btn.paper_version_id != "0"
										),
										evaluation_type_bottom: item.evaluation_type.filter(
											(btn) =>
											btn.is_separate == "1" &&
											btn.paper_version_id &&
											btn.paper_version_id != "0"
										),
									};
								});
						}
					})
					.catch((err) => {
						console.log(err);
						uni.showToast({
							title: err.msg && err.msg[0] ? err.msg[0] : '获取课程失败',
							icon: 'none'
						});
						if (err.code == 100002) {
							this.$xh.push('jintiku', 'pages/login/index');
						}
					})
					.finally(() => {
						this.loading = false;
					});
			},
			getDateCourse() {
				this.courseListLoading = true;
				dateCourse({
						teaching_type: this.teaching_type,
						date: this.selected,
					})
					.then(({
						data
					}) => {
						if (data && data.length) {
							this.courseList = data.map((item) => {
								let teacher = [];
								// 教师只展示前两个
								if (item.class.teacher && item.class.teacher.length) {
									if (item.class.teacher.length > 2) {
										teacher = item.class.teacher.slice(0, 2);
									} else {
										teacher = item.class.teacher;
									}
								}
								return {
									...item,
									evaluation_type: item.evaluation_type.filter(
										(btn) => btn.paper_version_id && btn.paper_version_id != "0"
									),
									class: {
										...item.class,
											teacher,
									},
								};
							});
						} else {
							this.courseList = [];
						}
					})
					.catch((err) => {
						console.log(err);
						uni.showToast({
							title: err.msg && err.msg[0] ? err.msg[0] : '获取课程失败',
							icon: 'none'
						});
						if (err.code == 100002) {
							this.$xh.push('jintiku', 'pages/login/index');
						}
					})
					.finally(() => {
						this.courseListLoading = false;
						this.loading = false;
					});
			},
			getData() {
				this.loading = true;
				this.getCalendar();
				this.getDateLessons();
				this.getDateCourse();
			},
			changeSelected(val) {
				this.selected = val;
				this.loading = true;
				this.lessonsList = {};
				this.courseList = [];
				this.getDateLessons();
				this.getDateCourse();
			},
			teachingTypeChange(val) {
				this.teaching_type = val;
				this.showSelectTeachingType = false;
				this.getDateCourse();
			},
			goMyCourse() {
				this.$xh.push('jintiku', 'pages/study/myCourse/index');
			},
		},
	};
</script>
<style lang="less" scoped>
	.study {
		background-color: #f2f5f7;
		min-height: 100vh;
		width: 100vw;
		padding-bottom: 40px;
		// .nav-bar {
		//   /* #ifdef MP */
		//   height: var(--status-bar-height, 44px);
		//   /* #endif */

		//   /* #ifdef H5 */
		//   height: 0;
		//   /* #endif */
		// }

		.header-box {
			position: fixed;
			top: 0;
			left: 0;
			right: 0;
			z-index: 999;
			background: #fff;
			/* #ifdef MP */
			padding-top: 80rpx;
			/* #endif */

			/* #ifdef H5 */
			padding-top: 0rpx;
			/* #endif */

			.header {
				height: calc(96rpx);
			}

			// .status-bar {
			// 	/* #ifdef MP */
			// 	height: var(--status-bar-height, 44px);
			// 	/* #endif */

			// 	/* #ifdef H5 */
			// 	height: 0;
			// 	/* #endif */
			// }

			.header-content {
				height: 88rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				position: relative;

				.header-title {
					font-size: 36rpx;
					font-weight: 600;
					color: #333;
				}
			}
		}

		.mask {
			box-sizing: border-box;
			padding-top: 54px;
			background: rgba(0, 0, 0, 0.5);
			position: fixed;
			left: 0;
			top: 0;
			width: 100vw;
			height: 100vh;
			z-index: 2;

			.study-plan {
				.study-plan-box {
					background: #f2f5f7;
					padding-bottom: 14px;
					padding-top: 18px;

					.study-plan-title {
						display: flex;
						justify-content: flex-start;
						align-items: center;
						padding-right: 15px;
						padding-left: 15px;
						margin-bottom: 16px;
						font-weight: 600;
						font-size: 15px;
						color: #262629;

						span {
							font-weight: 600;
						}

						.line {
							width: 4px;
							height: 16px;
							background: #018CFF;
							border-radius: 3px;
							margin-right: 6px;
						}
					}

					.study-plan-middle {
						padding-right: 15px;
						padding-left: 15px;
						display: flex;
						justify-content: space-between;
						align-items: flex-end;

						.select-teaching-type {
							font-weight: 500;
							font-size: 15px;
							color: #262629;

							.select-teaching-type-img {
								width: 6px;
								height: 6px;
								margin-left: 3px;
								transform: translateY(4rpx);
							}
						}

						.my-course {
							font-weight: 400;
							font-size: 14px;
							color: rgba(3, 32, 61, 0.75);
							display: flex;
							align-items: center;
							justify-content: flex-start;

							.my-course-img {
								width: 14px;
								height: 14px;
								margin-right: 3px;
							}
						}
					}
				}

				.study-plan-select-list {
					border-radius: 0px 0px 10px 10px;
					padding: 24px 15px 4px 15px;
					background: #fff;
					display: flex;
					justify-content: space-between;
					align-items: center;
					flex-wrap: wrap;

					.study-plan-select-list-item {
						width: 94px;
						height: 34px;
						background: #eceef0;
						border-radius: 22px;
						font-weight: 400;
						font-size: 14px;
						line-height: 34px;
						color: rgba(3, 32, 61, 0.85);
						text-align: center;
						margin-bottom: 20px;
					}

					.study-plan-select-list-item-active {
						background: rgba(1, 163, 99, 0.14);
						color: #018CFF;
					}
				}
			}
		}
	}
</style>