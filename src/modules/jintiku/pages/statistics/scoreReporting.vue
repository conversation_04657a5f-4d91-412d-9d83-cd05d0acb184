<template>
  <view class="score_reporting">
    <view class="header-box">
      <view class="header">
        <image
          src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/169509193147644ed169509193147782088_back.png"
          mode="widthFix"
          class="back"
          @click="back"
        />
        <view class="major">
          <text>{{ student_name }}的成绩报告</text>
        </view>
      </view>
    </view>
    <view class="data_list">
      <view class="evaluate list_box">
        <view class="title_text">你的综合实力</view>
        <view class="evaluate_text">
          <!-- {{ transform(data.session_score.is_pass) }} -->
          <text
            v-if="
              data.session_score.is_pass == 1 &&
              data.session_score.is_correct == 1
            "
          >
            及格
          </text>
          <text
            v-if="
              data.session_score.is_pass == 2 &&
              data.session_score.is_correct == 1
            "
          >
            不及格
          </text>
          <text v-if="data.session_score.is_correct == 2"> 阅卷中 </text>
        </view>
        <view class="session">{{ data.session_score.examination_round }}</view>
        <view class="grade">
          <view class="grade_number">{{
            data.session_score.is_correct == 1
              ? `${data.session_score.score}`
              : '阅卷中'
          }}</view>
          <!-- <view class="grade_text">分数</view> -->
        </view>
      </view>
      <view class="list_box">
        <view class="title">成绩明细</view>
        <view class="content_list">
          <!-- <view style="width: 80rpx">
            <view class="name">成绩</view>
            <view class="number"
              >{{
                data.session_score.is_correct == 1
                  ? `${data.session_score.score}分`
                  : '阅卷中'
              }}
            </view>
          </view> -->
          <view>
            <view class="name">客观题得分</view>
            <view class="number">
              {{ data.session_score.objective_item_score || '-' }}分
            </view>
          </view>
          <view>
            <view class="name">主观题得分</view>
            <view class="number">
              {{
                data.session_score.is_correct == 1
                  ? `${data.session_score.subjective_item_score}分`
                  : '待阅卷'
              }}
            </view>
          </view>
          <view>
            <view class="name">试卷满分</view>
            <view class="number">
              {{ data.session_score.full_mark_score || '-' }}分
            </view>
          </view>
          <view>
            <view class="name">及格分</view>
            <view class="number">
              {{ data.session_score.passing_score || '-' }}分
            </view>
          </view>
        </view>
      </view>
      <view class="list_box">
        <view class="title">排名</view>
        <view class="content_list">
          <view>
            <view class="name">排名</view>
            <view class="number">
              <text
                style="flex-shrink: 0"
                v-if="data.session_score.is_random == 2"
              >
                {{
                  data.rank_info.rank == '0' ? '待更新' : data.rank_info.rank
                }}
              </text>
              <text
                style="flex-shrink: 0"
                v-else-if="data.session_score.is_random == 1"
              >
                -
              </text>
            </view>
          </view>
          <view> </view>
          <view>
            <view class="name">环比排名</view>
            <view
              class="number up"
              v-if="
                data.rank_info.rank_status == 1 &&
                data.rank_info.rank_change_num != 0 &&
                data.session_score.is_random == 2 &&
                data.session_score.is_correct == '1'
              "
            >
              上升
              {{ data.rank_info.rank_change_num || '-' }}
            </view>
            <view
              class="number down"
              v-if="
                data.rank_info.rank_status == 3 &&
                data.rank_info.rank_change_num != 0 &&
                data.session_score.is_random == 2 &&
                data.session_score.is_correct == '1'
              "
            >
              下降
              {{ data.rank_info.rank_change_num || '-' }}
            </view>
            <view
              class="number"
              v-if="
                data.rank_info.rank_change_num == 0 &&
                data.session_score.is_random == 2 &&
                data.session_score.is_correct == '1'
              "
            >
              首场考试
            </view>
            <view
              class="number up"
              v-if="
                data.rank_info.rank_status == 2 &&
                data.rank_info.rank_change_num != 0 &&
                data.session_score.is_random == 2
              "
            >
              --
              {{ data.rank_info.rank_change_num || '-' }}
            </view>
            <view
              class="number"
              v-if="
                data.session_score.is_random == 2 &&
                data.session_score.is_correct == '2'
              "
              >待更新</view
            >
            <view class="number up" v-if="data.session_score.is_random == 1">
              --
            </view>
          </view>
          <!-- <view style="width: 103rpx"> </view> -->
        </view>
      </view>
      <view class="list_box" v-if="isShowSF">
        <view class="title">失分试题</view>
        <view>
          <table v-if="data.lose_points_question.length">
            <tr style="border: 0 none">
              <th style="" class="line line1">题号</th>
              <th style="" class="line2">得分</th>
              <th style="" class="line3">对应知识点</th>
            </tr>
            <view>
              <tr
                v-for="(item, index) in data.lose_points_question"
                class="lose_list"
                :key="index"
              >
                <td style="width: 120rpx; border-right: 0 none">
                  {{ item.sort || '-' }}
                </td>
                <td style="width: 120rpx; border-right: 0 none">
                  {{ item.get_score || '-' }}/{{ item.score || '-' }}
                </td>
                <td style="width: 100%">{{ item.knowledge || '-' }}</td>
              </tr>
            </view>
          </table>
          <view v-else style="display: flex; justify-content: center">
            <img
              style="width: 384rpx; height: 44rpx; margin-top: 22rpx"
              src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/datatpls/%E7%BC%96%E7%BB%84%2010%402x.png"
            />
          </view>
        </view>
      </view>
      <view class="list_box">
        <view class="title" style="margin-bottom: 26rpx">答题详情</view>
        <answer-particulars
          :data="data.answer_question_details"
          :isCorrect="data.session_score.is_correct"
          :isPublishAnswer="data.session_score.is_publish_answer"
        />
      </view>
      <view class="not_more">没有更多啦~</view>
    </view>
    <view class="background"></view>
  </view>
</template>
<script>
import answerParticulars from '../../components/makeQuestion/answer-particulars.vue'
import { examScorereporting } from '../../api'
import { isSubjective } from '../../utils/index'
export default {
  components: { answerParticulars },
  props: {},
  data() {
    return {
      data: {},
      statusBarHeight: 40,
      examination_id: '',
      examination_session_id: '',
      paper_version_id: '',
      exam_paper_id: '',
      user_id: '',
      student_name: ''
    }
  },
  computed: {
    isShowSF() {
      if (!this.data.answer_question_details) {
        return false
      }
      let res = this.data.answer_question_details.filter(item => {
        return !isSubjective(item.question_type)
      })
      return !!res.length
    }
  },
  onShow: function () {
    this.init()
  },
  methods: {
    init() {
      let data = {
        examination_id: this.examination_id,
        examination_session_id: this.examination_session_id,
        paper_version_id: this.paper_version_id,
        exam_paper_id: this.exam_paper_id,
        user_id: this.user_id
      }
      examScorereporting(data).then(res => {
        this.data = res.data
      })

      let userInfo = uni.getStorageSync('__xingyun_userinfo__')
      if (userInfo.student_name == '未填写') {
        userInfo.student_name = '未填写姓名'
      }
      this.student_name = userInfo.nickname || userInfo.student_name
    },
    back() {
      uni.navigateBack({
        delta: 1
      })
    }
    // transform(data) {
    //   switch (data) {
    //     case '1':
    //       return '及格'
    //     case '2':
    //       return '不及格'
    //     default:
    //       return '-'
    //   }
    // }
  },
  onLoad: function (e) {
    // `${url}?examination_id=${citem.mock_id}&examination_session_id=${item.id}&paper_version_id=${citem.exam_paper_id}&user_id=${userId}`
    this.examination_id = e.examination_id
    this.examination_session_id = e.examination_session_id
    this.paper_version_id = e.paper_version_id
    this.exam_paper_id = e.exam_paper_id
    this.user_id = e.user_id
  },
  onShareAppMessage() {
    return this.$xh.shareAppMessage()
  }
}
</script>
<style scoped lang="less">
.score_reporting {
  position: relative;
  .header-box {
    padding-top: 80rpx;
    // height: 605rpx;
    position: fixed;
    left: 0;
    top: 0;
    width: 100%;
    z-index: 2;
    background: #2e68ff;
    // background: linear-gradient(
    //   180deg,
    //   #2E68FF 0%,
    //   #5197fe 43%,
    //   rgba(255, 255, 255, 0) 100%
    // );

    .header {
      position: relative;
      height: calc(96rpx);
      display: flex;
      align-items: center;
      justify-content: center;
      // margin-top: --status-bar-height;
      .back {
        position: absolute;
        width: 20rpx;
        height: 32rpx;
        left: 0rpx;
        top: 0;
        bottom: 0;
        margin: auto 0;
        z-index: 10;
        padding: 10rpx 30rpx;
      }

      .major {
        display: flex;
        align-items: center;
        justify-content: center;
        text {
          font-size: 32rpx;
          font-family: PingFangSC-Medium, PingFang SC;
          font-weight: 500;
          color: #fff;
          line-height: 36rpx;
        }
      }
    }
  }
  .data_list {
    margin-top: 220rpx;
    z-index: 1;
    position: relative;
    padding: 32rpx 24rpx;
    // background: linear-gradient(
    //   180deg,
    //   #2E68FF 0%,
    //   #5197fe 43%,
    //   rgba(255, 255, 255, 0) 100%
    // );
    .list_box {
      padding: 30rpx 32rpx;
      background: #ffffff;
      border-radius: 12rpx;
      margin-bottom: 32rpx;
      .title {
        font-size: 32rpx;
        color: #161f30;
        font-weight: 500;
      }
      .content_list {
        width: 100%;
        padding: 20rpx;
        background: #f1f8ff;
        margin-top: 22rpx;
        // display: flex;
        // justify-content: space-between;
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        // grid-template-rows: repeat(2, 1fr);
        border-radius: 8rpx;
        .name {
          font-size: 26rpx;
          color: #787e8f;
          text-align: center;
        }
        .number {
          font-size: 28rpx;
          color: #2e68ff;
          margin-top: 20rpx;
          text-align: center;
          flex-shrink: 0;
        }
        .up {
          color: #44d7b6;
        }
        .down {
          color: #e02020;
        }
      }
    }

    .evaluate {
      position: relative;
      .title_text {
        color: #787e8f;
        font-size: 22rpx;
        margin-bottom: 20rpx;
      }
      .evaluate_text {
        font-size: 36rpx;
        font-weight: 400;
        color: #161f30;
        margin-bottom: 20rpx;
        display: inline-block;
        background-image: linear-gradient(to top, #2e68ff 35%, white 35%);
      }
      .session {
        color: #161f30;
        font-size: 26rpx;
        font-weight: 400;
      }
      .grade {
        width: 228rpx;
        height: 182rpx;
        position: absolute;
        top: -44rpx;
        right: 34rpx;
        background: url('https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/16967627423535267169676274235372918_%E7%BC%96%E7%BB%84%2014%E5%A4%87%E4%BB%BD%202%402x.png');
        background-size: 228rpx 182rpx;
        .grade_number {
          font-size: 38rpx;
          color: #2e68ff;
          font-weight: 500;
          text-align: center;
          margin-top: 52rpx;
        }
        .grade_text {
          font-size: 24rpx;
          color: #ffffff;
          text-align: center;
          margin-top: 4rpx;
        }
      }
    }
  }
  table {
    font-size: 26rpx;
    color: #161f30;
    margin-top: 24rpx;
    border-left: 1rpx solid #d7e5fe;
    border-right: 1rpx solid #d7e5fe;
    position: relative;
    tr,
    td {
      display: flex;
      justify-content: center;
      align-items: center;
    }
    tr {
      width: 100%;
      border-bottom: 1rpx solid #d7e5fe;
      // &:first-child {
      //   border: 0 none;
      // }
    }
    .line {
      width: 120rpx;
      border-top: 1rpx solid #d7e5fe;
      &::after {
        height: 100%;
        width: 1px;
        background-color: #d7e5fe;
        position: absolute;
        left: 86rpx;
        top: 0;
        content: '';
      }
    }
    .line2 {
      width: 120rpx;
      border-top: 1rpx solid #d7e5fe;
      &::after {
        height: 100%;
        width: 1px;
        background-color: #d7e5fe;
        position: absolute;
        left: 173rpx;
        top: 0;
        content: '';
      }
    }
    .line3 {
      width: 100%;
      border-top: 1rpx solid #d7e5fe;
      // &::after {
      //   height: 100%;
      //   width: 1px;
      //   background-color: #d7e5fe;
      //   position: absolute;
      //   right: 0;
      //   content: '';
      // }
    }

    th {
      background: #f1f8ff;
      // width: 100%;
      height: 70rpx;
      line-height: 70rpx;
      text-align: center;
      // border-right: 1rpx solid #d7e5fe;
      border-right: 0;
      // border-bottom: 0;
    }
    td {
      text-align: center;
      line-height: 70rpx;
      // border: 1rpx solid #d7e5fe;
      border-top: 0;
      border-right: 0;
      min-height: 70rpx;
      border-right: 1rpx solid #d7e5fe;
    }
    td:last-child,
    th:last-child {
      border-right: 0;
    }
    td:first-child,
    th:first-child {
      border-left: 0;
    }
  }
  .background {
    background: linear-gradient(
      180deg,
      #2e68ff 0%,
      #5197fe 43%,
      rgba(255, 255, 255, 0) 100%
    );
    width: 100%;
    height: 350rpx;
    position: absolute;
    top: -47rpx;
    left: 0;
  }
  .not_more {
    line-height: 80rpx;
    text-align: center;
    font-size: 24rpx;
    color: #ccc;
    width: 100%;
    margin-bottom: 50rpx;
  }
}
</style>
