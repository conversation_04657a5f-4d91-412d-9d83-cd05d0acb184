<template>
  <scroll-view scroll-y style="height: 100vh" class="form-box">
    <view class="form">
      <view class="avatar-item">
        <view class="text">
          <view>头像</view>
          <view class="layout-row tips">
            <!-- <image
              src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/1699068234654413c169906823465468543_info-circle.png"
            ></image> -->
            <view>上传头像</view>
          </view>
        </view>
        <image class="avatar" :src="getAvatar()" @click="updateJobDoc"></image>
      </view>
      <formItem label="姓名" label-position="top">
        <input
          class="k-input"
          v-model="form.nickname"
          maxlength="10"
          confirm-type="done"
          type="text"
          placeholder="请输入你的姓名"
          placeholder-class="job-job-input-placeholder"
        />
      </formItem>
    </view>
    <button class="save" @click="save">保存</button>
  </scroll-view>
</template>

<script>
import formItem from '../../components/commen/form-item.vue'
import { changeBasic } from '../../api'
import { upLoad } from '../../utils'
export default {
  components: { formItem },
  data() {
    return {
      form: {
        id: '',
        nickname: '',
        avatar: ''
      },
      rules: {
        nickname: {
          message: '姓名'
        },
        avatar: {
          message: '头像'
        }
      },
      toUrl: ''
    }
  },
  onLoad(query) {
    if (query.toUrl) {
      this.toUrl = decodeURIComponent(query.toUrl)
    }
    let info = uni.getStorageSync('__xingyun_userinfo__')
    this.form.nickname = info.nickname || info.student_name
    this.form.avatar =
      info.avatar ||
      '408559575579495187/2025/04/23/17453936827177152-1745393682722-52009.png'
    this.form.id = info.student_id
  },
  methods: {
    getAvatar() {
      return this.$xh.completepath(this.form.avatar)
    },
    save() {
      for (let key of Object.keys(this.rules)) {
        if (!this.form[key]) {
          this.$xh.Toast(this.rules[key].message + '不能为空')
          return
        }
      }
      changeBasic(this.form).then(res => {
        if (res.code == 100000) {
          let info = uni.getStorageSync('__xingyun_userinfo__')
          uni.setStorageSync('__xingyun_userinfo__', {
            ...info,
            ...this.form
          })
          if (this.toUrl) {
            this.$xh.redirect('jintiku', this.toUrl)
          } else {
            uni.navigateBack({
              delta: 1
            })
          }
        }
      })
    },
    updateJobDoc() {
      let that = this
      wx.chooseMedia({
        count: 1,
        mediaType: ['image'],
        sourceType: ['album', 'camera'],
        maxDuration: 30,
        camera: 'back',
        success: res => {
          upLoad(res.tempFiles[0].tempFilePath)
            .then(url => {
              this.form.avatar = url
            })
            .catch(res => {
              this.$xh.Toast('上传文件失败！')
            })
        },
        fail(err) {
          console.log(err)
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
// /deep/
.sex-item {
  border-bottom: 1px solid #d8dde1;
  margin-bottom: 32rpx;
  .sex-content {
    width: 100%;
    justify-content: flex-end;
  }
}
.avatar-item {
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid #d8dde1;
  font-size: 28rpx;
  color: rgba(3, 32, 61, 0.65);
  padding-top: 24rpx;
  padding-bottom: 32rpx;
  .tips {
    align-items: center;
    font-size: 24rpx;
    color: #2e68ff;
    image {
      width: 28rpx;
      height: 28rpx;
      margin-right: 4rpx;
    }
  }
  .text {
    height: 108rpx;
    // padding: 10px 0;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }
  .avatar {
    width: 116rpx;
    height: 116rpx;
    background-color: #f2f4f7;
    border-radius: 50%;
  }
}
.f-a-c {
  align-items: center;
}
.layout-row {
  display: flex;
}
.form-box {
  background-color: #fff;
  height: 100vh;
  width: 100vw;
  .form {
    height: calc(100vh - 140rpx);
    padding: 32rpx;
    overflow-y: auto;
  }
  .save {
    position: fixed;
    left: 40rpx;
    height: 88rpx;
    line-height: 88rpx;
    width: calc(100% - 80rpx);
    background: #2e68ff;
    border-radius: 16rpx;
    font-size: 28rpx;
    color: #ffffff;
    bottom: 40rpx;
    z-index: 1;
    font-weight: bold;
  }
}
</style>
