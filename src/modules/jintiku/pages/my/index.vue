<template>
	<view class="box">
		<view class="header-box">
			<view style="height: 76px"></view>
			<login v-if="!isLoging" @success="sin">
				<view class="top">
					<view class="top-photo">
						<img
							src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/16693568262487366166935682624823545_touxiang.png" />
						<view class="top-name">请登录</view>
					</view>
				</view>
			</login>
			<view v-else class="top">
				<!-- @click="goDetail('pages/userInfo/authentication')" -->
				<view class="top-photo" @click="goDetail('pages/my/person')">
					<img :src="
	  	        $xh.completepath(
	  	          userinfo.avatar ||
	  	            '408559575579495187/2025/04/23/17453936827177152-1745393682722-52009.png'
	  	        )
	  	      " />

					<view class="user-info">
						<view class="name">{{
	  	        userinfo.nickname || userinfo.student_name
	  	      }}</view>
						<view class="phone">
							{{ phone.substring(0, 3) + '****' + phone.substring(7) }}
						</view>
					</view>

					<view class="edit">
						<view></view>
						<img class="imgTwo"
							src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/34e7174539261545097316_%E7%BC%96%E7%BB%84%204%E5%A4%87%E4%BB%BD%203%402x.png" />
					</view>
				</view>
				<view class="top-code">
					<text>
						<!-- {{ isAuth ? '已认证' : '未认证' }} -->
					</text>
					<!-- <image
	  	      src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/1669357686810b313166935768681084385_right.png"
	  	      mode="widthFix"
	  	    /> -->
				</view>
			</view>
		</view>

		<view class="tab">
			<login v-for="item of tabs" @success="goDetail(item.url)">
				<view class="tab-item">
					<image class="icon" :src="item.icon"></image>
					<view class="text">{{ item.text }}</view>
				</view>
			</login>
		</view>
		<login @success="goDetail('pages/test/order')">
			<view class="list" style="margin-top:32rpx">
				<img class="imgOne" src="https://xy-shunshun-pro.oss-cn-hangzhou.aliyuncs.com/file.png" />
				<view>我的订单</view>
				<img class="imgTwo"
					src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/1669357686810b313166935768681084385_right.png" />
			</view>
		</login>

		<login @success="goDetail('pages/index/brushing')">
			<view class="list">
				<img class="imgOne" src="https://yakaixin.oss-cn-beijing.aliyuncs.com/pen.png" />
				<view>我的练习</view>
				<img class="imgTwo"
					src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/1669357686810b313166935768681084385_right.png" />
			</view>
		</login>
		<login @success="goDetail('pages/userInfo/set')">
			<view class="list">
				<img class="imgOne" src="https://yakaixin.oss-cn-beijing.aliyuncs.com/meme.png" />
				<view>设置</view>
				<img class="imgTwo"
					src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/1669357686810b313166935768681084385_right.png" />
			</view>
		</login>
		<login v-if="orgList.length && orgList.length > 1 && false" :key="orgList.length">
			<picker :range="orgList" :value="merchantIndex" @change="orgDateChange" @click="getMerchangeList">
				<view class="list">
					<img class="imgOne"
						src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/16986579167243467169865791672483195_time.png" />
					<view>切换机构</view>
					<view class="end">
						<text>{{ orgList[merchantIndex] }}</text>
						<img class="imgTwo"
							src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/1669357686810b313166935768681084385_right.png" />
					</view>
				</view>
			</picker>
		</login>
		<!-- 退出登录 -->
		<view class="loginOut" v-if="isLoging && false">
			<!-- <u-button @click="loginOut">退出登录</u-button> -->
		</view>
	</view>
</template>
<script>
	import login from '../../components/commen/login'
	import {
		getExamTime,
		setTimeInfo
	} from '../../api/userInfo'
	import {
		getToken
	} from '../../api'
	import {
		getMerchantbrands
	} from '../../api/commen'
	import {
		app_id
	} from '../../config'
	export default {
		components: {
			login
		},
		data() {
			return {
				isLoging: false,
				userinfo: {},
				majorInfo: {},
				phone: '',
				time: '',
				img: '',
				timer: '',
				date: '',
				orgList: [],
				merchantIndex: 0,
				isAuth: false,
				newMerchant: [],
				tabs: [{
						icon: 'https://yakaixin.oss-cn-beijing.aliyuncs.com/order.png',
						url: 'pages/study/index',
						text: '我的课程' //之前是我的订单
					},
					{
						icon: 'https://yakaixin.oss-cn-beijing.aliyuncs.com/flower.png',
						url: 'pages/userInfo/report',
						text: '我的报告' //之前是我的测验
					},

					{
						icon: 'https://yakaixin.oss-cn-beijing.aliyuncs.com/error.png',
						url: 'pages/wrongQuestionBook/index',
						text: '纠错' //之前没有这个
					},
					{
						icon: 'https://yakaixin.oss-cn-beijing.aliyuncs.com/mail.png',
						url: 'pages/collect/index',
						text: '试题收藏' //之前是我的练习
					}
				]
			}
		},
		computed: {
			startDate() {
				return this.getDate('start')
			},
			endDate() {
				return this.getDate('end')
			}
		},
		onShow() {
			this.majorInfo = uni.getStorageSync('__xingyun_major__')
			this.userinfo = uni.getStorageSync('__xingyun_userinfo__')
			this.getInfo()
		},
		onLoad(e) {
			let token = uni.getStorageSync('__xingyun_token__')
			this.startFun(token)
			// this. uni.getStorageSync('__org_data__')
			// let newDate=new Date(2023,6,6,12,30,10);
			// this.time=this.formatDuring(newDate-new Date())
			// setInterval(()=>{
			// _self.time=_self.formatDuring(newDate-new Date())
			// this.getMerchangeList()
			// },5000)
		},
		onTabItemTap: function(item) {
			let token = uni.getStorageSync('__xingyun_token__')
			this.startFun(token)
		},
		methods: {
			bindDateChange: function(e) {
				this.date = e.detail.value
				setTimeInfo({
					exam_date: this.date
				}).then(data => {
					this.$xh.Toast('设置成功！')
				})
			},
			orgDateChange: function(e) {
				let userInfo = uni.getStorageSync('__xingyun_userinfo__')
				let merchant = this.newMerchant.length ?
					this.newMerchant :
					userInfo.merchant
				let tmpData = JSON.parse(JSON.stringify(merchant[e.detail.value]))
				merchant.splice(e.detail.value, 1)
				merchant.unshift(tmpData)

				userInfo.merchant = merchant
				this.$xh.Toast('设置成功！')
				this.merchantIndex = e.detail.value
				// 更新token
				getToken({
					merchant_id: tmpData.merchant_id,
					brand_id: tmpData.brand_id
				}).then(result => {
					// merchant
					uni.setStorageSync('__xingyun_merchant_index__', e.detail.value)
					this.$store.commit('jintiku/setToken', result.data.token)
					userInfo.student_id = result.data.student_id
					userInfo.student_name = result.data.nickname || result.data.student_name
					uni.setStorageSync('__xingyun_userinfo__', userInfo)
					uni.$emit('merch_clear')
					/**
					 * 删除专业并重新去选择专业
					 */
					uni.setStorageSync('__xingyun_major__', {})
					uni.redirectTo({
						url: '/modules/jintiku/pages/major/index'
					})
				})
			},
			getDate(type) {
				const date = new Date()
				let year = date.getFullYear()
				let month = date.getMonth() + 1
				let day = date.getDate()
				if (type === 'start') {
					year = year - 60
				} else if (type === 'end') {
					year = year + 2
				}
				month = month > 9 ? month : '0' + month
				day = day > 9 ? day : '0' + day
				return `${year}-${month}-${day}`
			},
			loginOut() {
				let that = this
				uni.showModal({
					title: '温馨提示',
					content: '是否确认要退出？',
					confirmText: '退出',
					success() {
						// that.$store.state.jintiku.token = ''
						try {
							let userInfo = uni.getStorageSync('__xingyun_userinfo__')
							let merchant = userInfo.merchant[0]
							uni.setStorageSync('__temp__merchant__', merchant)
						} catch (error) {}
						uni.removeStorageSync('__xingyun_token__')
						uni.removeStorageSync('__xingyun_userinfo__')
						that.isLoging = false
						that.$xh.Toast('退出成功')
						that.$store.commit('jintiku/removeToken')
						setTimeout(() => {
							uni.switchTab({
								url: '/modules/jintiku/pages/index/index'
							})
						}, 1000)
					}
				})
			},
			setLogin() {
				this.isLoging = true
				this.userinfo = uni.getStorageSync('__xingyun_userinfo__')
				this.phone = uni.getStorageSync('__xingyun_userPhone__')
			},
			startFun(token) {
				if (token) {
					this.setLogin()
				}
			},
			sin() {
				this.setLogin()
			},
			goDetail(url) {
				if (!url) {
					this.$xh.Toast('敬请期待！')
					return
				}
				this.$xh.push('jintiku', url)
			},
			goPersonInfo() {
				// this.getCode()
				// this.show = true
				// this.$xh.push('')
			},
			//传入时间戳，返回分秒
			formatDuring(mss) {
				let minutes = parseInt((mss % (1000 * 60 * 60)) / (1000 * 60))
				let seconds = Math.trunc((mss % (1000 * 60)) / 1000)
				return minutes + '分钟 ' + seconds + '秒 '
			},
			getMerchangeList() {
				// 新增接口请求
				getMerchantbrands({
					app_id: app_id,
					noloading: true
				}).then(res => {
					this.merchantIndex = uni.getStorageSync('__xingyun_merchant_index__')
					this.newMerchant = res.data.mbs
					if (this.newMerchant.length) {
						this.orgList = this.newMerchant.map(res => {
							return res.merchant_name
						})
					}
					//回显当前机构
					let userInfo = uni.getStorageSync('__xingyun_userinfo__')
					let merchant = userInfo.merchant[0]
					if (this.orgList.length) {
						this.merchantIndex = this.orgList.findIndex(
							s => s == merchant.merchant_name
						)
					}
				})
			},
			getInfo() {
				getExamTime({}).then(data => {
					this.date = data.data.exam_date
					console.log(data)
					if (data.data.face_url) {
						this.isAuth = true
					}
				})
				this.orgList = uni
					.getStorageSync('__xingyun_userinfo__')
					.merchant.map(res => {
						return res.merchant_name
					})
				this.getMerchangeList()
			}
		},
		onShareAppMessage() {
			return this.$xh.shareAppMessage()
		}
	}
</script>

<style scoped lang="less">
	.tab {
		display: flex;
		flex-direction: row;
		justify-content: space-around;
		align-items: center;
		padding: 32rpx 0rpx;
		margin-top: 32rpx;
		margin-left: 32rpx;
		margin-right: 32rpx;
		background-color: #FFFFFF;
		border-radius: 32rpx;


		.tab-item {
			display: flex;
			flex-direction: column;
			align-items: center;

			.icon {
				width: 25px;
				height: 33px;
			}

			.text {
				font-weight: 400;
				font-size: 14px;
				color: #1b2637;
				margin-top: 10rpx;
			}
		}
	}

	.header-box {
		width: 100%;
		background: url(https://yakaixin.oss-cn-beijing.aliyuncs.com/my-background-img.png);
	}

	.box {
		width: 100%;
		min-height: 100vh;
		background-color: #F5F6F8;
		padding: 20rpx 0rpx;

		.top {
			padding-bottom: 30rpx;
			display: flex;
			align-items: center;
			justify-content: space-between;

			.top-photo {
				display: flex;
				align-items: center;
				width: 100%;

				img {
					width: 128rpx;
					height: 128rpx;
					border: 8rpx solid #ffffff;
					border-radius: 100%;
					margin-left: 32rpx;
				}

				.top-name {
					font-size: 40rpx;
					font-family: PingFangSC-Medium, PingFang SC;
					font-weight: 500;
					color: #03203d;
					margin-left: 16rpx;
				}

				.edit {
					margin-left: auto;

					image {
						border-radius: 0;
						border: none;
						width: 20px;
						height: 20px;
					}
				}

				.user-info {
					display: flex;
					flex-direction: column;
					justify-content: space-between;
					margin-left: 24rpx;

					.name {
						font-weight: 500;
						font-size: 18px;
						color: #03203d;
						line-height: 24px;
						margin-bottom: 8px;
						margin-top: 8px;
					}

					.phone {
						font-weight: 500;
						font-size: 14px;
						color: rgba(3, 32, 61, 0.85);
						line-height: 20px;
					}
				}
			}

			.top-code {
				display: flex;
				align-items: center;
				justify-content: space-between;

				.top-label {
					color: #03c497;
					margin-right: 16rpx;
				}

				text {
					font-size: 22rpx;
					font-family: PingFangSC-Regular, PingFang SC;
					font-weight: 400;
					color: #787e8f;
					line-height: 32rpx;
				}

				image {
					width: 26rpx;
				}
			}
		}

		.list {
			display: flex;
			align-items: center;
			width: calc(100%-64rpx);
			padding: 40rpx 32rpx;
			background: #ffffff;
			border-radius: 32rpx;
			font-size: 32rpx;
			font-family: PingFangSC-Regular, PingFang SC;
			font-weight: 400;
			color: #03203d;
			margin: 0rpx 32rpx 24rpx 32rpx;

			.imgOne {
				width: 48rpx;
				height: 48rpx;
				margin-right: 20rpx;
			}

			view {
				flex: 1;
				margin-left: 8rpx;
			}

			.imgTwo {
				width: 40rpx;
				height: 40rpx;
			}

			.end {
				display: flex;
				align-items: center;
				justify-content: flex-end;

				text {
					color: #333;
					font-size: 28rpx;
				}
			}
		}

		.noticeBox {
			width: 100%;
			height: 100vh;
			position: fixed;
			top: 0;
			left: 0;

			.shadowBox {
				width: 100%;
				height: 100%;
				position: fixed;
				top: 0;
				left: 0;
				background-color: #000000;
				opacity: 0.3;
				z-index: 1;
			}

			.content {
				display: flex;
				align-items: center;
				justify-content: center;
				margin-top: calc((100vh - 948rpx) / 2);

				.bullet {
					background-image: url(https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/16887211188647d9b168872111886446994_codebg.png);
					background-repeat: no-repeat;
					background-size: 100% 100%;
					box-sizing: border-box;
					z-index: 2;
					padding: 44rpx 8rpx 44rpx 8rpx;
					width: 654rpx;
					height: 948rpx;
					border-radius: 32rpx;

					//display: flex;
					//flex-direction: column;
					//justify-content: center;
					//align-items: flex-start;
					.bulletTitle {
						text-align: center;
						font-size: 28rpx;
						font-weight: 400;
						color: rgba(3, 32, 61, 0.65);
						margin-bottom: -36rpx;
						letter-spacing: 0.5;
					}

					.userMesage {
						padding-left: 56rpx;
						display: flex;
						justify-content: flex-start;
						align-items: center;
						margin-top: 90rpx;

						.message {

							//display: flex;
							//flex-direction: column;
							//align-items: center;
							//justify-content: flex-start;
							.name {
								color: rgba(3, 32, 61, 1);
								font-weight: 500;
								font-size: 32rpx;
								line-height: 36rpx;
								text-align: left;
							}

							.phone {
								color: rgba(3, 32, 61, 0.85);
								font-size: 28rpx;
								line-height: 36rpx;
								margin-top: 16rpx;
							}
						}

						img {
							width: 112rpx;
							height: 112rpx;
							border-radius: 100%;
							margin-right: 16rpx;
						}
					}

					.bulletImg {
						position: relative;
						z-index: -1;

						img {
							width: 638rpx;
							height: 638rpx;
						}

						margin-bottom: -32rpx;
					}

					.bulletRefresh {
						display: flex;
						font-size: 28rpx;
						color: rgba(37, 124, 250, 1);
						justify-content: center;
						align-items: center;

						img {
							margin-right: 4rpx;
							width: 36rpx;
							height: 36rpx;
						}
					}
				}
			}
		}
	}

	.loginOut {
		position: fixed;
		padding: 0 40rpx;
		left: 0;
		right: 0;
		bottom: 40rpx;
	}
</style>