// import model from '../components/commen/uni-model.vue'
export default {
  components: {
    // model
  },
  data() {
    return {
      showModel: false,
      title: '',
      desc: '',
      sure: '',
      cancel: ''
    }
  },
  methods: {
    $showModel(title, desc, sure, cancel) {
      this.showModel = true
      Object.assign(this, {
        title,
        desc,
        sure,
        cancel
      })
    },
    $hideModel() {
      this.showModel = false
    }
  }
}
