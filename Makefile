.PHONY: start clean


RELEASE_VERSION    = v1.0.0
PKG_NAME 		   = $(name)
NOW_TIME		   = $(shell date '+%Y%m%d%H%M')
PKG_USER		   = $(shell git config user.name)
GIT_HASH           = $(shell git rev-parse --short HEAD)
GIT_COUNT 		   = $(shell git rev-list --all --count)
GIT_CURRENT_BRANCH = $(shell git symbolic-ref --short HEAD)
BASE_SECURITY_CODE = JvG5grlELsBoRa6fqFHoq9h4CMnWOaMRh5Xou2RrliPtQRsgMVjzFLRgCFvJzIAw
RELEASE_TAG        = $(GIT_CURRENT_BRANCH)_$(RELEASE_VERSION).$(GIT_COUNT)_$(GIT_HASH)_$(PKG_USER)
PKG_FULL_NAME      = $(PKG_NAME)_$(RELEASE_TAG)_$(NOW_TIME)
SECURITY_CODE      = $(BASE_SECURITY_CODE)$(PKG_FULL_NAME)


dev: clean branchdev init builddev
pre: clean branchpre init buildpre
test: clean branchtest init buildtest
prod: clean branchprod init buildprod


clean:
	@rm -rf ./dist


branchdev:
	@git checkout dev
branchpre:
	@git checkout pre
branchtest:
	@git checkout test
branchprod:
	@git checkout master


init:
	@git pull
	@mkdir -p ./tmp/pkg
	@cd ./tmp/pkg && rm -rf ./*.tar.bz2 && rm -rf ./*.zip


builddev:
	@npm run builddev:h5
	@cd ./dist/dev/h5 && tar jcf ../../../tmp/pkg/$(PKG_FULL_NAME).tar.bz2 index.html static
	@cd ./tmp/pkg && zip -P $(SECURITY_CODE) $(PKG_FULL_NAME).zip $(PKG_FULL_NAME).tar.bz2 && rm -rf ./*.tar.bz2
buildpre:
	@npm run build:h5-pre
	@cd ./dist/dev/h5 && tar jcf ../../../tmp/pkg/$(PKG_FULL_NAME).tar.bz2 index.html static
	@cd ./tmp/pkg && zip -P $(SECURITY_CODE) $(PKG_FULL_NAME).zip $(PKG_FULL_NAME).tar.bz2 && rm -rf ./*.tar.bz2
buildprod:
	@npm run build
	@cd ./dist/build/h5 && tar jcf ../../../tmp/pkg/$(PKG_FULL_NAME).tar.bz2 index.html static
	@cd ./tmp/pkg && zip -P $(SECURITY_CODE) $(PKG_FULL_NAME).zip $(PKG_FULL_NAME).tar.bz2 && rm -rf ./*.tar.bz2
buildtest:
	@npm run build:h5-test
	@cd ./dist/dev/h5 && tar jcf ../../../tmp/pkg/$(PKG_FULL_NAME).tar.bz2 index.html static
	@cd ./tmp/pkg && zip -P $(SECURITY_CODE) $(PKG_FULL_NAME).zip $(PKG_FULL_NAME).tar.bz2 && rm -rf ./*.tar.bz2