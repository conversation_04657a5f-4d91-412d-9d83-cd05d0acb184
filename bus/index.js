// import Vue from 'vue'
// export default new Vue()
// const Vue = require('vue')
const stack = {}
module.exports = {
  ['$on'](name, func) {
    if (!stack[name]) {
      stack[name] = []
    }
    if (stack[name].includes(func)) {
      return
    }
    stack[name].push(func)
  },
  ['$emit'](name, ...arg) {
    if (stack[name] && stack[name].length) {
      stack[name].forEach(fn => {
        fn && fn(...arg)
      })
    }
  },
  ['$get']() {
    console.log(stack)
    return stack
  }
}
