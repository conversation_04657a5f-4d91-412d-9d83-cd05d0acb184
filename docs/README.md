# 金题库 UniApp 开发文档

## 项目概述

金题库是一个基于 UniApp 框架开发的跨平台在线学习应用，主要提供题库练习、考试测评、章节学习等功能。项目支持微信小程序、H5、App 等多个平台。

### 基本信息

- **项目名称**: 金题库 (uniapp_lbr)
- **版本**: 1.4.14
- **框架**: UniApp + Vue 2.6.11
- **UI 库**: uView UI 2.0.31 + Vant 2.12.54
- **状态管理**: Vuex 3.2.0
- **构建工具**: Vue CLI 4.5.0

### 技术栈

#### 前端框架
- **Vue.js 2.6.11**: 核心框架
- **UniApp**: 跨平台开发框架
- **Vuex**: 状态管理
- **Vue Router**: 路由管理（UniApp 内置）

#### UI 组件库
- **uView UI 2.0.31**: 主要 UI 组件库
- **Vant 2.12.54**: 补充 UI 组件
- **@dcloudio/uni-ui**: UniApp 官方组件

#### 工具库
- **flyio**: HTTP 请求库
- **md5**: 加密工具
- **echarts**: 图表库
- **qrcode**: 二维码生成
- **pdfh5**: PDF 预览
- **weixin-js-sdk**: 微信 JS SDK

#### 开发工具
- **Sass**: CSS 预处理器
- **ESLint**: 代码检查
- **Prettier**: 代码格式化
- **Babel**: JavaScript 编译
- **Jest**: 单元测试

### 项目结构

```
wb_prdt/
├── bin/                    # 构建脚本和项目配置
│   ├── create-template     # 模板创建工具
│   ├── index.js           # 主入口脚本
│   └── project/           # 项目配置文件
├── bus/                   # 事件总线
├── public/                # 静态资源
├── src/                   # 源代码目录
│   ├── api/              # 全局 API 配置
│   ├── components/       # 全局组件
│   ├── modules/          # 业务模块
│   │   └── jintiku/      # 金题库模块
│   │       ├── api/      # 模块 API
│   │       ├── components/ # 模块组件
│   │       ├── pages/    # 页面文件
│   │       ├── store/    # 状态管理
│   │       ├── utils/    # 工具函数
│   │       └── mixin/    # 混入
│   ├── static/           # 静态资源
│   ├── store/            # 全局状态管理
│   ├── utlis/            # 全局工具函数
│   ├── App.vue           # 应用入口组件
│   ├── main.js           # 应用入口文件
│   ├── pages.json        # 页面配置
│   └── manifest.json     # 应用配置
├── docs/                 # 文档目录（本文档）
├── package.json          # 项目依赖配置
├── vue.config.js         # Vue CLI 配置
├── babel.config.js       # Babel 配置
├── tsconfig.json         # TypeScript 配置
└── project.config.json   # 微信小程序配置
```

### 支持平台

- **微信小程序**: 主要平台
- **H5**: Web 端支持
- **App**: iOS/Android 应用
- **支付宝小程序**: 跨平台支持
- **百度小程序**: 跨平台支持
- **头条小程序**: 跨平台支持
- **QQ 小程序**: 跨平台支持

### 环境配置

项目支持多环境配置：

- **development**: 开发环境
- **test**: 测试环境  
- **pre**: 预发布环境
- **production**: 生产环境

### 核心功能模块

1. **题库练习**: 章节练习、真题闯关、智能测评
2. **考试系统**: 模考大赛、在线考试、成绩报告
3. **学习管理**: 错题本、试题收藏、学习进度
4. **用户系统**: 登录注册、个人中心、专业选择
5. **支付系统**: 商品购买、订单管理、支付集成

### 开发规范

- 使用 ES6+ 语法
- 遵循 Vue.js 官方风格指南
- 组件命名采用 PascalCase
- 文件命名采用 kebab-case
- 使用 ESLint + Prettier 进行代码规范检查

### 构建部署

```bash
# 开发环境
npm run dev:h5          # H5 开发
npm run dev:mp-weixin   # 微信小程序开发

# 生产构建
npm run build:h5        # H5 构建
npm run build:mp-weixin # 微信小程序构建
```

## 快速开始

### 环境要求

- Node.js >= 12.0.0
- npm >= 6.0.0 或 yarn >= 1.0.0
- HBuilderX（推荐）或 VS Code

### 安装依赖

```bash
npm install
# 或
yarn install
```

### 开发运行

```bash
# H5 开发
npm run dev:h5

# 微信小程序开发
npm run dev:mp-weixin
```

### 项目配置

1. 复制环境配置文件
2. 配置 API 接口地址
3. 配置微信小程序 AppID
4. 配置支付相关参数

详细配置说明请参考各模块文档。
