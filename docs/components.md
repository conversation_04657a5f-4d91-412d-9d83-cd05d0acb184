# 组件使用文档

## 组件概览

金题库项目包含全局通用组件和业务专用组件两大类，采用模块化设计，便于复用和维护。

## 全局通用组件 (`src/components`)

### 1. 音频组件 (`Audio/index.vue`)
音频播放控制组件。

```vue
<template>
  <Audio :src="audioUrl" :autoplay="false" />
</template>
```

**Props:**
- `src`: 音频文件地址
- `autoplay`: 是否自动播放

### 2. 基础组件 (`BaseComComponents`)

#### 导航栏 (`navigation-bar/navigation-bar.vue`)
自定义状态栏组件。

```vue
<template>
  <navigation-bar 
    :color="#fff" 
    :position="fixed"
    @changetatusBarHeight="handleHeightChange"
  />
</template>
```

**Props:**
- `color`: 背景颜色，默认 `#fff`
- `position`: 定位方式，默认 `fixed`

**Events:**
- `changetatusBarHeight`: 状态栏高度变化事件

#### 页脚组件 (`xh-footer`)
通用页脚组件。

### 3. 工具组件 (`ToolCom`)

#### 圆形进度条 (`ar-circle-progress.vue`)
```vue
<template>
  <ar-circle-progress 
    :percent="75" 
    :size="100"
    :strokeWidth="8"
    :color="#2E68FF"
  />
</template>
```

**Props:**
- `percent`: 进度百分比
- `size`: 组件大小
- `strokeWidth`: 线条宽度
- `color`: 进度条颜色

#### 日期选择器 (`dy-Date.vue`)
日期时间选择组件。

#### 录音组件 (`sound-recording.vue`)
音频录制功能组件。

#### 日历组件 (`uni-zwy-calendar.vue`)
日历选择器组件。

### 4. 图表组件

#### 数据图表 (`qiun-data-charts/qiun-data-charts.vue`)
基于 ECharts 的图表组件。

```vue
<template>
  <qiun-data-charts 
    :chartData="chartData"
    :opts="opts"
    :loadingType="1"
  />
</template>
```

#### U-Charts (`u-charts`)
轻量级图表组件。

### 5. 加载组件 (`qiun-loading`)
多种样式的加载动画组件。

```vue
<template>
  <qiun-loading :loading-type="1" />
</template>
```

### 6. 图标组件 (`uni-icons`)
统一图标组件。

```vue
<template>
  <uni-icons type="home" size="24" color="#333" />
</template>
```

### 7. 加载更多 (`uni-load-more`)
列表加载更多组件。

### 8. 导航组件

#### 返回按钮 (`xh-back/xh-back.vue`)
通用返回按钮。

#### 自定义导航栏 (`xh-nav-bar`)
完整的自定义导航栏组件。

```vue
<template>
  <xh-nav-bar 
    :title="页面标题"
    :left-text="返回"
    :right-text="更多"
    @clickLeft="handleBack"
    @clickRight="handleMore"
  />
</template>
```

## 业务组件 (`src/modules/jintiku/components`)

### 1. 通用业务组件 (`commen`)

#### 返回按钮 (`backbtn.vue`)
业务定制的返回按钮。

#### 倒计时 (`countdown.vue`)
考试倒计时组件。

```vue
<template>
  <countdown 
    :time="3600" 
    @finish="handleFinish"
    @update="handleUpdate"
  />
</template>
```

**Props:**
- `time`: 倒计时秒数

**Events:**
- `finish`: 倒计时结束
- `update`: 倒计时更新

#### 表单项 (`form-item.vue`)
统一的表单项组件。

```vue
<template>
  <form-item 
    label="用户名"
    :labelPosition="left"
    :isPushIcon="true"
  >
    <input placeholder="请输入用户名" />
  </form-item>
</template>
```

**Props:**
- `label`: 标签文本
- `labelPosition`: 标签位置 (`left`/`top`)
- `isPushIcon`: 是否显示箭头图标
- `valuePos`: 值的位置 (`left`/`right`)

#### 头部高度 (`head-height.vue`)
适配不同设备的头部高度组件。

#### 首页导航 (`index-nav.vue`)
首页功能导航组件。

```vue
<template>
  <index-nav ref="indexNav" />
</template>
```

**Methods:**
- `init()`: 初始化导航数据

#### 二维码 (`kf-qrcode.vue`)
二维码生成和显示组件。

#### 登录组件
- `login.vue`: 小程序登录
- `login-h5.vue`: H5 登录

#### 音频播放 (`my-audio.vue`)
自定义音频播放器。

#### 导航栏 (`navbar.vue`)
业务定制导航栏。

```vue
<template>
  <navbar :title="页面标题" />
</template>
```

#### 无数据 (`no-data.vue`)
空状态展示组件。

#### 订单列表 (`order-list.vue`)
订单列表项组件。

#### 选择器外壳 (`picker-shell.vue`)
弹窗选择器容器。

```vue
<template>
  <picker-shell 
    :value="show" 
    :title="选择器标题"
    @input="handleInput"
  >
    <!-- 选择器内容 -->
  </picker-shell>
</template>
```

#### 模态框 (`uni-model.vue`)
自定义模态框组件。

### 2. 做题相关组件 (`makeQuestion`)

#### 答案详情 (`answer-particulars.vue`)
答案解析详情展示。

#### 答题卡 (`answer-sheet.vue`)
考试答题卡组件。

```vue
<template>
  <answer-sheet 
    v-model="sheetShow"
    :questions="questionList"
    @change="handleQuestionChange"
  />
</template>
```

**Props:**
- `value`: 显示状态
- `questions`: 题目列表

**Events:**
- `change`: 题目切换事件

#### 错题答题卡 (`error-answer-sheet.vue`)
错题专用答题卡。

#### 底部工具栏 (`bottom-utils.vue`)
做题页面底部工具栏。

```vue
<template>
  <bottom-utils 
    :current="currentIndex"
    :lists="questionList"
    :utils="['lookResolution', 'answerSheet', 'collect']"
    :isnextChapter="false"
    @nextChapter="handleNextChapter"
  />
</template>
```

**Props:**
- `current`: 当前题目索引
- `lists`: 题目列表
- `utils`: 显示的工具按钮
- `isnextChapter`: 是否显示下一章按钮

#### 倒计时组件
- `count-down.vue`: 基础倒计时
- `count-down2.vue`: 增强倒计时

#### 题目轮播组件
- `chapter-exercise-question-swiper.vue`: 章节练习题目轮播
- `examination-question-swiper.vue`: 考试题目轮播
- `practise-question-swiper.vue`: 练习题目轮播

#### 考试相关组件
- `examination-list.vue`: 考试列表
- `examination-test-item.vue`: 考试项目
- `examination-test-list.vue`: 考试测试列表

#### 纠错组件 (`error-correction.vue`)
题目纠错功能。

#### 题目回答 (`question-answer.vue`)
题目答题区域组件。

#### 题目分享 (`question-share.vue`)
题目分享功能。

#### 题型组件 (`questionType`)
不同题型的渲染组件。

#### 媒体渲染 (`render-img-and-video.vue`)
图片和视频渲染组件。

#### 选择题目 (`select-question.vue`)
题目选择组件。

#### 默写组件 (`write-from-memory.vue`)
默写功能组件。

#### 提示对话框 (`tipDialog`)
各种提示对话框组件。

### 3. 收藏相关组件 (`collect`)

#### 筛选选择 (`filtrate-select.vue`)
收藏筛选组件。

#### 题型选择 (`select-question-type.vue`)
题型筛选组件。

#### 时间范围选择 (`select-time-range.vue`)
时间范围筛选组件。

### 4. 其他组件 (`other`)

#### 屏幕适配 (`screen.vue`)
屏幕尺寸适配组件。

#### 搜索类型 (`search-type.vue`)
搜索类型选择。

#### 城市选择 (`select-city.vue`)
城市选择器。

### 5. 专业选择 (`select-major.vue`)
专业选择组件。

```vue
<template>
  <select-major 
    v-model="majorId"
    :show.sync="showMajor"
    :major_name.sync="majorName"
    @input="handleMajorChange"
  />
</template>
```

### 6. 树形章节 (`treeChapter`)

#### 树形组件 (`tree-item.vue`)
章节树形结构组件。

#### 子组件 (`child-com.vue`)
树形子节点组件。

#### 树形头部 (`tree-head.vue`)
树形结构头部。

### 7. 轮播组件 (`ls-swiper`)
自定义轮播图组件。

### 8. 滑动操作 (`uni-swipe-action`)
列表项滑动操作组件。

## 组件使用规范

### 1. 引入方式
```javascript
// 局部引入
import ComponentName from '@/components/ComponentName.vue'

// 全局引入（已配置 easycom）
// 直接在模板中使用，无需引入
```

### 2. 命名规范
- 组件文件名：kebab-case
- 组件名称：PascalCase
- Props：camelCase
- Events：kebab-case

### 3. 文档规范
每个组件应包含：
- 功能描述
- Props 说明
- Events 说明
- Slots 说明
- 使用示例

### 4. 开发规范
- 组件应具有良好的封装性
- 支持主题定制
- 提供合理的默认值
- 支持多平台兼容
