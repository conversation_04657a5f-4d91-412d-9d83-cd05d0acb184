# 页面结构文档

## 页面概览

金题库应用包含主包页面和多个分包页面，采用 TabBar 导航结构，主要分为题库、测验、刷题、我的四个主要模块。

## TabBar 导航

### 底部导航栏配置

```json
{
  "color": "#5B6E81",
  "selectedColor": "#2E68FF",
  "backgroundColor": "#fff",
  "list": [
    {
      "pagePath": "modules/jintiku/pages/index/index",
      "text": "题库",
      "iconPath": "static/imgs/jintiku/home.png",
      "selectedIconPath": "static/imgs/jintiku/home-active.png"
    },
    {
      "pagePath": "modules/jintiku/pages/index/test",
      "text": "测验",
      "iconPath": "static/imgs/jintiku/examination.png",
      "selectedIconPath": "static/imgs/jintiku/examination-active.png"
    },
    {
      "pagePath": "modules/jintiku/pages/index/brushing",
      "text": "刷题",
      "iconPath": "static/imgs/jintiku/brushing.png",
      "selectedIconPath": "static/imgs/jintiku/brushing-active.png"
    },
    {
      "pagePath": "modules/jintiku/pages/my/index",
      "text": "我的",
      "iconPath": "static/imgs/jintiku/my.png",
      "selectedIconPath": "static/imgs/jintiku/my-active.png"
    }
  ]
}
```

## 主包页面

### 1. 题库首页 (`modules/jintiku/pages/index/index`)
- **功能**: 应用主页，展示免费刷题入口和推荐内容
- **特性**: 
  - 自定义导航栏
  - 支持下拉刷新
  - 专业选择功能
  - 免费刷题导航（真题闯关、智能测评、考点词条、模考大赛）
  - 温故知新模块（错题本、试题收藏）
  - 推荐商品展示

### 2. 测验页面 (`modules/jintiku/pages/index/test`)
- **功能**: 测验模块入口，展示测验相关功能
- **特性**:
  - 自定义导航栏
  - 支持下拉刷新
  - 专业选择
  - 测验商品列表

### 3. 刷题页面 (`modules/jintiku/pages/index/brushing`)
- **功能**: 刷题模块入口，展示刷题相关内容
- **特性**:
  - 自定义导航栏
  - 支持下拉刷新
  - 刷题商品列表

### 4. 个人中心 (`modules/jintiku/pages/my/index`)
- **功能**: 用户个人信息和功能入口
- **特性**:
  - 自定义导航栏
  - 用户信息展示
  - 功能菜单入口

### 5. 登录中心
- **小程序版本** (`modules/jintiku/pages/loginCenter/index`)
- **H5版本** (`modules/jintiku/pages/loginCenter/h5`)
- **功能**: 用户登录注册

### 6. 专业选择 (`modules/jintiku/pages/major/index`)
- **功能**: 选择学习专业
- **特性**: 自定义导航栏

### 7. 考试相关页面
- **考试页面** (`modules/jintiku/pages/examination/index`)
- **考试须知** (`modules/jintiku/pages/examination/notice`)
- **考试中** (`modules/jintiku/pages/examination/examinationing`)
- **交卷成功** (`modules/jintiku/pages/examination/submitSuccess`)
- **测试页面** (`modules/jintiku/pages/examination/test`)

### 8. 做题相关页面
- **做题页面** (`modules/jintiku/pages/makeQuestion/makeQuestion`)
- **查看解析** (`modules/jintiku/pages/makeQuestion/lookAnalysisQuestion`)

### 9. 错题本
- **错题本列表** (`modules/jintiku/pages/wrongQuestionBook/index`)
- **错题详情** (`modules/jintiku/pages/wrongQuestionBook/wrongQuestionDetails`)

### 10. 章节练习
- **章节练习首页** (`modules/jintiku/pages/chapterExercise/index`)
- **章节练习详情** (`modules/jintiku/pages/chapterExercise/detail`)

### 11. 试题收藏
- **收藏列表** (`modules/jintiku/pages/collect/index`)
- **收藏详情** (`modules/jintiku/pages/collect/detail`)

### 12. 其他功能页面
- **个人资料修改** (`modules/jintiku/pages/my/person`)
- **URL视图** (`modules/jintiku/pages/urlView/index`)
- **成绩报告** (`modules/jintiku/pages/statistics/scoreReporting`)

### 13. H5活动页面
- **兑换码领取** (`modules/jintiku/pages/h5Active/code-receive`)
- **App下载** (`modules/jintiku/pages/h5Active/app-upload`)
- **App打开** (`modules/jintiku/pages/h5Active/open-app`)

## 分包页面

### 1. 真题闯关分包 (`modules/jintiku/pages/questionChallenge`)

#### 页面列表
- **闯关首页** (`index`): 真题闯关主页
- **闯关历史** (`history`): 闯关记录查看
- **关卡列表** (`levelList`): 关卡选择页面
- **闯关进行** (`go`): 闯关答题页面
- **闯关练习** (`practise`): 练习模式
- **闯关记录** (`record`): 详细记录页面
- **闯关报告** (`report`): 闯关结果报告

#### 特色功能
- 游戏化闯关体验
- 关卡进度管理
- 成绩统计分析

### 2. 智能测评分包 (`modules/jintiku/pages/intelligentEvaluation`)

#### 页面列表
- **测评首页** (`index`): 智能测评入口
- **测评练习** (`practise`): 测评答题页面
- **测评报告** (`report`): 智能分析报告
- **今日刷题报告** (`planQuestionReport`): 每日刷题统计
- **刷题计划** (`questionPlan`): 学习计划制定
- **计划报告** (`scheduleReport`): 计划执行报告
- **排行榜** (`ranking`): 用户排名

#### 特色功能
- AI 智能测评
- 个性化学习计划
- 学习数据分析
- 社交排行功能

### 3. 考点词条分包 (`modules/jintiku/pages/examEntry`)

#### 页面列表
- **词条首页** (`index`): 考点词条入口
- **考点口诀** (`examKnack`): 记忆口诀
- **词条详情** (`entry`): 词条内容页面
- **考点收藏** (`examEntryCollect`): 收藏的考点

#### 特色功能
- 知识点梳理
- 记忆口诀辅助
- 考点收藏管理

### 4. 模考大赛分包 (`modules/jintiku/pages/modelExaminationCompetition`)

#### 页面列表
- **模考列表** (`index`): 全部模考展示
- **考试信息** (`examInfo`): 套卷模考详情
- **模考说明** (`help`): 使用帮助

#### 特色功能
- 真实考试模拟
- 竞赛排名机制
- 详细成绩分析

### 5. 用户信息分包 (`modules/jintiku/pages/userInfo`)

#### 页面列表
- **报告中心** (`report`): 学习报告汇总
- **设置页面** (`set`): 应用设置
- **隐私条款** (`privacy`): 隐私政策
- **服务协议** (`userServiceAgreement`): 用户协议

### 6. 订单分包 (`modules/jintiku/pages/order`)

#### 页面列表
- **支付成功** (`paySuccess`): 支付结果页面

### 7. 测试分包 (`modules/jintiku/pages/test`)

#### 页面列表
- **商品详情** (`detail`): 商品信息展示
- **试卷详情** (`exam`): 试卷信息
- **成绩报告** (`examScoreReporting`): 考试成绩
- **我的订单** (`order`): 订单管理

### 8. 专题分包 (`modules/jintiku/pages/zhuanti`)

#### 页面列表
- **技能小黑屋** (`skillBlackRoom`): 专项技能训练

## 页面导航规则

### 路由跳转方式
- **uni.navigateTo**: 普通页面跳转
- **uni.redirectTo**: 重定向跳转
- **uni.switchTab**: TabBar 页面切换
- **uni.reLaunch**: 重启应用
- **uni.navigateBack**: 返回上一页

### 参数传递
- URL 参数传递
- 全局状态管理
- 本地存储传递

### 页面生命周期
- **onLoad**: 页面加载
- **onShow**: 页面显示
- **onReady**: 页面初次渲染完成
- **onHide**: 页面隐藏
- **onUnload**: 页面卸载
- **onPullDownRefresh**: 下拉刷新
- **onReachBottom**: 上拉加载
