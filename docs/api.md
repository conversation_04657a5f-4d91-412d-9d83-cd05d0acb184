# API 接口文档

## 接口概览

金题库项目的 API 接口分为全局接口和业务模块接口，采用统一的请求封装和错误处理机制。

## 请求配置

### 基础配置

```javascript
// 环境配置
const baseURL = {
  development: '/api',
  test: 'https://xingyuntest.jinyingjie.com/api',
  pre: 'https://xingyunpre.jinyingjie.com/api',
  production: 'https://erp.shunyixia.com/api'
}
```

### 请求拦截器

```javascript
// 请求头配置
const headers = {
  'Authorization': 'Basic ' + base64(key:value),
  'x-merchant-id': Base64.encode(merchantId),
  'x-brand-id': Base64.encode(brandId),
  'Content-Type': 'application/json'
}
```

### 响应状态码

- `100000`: 请求成功
- `100002`: 登录过期，需要重新登录
- `其他`: 业务错误码

## 全局接口 (`src/api`)

### 微信登录相关

#### 微信登录
```javascript
// 微信登录
export const wxLogin = data => {
  return http({
    url: '/Login/getWxLogin',
    data: data,
    method: 'GET'
  })
}
```

#### 生成小程序推广码
```javascript
export const getWxCode = data => {
  return http({
    url: '/WeChatApplet/appletQrCode',
    data: data,
    method: 'GET'
  })
}
```

#### 生成添加老师二维码
```javascript
export const getWeChatPromoterQrcode = data => {
  return http({
    url: '/WeChatApplet/getWeChatPromoterQrcode',
    data: data,
    method: 'GET'
  })
}
```

### 活动相关

#### 活动信息记录
```javascript
export const setActive = data => {
  return http({
    url: '/MiniApps/miniAppActiveInfoRecord',
    data: data,
    method: 'GET'
  })
}
```

## 金题库业务接口 (`src/modules/jintiku/api`)

### 1. 用户认证接口 (`index.js`)

#### 获取验证码
```javascript
export const getCode = data => {
  return http({
    url: '/c/sms/send',
    method: 'POST',
    data
  })
}
```

#### 用户登录
```javascript
export const Login = data => {
  return http({
    url: '/c/user/login',
    method: 'POST',
    data
  })
}
```

#### 短信登录
```javascript
export const smslogin = data => {
  return http({
    url: '/c/user/smslogin',
    method: 'POST',
    data
  })
}
```

#### 获取手机号
```javascript
export const getPhone = data => {
  return http({
    url: '/c/user/getphone',
    method: 'POST',
    data
  })
}
```

### 2. 题目相关接口 (`commen.js`)

#### 获取题目列表
```javascript
export const getQuestionsList = data => {
  return http({
    url: '/c/tiku/question/getquestionlist',
    method: 'GET',
    data
  })
}
```

#### 提交答案
```javascript
export const setAnswer = data => {
  return http({
    url: '/c/tiku/question/answer',
    method: 'POST',
    data
  })
}
```

#### 题目收藏
```javascript
export const collect = function (data = {}) {
  return http({
    url: '/c/tiku/question/practice/collect',
    method: 'GET',
    data
  })
}
```

#### 题目纠错
```javascript
export const correction = function (data = {}) {
  return http({
    url: '/c/tiku/question/correction',
    method: 'POST',
    header: {
      'Content-Type': 'application/json'
    },
    data
  })
}
```

#### 获取题型
```javascript
export const getQuestionType = function (data = {}) {
  return http({
    url: '/c/tiku/question/type',
    method: 'GET',
    data
  })
}
```

### 3. 章节练习接口 (`chapter.js`)

#### 获取章节列表
```javascript
export const getChapterlist = function (data = {}) {
  return http({
    url: '/c/tiku/chapter/list',
    method: 'GET',
    data
  })
}
```

#### 章节练习包商品树接口
```javascript
export const chapterpackageTree = function (data = {}) {
  return http({
    url: '/c/tiku/homepage/chapterpackage/tree',
    method: 'GET',
    data
  })
}
```

#### 成绩报告
```javascript
export const scorereporting = function (data = {}) {
  return http({
    url: '/c/tiku/servicehall/scorereporting',
    method: 'GET',
    data
  })
}
```

### 4. 考点词条接口 (`examEntry.js`)

#### 获取考点口诀列表
```javascript
export const getTestpointknacklist = function (data = {}) {
  return http({
    url: '/c/tiku/testingcentre/gettestpointknacklist',
    method: 'GET',
    data
  })
}
```

#### 考点词条二级列表
```javascript
export const getTestpointknackchildlist = function (data = {}) {
  return http({
    url: '/c/tiku/testingcentre/gettestpointknackchildlist',
    method: 'GET',
    data
  })
}
```

#### 考点词条信息
```javascript
export const getTestpointknackquestion = function (data = {}) {
  return http({
    url: '/c/tiku/testingcentre/gettestpointknackquestion',
    method: 'GET',
    data
  })
}
```

#### 收藏考点目录
```javascript
export const getTestpointcollectlist = function (data = {}) {
  return http({
    url: '/c/tiku/testingcentre/testingcentrecollectlist',
    method: 'GET',
    data
  })
}
```

### 5. 考试相关接口

#### 获取全部考试
```javascript
export const getAllExam = (data = {}) => {
  return http({
    url: '/c/tiku/mockexam/allexam',
    method: 'GET',
    data
  })
}
```

#### 获取模考详情
```javascript
export const getExaminfoDetail = (data = {}) => {
  return http({
    url: '/c/tiku/mockexam/examinfo',
    method: 'GET',
    data
  })
}
```

#### 考试交卷
```javascript
export const examSubmitAnswer = data => {
  return http({
    url: '/c/tiku/question/answer',
    method: 'POST',
    data
  })
}
```

### 6. 商品订单接口

#### 获取商品列表
```javascript
export const getGoods = (data = {}) => {
  return http({
    url: '/c/goods/list',
    method: 'GET',
    data
  })
}
```

#### 创建订单
```javascript
export const getOrderV2 = (data = {}) => {
  return http({
    url: '/c/order/v2',
    method: 'post',
    data,
    header: {
      'Content-Type': 'application/json'
    }
  })
}
```

#### 订单列表
```javascript
export const orderList = (data = {}) => {
  return http({
    url: '/c/order/my/list',
    method: 'GET',
    data
  })
}
```

#### 微信支付
```javascript
export const wechatapplet = (data = {}) => {
  return http({
    url: '/c/pay/wechatpay/jsapi',
    method: 'post',
    data,
    header: {
      'Content-Type': 'application/json'
    }
  })
}
```

### 7. 用户信息接口 (`userInfo.js`)

#### 获取用户信息
```javascript
export const getUserInfo = (data = {}) => {
  return http({
    url: '/c/user/info',
    method: 'GET',
    data
  })
}
```

#### 修改用户信息
```javascript
export const changeBasic = (data = {}) => {
  return http({
    url: '/c/user/changebasic',
    method: 'POST',
    data
  })
}
```

#### 获取学习数据
```javascript
export const getLearningData = (data = {}) => {
  return http({
    url: '/c/user/learningdata',
    method: 'GET',
    data
  })
}
```

## 请求封装使用

### 基础用法

```javascript
import { getQuestionsList } from '@/modules/jintiku/api/commen'

// 调用接口
const fetchQuestions = async () => {
  try {
    const res = await getQuestionsList({
      page: 1,
      limit: 20,
      type: 1
    })
    console.log(res.data)
  } catch (error) {
    console.error('请求失败:', error)
  }
}
```

### 带加载状态

```javascript
const fetchData = async () => {
  const res = await getQuestionsList({
    loadingtext: '加载中...',
    noloading: false // 是否显示loading
  })
}
```

### 错误处理

```javascript
// 全局错误处理已在请求拦截器中配置
// 特殊错误码处理
const handlerStatusCode = {
  100002: function (data, request) {
    // 登录过期处理
    store.state.token = ''
    uni.removeStorage('__xingyun_token__')
    goToLogin('handlerStatusCode')
  }
}
```

## 数据结构

### 通用响应格式

```javascript
{
  "code": 100000,
  "msg": "success",
  "data": {
    // 具体数据
  }
}
```

### 分页数据格式

```javascript
{
  "code": 100000,
  "msg": "success", 
  "data": {
    "list": [],
    "total": 100,
    "page": 1,
    "limit": 20
  }
}
```

### 题目数据格式

```javascript
{
  "id": "题目ID",
  "type": "题型",
  "stem_list": [{
    "content": "题目内容",
    "option": ["选项A", "选项B", "选项C", "选项D"],
    "answer": ["正确答案"],
    "selected": ["用户选择"],
    "multiple": false
  }],
  "parse": "解析内容",
  "knowledge_ids": ["知识点ID"],
  "level": "难度等级"
}
```

## 接口调试

### 开发环境代理配置

```javascript
// vue.config.js
module.exports = {
  devServer: {
    proxy: {
      '/api': {
        target: 'https://xingyundev.jinyingjie.com',
        changeOrigin: true,
        pathRewrite: {
          '^/api': '/api'
        }
      }
    }
  }
}
```

### Mock 数据

项目支持 Mock 数据，可在 `src/api/mock.js` 中配置测试数据。
